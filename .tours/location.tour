{"$schema": "https://aka.ms/codetour-schema", "title": "location", "steps": [{"file": "src/runtime-lib/modules/location-resolution/useLocationResolution.ts", "description": "# 此处的 setData(points.map(() => addressTitle));为同步执行，需要保留 颜豪-李媛元", "line": 36}, {"file": "src/runtime-pages/monitoring-center/components/AlarmList/Card.tsx", "description": "# 需要确定该组件中有没有做地址批量解析操作，如有需要一起去掉 颜豪-李媛元", "line": 663}], "buildDate": "2025-03-19", "name": "saas-web", "participants": ["颜豪", "李媛元"], "version": "2.16.4-P1"}