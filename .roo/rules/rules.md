# Roo Code 规则

## 记忆库（Memory Bank）

Roo Code 使用记忆库（Memory Bank）功能来维护项目的上下文信息和知识。详细的记忆库规则和工作流程请参考 [memory-bank.md](./.roo/rules/memory-bank.md)。

### 记忆库使用指南

1. 在每个任务开始时，Roo 必须阅读所有记忆库文件，以获取项目的完整上下文。
2. 记忆库文件存储在 `memory-bank/` 目录中，包括：
   - projectbrief.md（项目简介）
   - frontendCodeStandards.md（前端代码标准）
   - productContext.md（产品上下文）
   - activeContext.md（当前工作上下文）
   - systemPatterns.md（系统模式）
   - techContext.md（技术上下文）
   - progress.md（进度）

3. 当用户请求"更新记忆库"时，Roo 必须审查所有记忆库文件，并根据最新的项目状态进行更新。

4. 记忆库更新应在以下情况发生：
   - 发现新的项目模式时
   - 实施重大变更后
   - 当用户请求更新记忆库时
   - 当上下文需要澄清时

## 代码规范

1. 遵循项目的代码风格指南
2. 使用清晰的命名约定
3. 编写详细的注释和文档
4. 保持代码模块化和可重用
5. 进行适当的错误处理和验证

## 工作流程

1. 分析任务需求
2. 查阅记忆库获取上下文
3. 制定实施计划
4. 执行开发任务
5. 测试和验证
6. 更新记忆库
7. 提交完成的工作

## 沟通指南

1. 使用清晰、专业的语言
2. 提供详细的技术解释
3. 在必要时使用图表和示例
4. 主动提出建议和改进方案
5. 及时回应用户的问题和反馈