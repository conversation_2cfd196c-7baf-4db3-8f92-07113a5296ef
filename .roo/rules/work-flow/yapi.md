
# YApi自动化工作流配置

# 当检测到以下关键词时，自动调用yapi-server工具获取接口信息
KEYWORDS:
- yapi接口
- yapi api
- 接口文档
- 创建接口
- 添加接口
- 新增接口
- 接口定义
- yapi

# 自动化工作流程图
```mermaid
flowchart TD
    A[检测关键词] --> B{是否包含YApi关键词?}
    B -->|是| C[调用yapi-server工具获取接口信息]
    B -->|否| Z[结束流程]

    C --> D[处理接口信息\n不显示原始返回数据]
    D --> E[分析接口所属模块]

    E --> F{检查src/service目录\n是否存在对应模块文件?}
    F -->|存在| H[根据接口信息生成\nTypeScript接口函数]
    F -->|不存在| G[创建新的模块文件]

    G --> H
    H --> I[将生成的函数添加到\n对应模块文件中]
    I --> J[完成接口生成]

    subgraph 类型定义生成
    H1[创建请求参数类型] --> H2[创建响应结果类型]
    H2 --> H3[导出所有类型定义]
    end

    H --> 类型定义生成
```

# 接口函数生成规则
CODE_GENERATION:
- 使用项目统一的request函数，从 '@base-app/runtime-lib' 导入
- 定义并导出完整的TypeScript接口类型（包括请求参数和响应数据的类型定义）
- 添加接口描述注释（包括函数用途、参数说明和返回值说明）
- 实现标准的错误处理（检查 success 和 code 值，不成功则抛出 message）
- 遵循项目现有代码风格
- 使用以下模板处理请求：
  ```typescript
  const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
  const successCode = 200;
  
  export const functionName = async (params?: ParamType) => {
      const { success, code, message, data } = await request<Request.Response<ResultType>>({
          baseURL,
          method: 'post|get',
          url: '/api/path',
          data|params: params,
      });
      if (!success || code != successCode) {
          throw message;
      }
      return data;
  };
  ```
- 在 request 函数中通过泛型指定返回类型，如 `request<Request.Response<ResultType>>`
- 对于分页查询接口，使用 `Request.ResponsePageList<ItemType>` 作为返回类型

# 示例工作流
用户提到："我需要添加CICD Server项目, tag包含 V1.0.0的接口：
```mermaid
flowchart TD
    A[开始] --> B[调用get_follow_list获取关注的项目列表]
    B --> C[根据上下文确定相关项目]
    C --> D[调用get_interfaces_by_project_and_tag\n获取V1.0.0标签的接口描述数据]
    D --> E[分析接口所属模块\n确定为user模块]
    E --> F{检查src/service/user.ts\n是否存在?}
    F -->|存在| H[生成符合项目规范的接口函数]
    F -->|不存在| G[创建新的user.ts文件]
    G --> H

    subgraph 类型定义生成
    H1[创建请求参数类型定义] --> H2[创建响应结果类型定义]
    H2 --> H3[导出所有类型定义]
    end

    H --> 类型定义生成
    类型定义生成 --> I[将生成的函数和类型定义\n添加到user.ts文件中]
    I --> J[完成接口生成]
```

# 类型定义生成规则
TYPE_GENERATION:
- 根据YApi接口文档自动生成相关的TypeScript类型定义
- 为请求参数和响应结果创建接口
- 使用适当的类型名称（如 MonitorGroupParams, MonitorGroupItem 等）
- 包含所有必要的字段和类型
- 为每个字段添加 JSDoc 注释说明其用途
- 对于复杂类型，使用嵌套接口定义
- 示例：
  ```typescript
  export interface MonitorGroupItem {
      /** 监控组id */
      id: string;
      /** 监控组名称 */
      groupName: string;
      /** 创建时间（时间戳） */
      createTime: string;
      /** 修改时间（时间戳） */
      updateTime: string;
  }
  
  export type MonitorGroupParams = {
      /** 监控组名 监控组名称小于50个字符 */
      groupName: string;
      /** 监控组关联的车辆大于50个，则取新增列表的前50个车辆。 */
      vehicleList: VehicleListItem[];
  };
  ```

# 命名规范
NAMING_CONVENTIONS:
- 接口函数命名应遵循以下规则：
  - 添加/创建操作：使用 `add` 前缀，如 `addMonitorGroup`
  - 更新操作：使用 `update` 前缀，如 `updateMonitorGroup`
  - 删除操作：使用 `delete` 前缀，如 `deleteMonitorGroup`
  - 查询单个资源：使用 `fetch` 前缀，如 `fetchMonitorGroup`
  - 查询列表：使用 `fetchXXXList` 格式，如 `fetchMonitorGroupList`
  - 分页查询：使用 `fetchXXXPageList` 格式，如 `fetchMonitorGroupPageList`
- 类型命名规则：
  - 请求参数类型：使用 `XXXParams` 格式，如 `MonitorGroupParams`
  - 响应项类型：使用 `XXXItem` 格式，如 `MonitorGroupItem`
  - 列表响应类型：使用 `XXXListResult` 格式，如 `MonitorGroupListResult`

# 请求响应类型定义
REQUEST_TYPES:
- 所有接口函数必须使用 `src/types/request.d.ts` 中定义的类型
- 单个对象返回使用 `Request.Response<DataType>` 类型
- 分页列表返回使用 `Request.ResponsePageList<DataType>` 类型
- 示例：
  ```typescript
  // 单个对象返回
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    // 请求配置
  });
  
  // 分页列表返回
  const { success, code, message, data } = await request<Request.ResponsePageList<MonitorGroupItem>>({
    // 请求配置
  });
  ```
- Response 类型定义：
  ```typescript
  declare namespace Request {
    export interface Response<DataType> {
      success: boolean;
      message: Error;
      code: number;
      status?: 'SUCCESS';
      langKey: string;
      data: DataType;
      sid?: string;
      errorVar: string[];
    }
    export type ResponsePageList<DataType> = Response<{
      list: DataType[];
      total: number;
      page: number;
      pageSize: number;
      hasNextPage?: boolean;
    }>;
  }
  ```

# 模块划分规则
MODULE_RULES:
- 根据接口路径前缀确定模块（如/user/login属于user模块）
- 如果无法确定模块，根据接口功能描述推断
- 遵循项目现有的模块划分方式

# 分页查询接口处理规则
PAGINATION_RULES:
- 分页查询接口参数通常继承自 BaseQueryParams
- 分页查询接口返回类型使用 Request.ResponsePageList<ItemType>
- 示例：
  ```typescript
  /**
   * 监控组分页查询
   */
  export const fetchMonitorGroupPageList = async (params?: {
      groupName?: string;
  } & BaseQueryParams) => {
      const { success, code, message, data } = await request<
          Request.ResponsePageList<MonitorGroupItem>
      >({
          baseURL,
          method: 'get',
          url: '/base-business-service/api/v1/monitor/group/page',
          params,
      });
      if (!success || code != successCode) {
          throw message;
      }
      return data;
  };
  ```