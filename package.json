{"name": "@streamax/saas-web", "scope": "base", "baseApp": "@streamax/saas-web", "version": "2.16.9", "author": "quting", "scripts": {"start": "concurrently \"cross-env PORT=8006 umi dev\" \"node scripts/proxy-server.js 8006 20552 20553 20554\"", "build": "cross-env NODE_OPTIONS='--max-old-space-size=100000' BABEL_CACHE=true starry-runtime-build umi", "build:types": "starry-runtime-build ts", "generate-deploy-scripts": "generate-deploy-scripts", "postinstall": "umi generate tmp", "lint": "npm run lint:js && npm run lint:prettier", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check '**/*.{js,jsx,tsx,ts,less,md,json}'", "prettier": "prettier -c --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "sonar": "node .sonar.js", "extract:share": "page-sharing-tool extract -g", "pub:npm": "npm run build:types && starry-runtime-publish --npm"}, "gitHooks": {"pre-push": "sync-config-script"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/pro-card": "^1.18.12", "@ant-design/pro-table": "^2.61.6", "@ctrl/tinycolor": "3.6.1", "@microsoft/fetch-event-source": "^2.0.1", "@streamax/babel-plugin-add-sharing-props": "^1.0.4", "@streamax/base-core-lib": "1.2.1", "@streamax/echarts-manager": "1.0.0", "@streamax/emmiter": "1.0.1", "@streamax/hooks": "1.1.0", "@streamax/iframe-sdk": "1.0.1", "@streamax/less-plugin-utils": "1.0.0-alpha.9", "@streamax/logger": "2.0.3", "@streamax/material": "1.0.0-alpha.10", "@streamax/page-sharing-core": "^1.0.1", "@streamax/performance-lens": "1.0.0", "@streamax/poppy": "4.16.3-alpha.4", "@streamax/poppy-icons": "4.1.42", "@streamax/poppy-themes": "1.0.1-alpha.3", "@streamax/responsive-layout": "1.0.3", "@streamax/starry-components": "1.14.3-alpha.1", "@streamax/starry-runtime-build": "2.0.2", "@streamax/streamap-jsloader": "1.0.20", "@streamax/streamap-react": "1.1.29", "@streamax/umi-plugin-csp": "1.0.6", "@streamax/umi-plugin-google-analytics": "1.1.2", "@streamax/umi-plugin-service-worker": "1.0.1", "@streamax/umi-plugin-starry": "2.4.1-alpha.0", "@streamax/umi-plugin-starry-example": "2.0.2", "@streamax/umi-plugin-starry-loader": "2.0.1", "@streamax/umi-plugin-starry-runtime-config": "2.0.0", "@streamax/video-sdk": "3.0.8", "@types/dompurify": "^3.0.5", "@umijs/renderer-react": "3.5.8", "ahooks": "^3.7.8", "array-move": "3.0.1", "axios": "0.21.1", "braft-editor": "^2.3.9", "classnames": "^2.3.1", "comlink": "^4.4.1", "crypto-js": "4.1.1", "dompurify": "^3.1.6", "dva": "2.6.0-beta.22", "dva-immer": "1.0.0", "dva-loading": "3.0.22", "echarts": "5.3.1", "fast-sort": "^3.4.1", "fflate": "^0.8.2", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "flv.js": "1.5.0", "html2canvas": "^1.4.1", "i18next": "20.3.2", "jquery": "^3.6.0", "js-encrypt": "2.3.4", "js-md5": "^0.7.3", "jspdf": "^2.5.1", "lodash": "4.17.21", "lodash.uniqby": "^4.7.0", "moment": "2.29.1", "nanoid": "3.3.7", "protobufjs": "^7.4.0", "qrcode.react": "^3.1.0", "qs": "6.11.0", "querystring": "0.2.1", "rc-util": "5.37.0", "react": "17.0.2", "react-color": "^2.19.3", "react-dom": "17.0.2", "react-draggable": "^4.4.3", "react-html-parser": "^2.0.2", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "8.0.7", "react-reflex": "^4.0.9", "react-resizable": "^3.0.5", "react-sortable-hoc": "^2.0.0", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "4.4.5", "react-virtualized": "^9.22.3", "remark-gfm": "3.0.1", "rfdc": "^1.4.1", "safe-stable-stringify": "^2.5.0", "swiper": "10.3.1", "tiny-worker": "^2.3.0", "umi": "3.x", "vconsole": "^3.15.1", "xss": "^1.0.15", "zustand": "4.5.5"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@streamax/babel-plugin-add-sharing-props": "^1.0.4", "@streamax/deploy-scripts": "3.2.1-alpha.0", "@streamax/less-plugin-dynamic-variable": "1.0.1", "@streamax/less-plugin-syntax-extend": "1.0.2", "@streamax/lint": "1.1.0-alpha.3", "@streamax/page-sharing-tool": "2.0.0", "@streamax/sync-config-script": "^1.0.0", "@streamax/umi-plugin-page-sharing": "^1.0.0", "@streamax/umi-plugin-starry-style-scope": "^1.2.0", "@streamax/umi-preset-base": "3.1.0", "@types/leaflet": "1.7.11", "@types/lodash": "^4.17.18", "@types/qs": "6.9.7", "@types/react": "17.0.2", "@types/react-dom": "17.0.2", "@types/react-resizable": "^3.0.7", "@types/react-transition-group": "4.4.11", "@types/react-virtualized": "^9.21.20", "@types/redux": "^3.6.0", "@typescript-eslint/eslint-plugin": "^5.39.0", "@umijs/test": "^3.2.28", "concurrently": "^8.2.2", "copy-webpack-plugin": "^11.0.0", "eslint": "^7.26.0", "lint-staged": "13.0.4", "postcss-prefix-selector": "^1.16.1", "prettier": "^2.3.0", "sonarqube-scanner": "2.8.1", "typescript": "^4.2.3", "worker-loader": "3.0.8", "yorkie": "^2.0.0"}, "resolutions": {"@streamax/streamap-js": "1.1.49", "coa": "2.0.2", "@geoman-io/leaflet-geoman-free": "2.11.3-3", "pixi.js": "6.0.4", "node-downloader-helper": "1.0.19", "prettier-plugin-packagejson": "2.2.18", "rc-tree-select": "5.1.2", "@pixi/filter-displacement": "6.3.2", "@pixi/text": "6.3.2", "stackframe": "1.2.1", "caniuse-lite": "1.0.30001583", "@playwright/test": "1.29.1", "cosmiconfig-typescript-loader": "4.3.0", "core-js-compat": "3.30.0", "@types/react": "17.0.44", "commander": "9.4.1", "antd": "4.18.0", "@babel/helper-create-regexp-features-plugin": "7.25.9", "cheerio": "1.0.0-rc.12", "rc-virtual-list": "3.14.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}