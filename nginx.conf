# 占位符请勿修改
server {
    # 端口占位符
    listen __PORT__;
    # gzip config
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # CSP占位符
    __CSP__
    server_tokens   off;
    add_header   Cache-Control  'max-age=0, must-revalidate';
    # root占位符
    root __ROOT__;
    location / {
        try_files $uri $uri/ /index.html;
    }
}

__EXTEND_CONFIG__