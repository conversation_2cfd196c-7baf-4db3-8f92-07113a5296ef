service-dependencies:
  - nginx
  - assets-engine
cluster-variables:
  - name: web.public.port
    type: port
    port-type: listen
    desc: 系统监听端口
    default: 20550

  - name: static.intranet.addrs
    type: addrs
    desc: 静态资源服务集群内网地址
    default: multi_addrs(assets-engine,engine_port)

  - name: static.public.url
    type: string
    desc: 静态资源服务公网地址(https?://ip:port)
    default: https://static.streamax.com:20602
  
  - name: gateway.public.url
    type: string
    desc: 网关服务公网地址(https?://ip:port)
    default: https://ftcloud.streamax.com:20501

  - name: gateway.websocket.url
    type: string
    desc: 网关服务websocket公网地址(https?://ip:port)
    default: wss://ftcloud.streamax.com:20998

  - name: gateway.websocket.port
    type: port
    port-type: "other"
    desc: 网关服务websocket端口
    default: 20998

  - name: login.web.public.url
    type: string
    desc: 登录web的公网地址(https?://ip:port)
    default: https://login.streamax.com:20604/base/login/pc/login
    
  - name: mobile.web.public.url
    type: string
    desc: 移动端页面公网地址(https?://ip:port)
    default: https://mobile.streamax.com:20608

  - name: saas.web.url
    type: string
    desc: 统一服务入口跳转页面的基础地址
    default: https://dev-saas-280-8.streamax.com:20551

  - name: runtime.pages
    type: string
    desc: 行业页面集合名称
    default: base

  - name: s17.server.h5.url
    type: string
    desc: s17 h5请求路径
    default: saas.streamax.com
    
  - name: s17.server.url
    type: string
    desc: s17server域名
    default: saas.streamax.com

  - name: s17.server.port
    type: port
    port-type: "other"
    desc: s17server  端口
    default: 20500

  - name: s17.server.h5.port
    type: port
    port-type: "other"
    desc: s17server H5 端口
    default: 20501
    
  - name: videoplayer.url
    type: string
    desc: video控件下载地址
    default: /static-file

  - name: disktool.url
    type: string
    desc: 磁盘工具下载地址
    default: saas.streamax.com

  - name: videoplayer.version
    type: string
    desc: video控件版本
    default: 1.3.4.7

  - name: videoplayer.protocol
    type: string
    desc: 视频控件请求协议
    default: http
    
  - name: download.apps
    type: string
    desc: 下载中心展示的app
    default: DiskTool.exe
    
  - name: humanfactors.appId
    type: string
    desc: 人因系统的app
    default: "1"

  - name: seek.timeout
    type: string
    desc: seek超时时间
    default: "30"

  - name: video.buffer.time
    type: string
    desc: 证据回放的buffer缓冲时间单位s
    default: "0.5"

  - name: realtime.monitoring.time
    type: string
    desc: 实时监控的处理时间间隔单位s
    default: "1"  

  - name: password.rsa2048.public.key
    type: string
    desc: 密码加密公钥
    default: -----BEGIN%20PUBLIC%20KEY-----%0AMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0VIjjNNY93vCmjuGqTteFHdt5rDFtca5%0A6v9oTFXormfQRTgjPtsr5tzCvpThcC8eY4UgeJfQT3zGcDnaKAONJQKHD2Q2w12RraazY0WDZcro%0AoqoLGx8hImpuGLOxc9ttXFDftDskrmcKNb8c20d9tB1v8qVE%2FpwKQF%2F8dfzI7sbgGkSZkCfuXHJN%0AtDRt5%2B97i6scaCMtwma3N7dy3bPg6GYlqGPrzcBdYjiaFfHUK82pEKSo7xK8%2Fe80wtUvwjuRSMva%0AD%2BPcX%2FnoSdiGO4oN3JqPioGnwv6DjntktbudJ9iayK0sWl%2FDZ%2Fqf90GQwcpH6Snigo8LymZOFD4%2F%0A4BaFgwIDAQAB%0A-----END%20PUBLIC%20KEY-----    
  
  - name: streamap.server.url
    type: string
    desc: 地图服务地址
    default: https://map.streamax.com/streamap-service

  - name: streamap.loader.server.url
    type: string
    desc: 地图动态版本管理库服务地址
    default: https://dl-map.streamax.com/upload/starry-map-libs

  - name: use.lr.components
    type: string
    desc: 控制是否初始化LR组件
    default: "1"

  - name: h5.player.history.cache.size
    type: string
    desc: 指定播放完成的历史缓存数据最大缓存量
    default: "1024"

  - name: h5.player.future.cache.time
    type: string
    desc: 设置当前播放时间点的未来未播放视频数据的缓冲时间
    default: "60"

  - name: h5.player.start.loading.timeout 
    type: string
    desc: 开始展示Loading百分比的时间间隔
    default: "2"

  - name: google.analytics.measure.id
    type: string
    desc: google分析衡量id
    default: "0"

  - name: content.security.policy
    type: string
    desc: 是否开启CSP安全策略
    default: "0"
  
  - name: videoplayer.perpage.channels.number
    type: string
    desc: 控制多模式监控通道数量展示
    default: "9"

  - name: system.component.style
    type: string
    desc: 海外风格
    default: "default"

  - name: realtime.monitoring.update.frequency
    type: string
    desc: 实时监控的处理时间间隔单位ms,默认500,计算规则:(车辆数/10000)*500
    default: "500"  
  
  - name: interface.exception.capture
    type: string
    desc: 接口异常捕获0关闭1开启
    default: "0"

  - name: page.iframe.render.ports
    type: string
    desc: iframe渲染模式代理端口号列表,不启用则配置为"",多个端口时使用逗号分隔
    default: "20552,20553,20554"
  
  - name: page.iframe.https
    type: string
    desc: iframe渲染模式是否启用https
    default: "1"
