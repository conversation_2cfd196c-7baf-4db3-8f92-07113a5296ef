{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"], "@base-app/runtime-lib": ["src/runtime-lib"], "@base-app/runtime-entry": ["src/runtime-entry"], "@base-app/runtime-pages": ["src/runtime-pages"], "@base-app/runtime-lib/core": ["./node_modules/@streamax/base-core-lib"], "@base-app/runtime-lib/components/*": ["src/runtime-lib/components/*"]}, "allowSyntheticDefaultImports": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts"]}