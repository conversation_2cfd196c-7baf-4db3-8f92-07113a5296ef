apiVersion: "v1"
kind: "Component"
level: "base"
name: "saas-web"
metadata:
  key1: "value1"
spec:
  dependencies_middleware_component:
    - name: "nginx"
      version: "V17.239.58"
  template_files:
    - "config.ini.j2"
    - "config.runtime.js.template.j2"
  cluster_variables:
    - name: static_intranet_addrs
      desc: 静态资源服务集群内网地址
      value: "env(assets-engine,addresses)"

    - name: static_public_url
      desc: 静态资源服务公网地址(https?://ip:port)
      value: "default(static_public_url, /asset-engine)"

    - name: gateway_public_url
      desc: 网关服务公网地址(https?://ip:port)
      value: "default(gateway_public_url, /gateway)"

    - name: gateway_websocket_port
      desc: websocket端口
      value: "default(gateway_websocket_port, 20998)"

    - name: web_public_port
      desc: 系统监听端口
      value: "default(web_public_port, 20550)"

    - name: login_web_public_url
      desc: 登录web的公网地址(https?://ip:port)
      value: "default(login_web_public_url, /login/pc/login)"

    - name: mobile_web_public_url
      desc: 移动端页面公网地址(https?://ip:port)
      value: "default(mobile_web_public_url, /mobile)"

    - name: gateway_websocket_url
      desc: 网关服务websocket公网地址(https?://ip:port)
      value: "default(gateway_websocket_url, /gateway)"

    - name: runtime_pages
      desc: 行业页面集合名称
      value: "default(runtime_pages, base)"
      
    - name: s17_server_url
      desc: s17server域名
      value: "default(s17_server_url, /)"

    - name: s17_server_port
      desc: s17server  端口
      value: "default(s17_server_port, 20500)"

    - name: s17_server_h5_port
      desc: s17server H5 端口
      value: "default(s17_server_h5_port, 20551)"

    - name: s17_server_h5_url
      desc: s17 h5请求路径
      value: "default(s17_server_h5_url, /gateway)"

    - name: videoplayer_version
      desc: videoplayer的版本
      value: "default(videoplayer_version, *******)"

    - name: disktool_url
      desc: 磁盘工具下载地址
      value: "default(disktool_url, saas.streamax.com)"

    - name: videoplayer_url
      desc: 下载videoplayer的链接
      value: "default(videoplayer_url,/static-file)"

    - name: videoplayer_protocol
      desc: 视频控件请求协议
      value: "default(videoplayer_protocol, http)"
      
    - name: download_apps
      desc: 下载的app
      value: 'default(download_apps,"DiskTool_v1.1.0.21.exe,LocalPlayer_V1.2.5.0.exe")'
    
    - name: humanfactors_appId
      desc: 人因系统的app
      value: 'default(humanfactors_appId, 1)'

    - name: password_rsa2048_public_key
      desc: rsa密钥
      value: "default(password_rsa2048_public_key, -----BEGIN%20PUBLIC%20KEY-----%0AMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0VIjjNNY93vCmjuGqTteFHdt5rDFtca5%0A6v9oTFXormfQRTgjPtsr5tzCvpThcC8eY4UgeJfQT3zGcDnaKAONJQKHD2Q2w12RraazY0WDZcro%0AoqoLGx8hImpuGLOxc9ttXFDftDskrmcKNb8c20d9tB1v8qVE%2FpwKQF%2F8dfzI7sbgGkSZkCfuXHJN%0AtDRt5%2B97i6scaCMtwma3N7dy3bPg6GYlqGPrzcBdYjiaFfHUK82pEKSo7xK8%2Fe80wtUvwjuRSMva%0AD%2BPcX%2FnoSdiGO4oN3JqPioGnwv6DjntktbudJ9iayK0sWl%2FDZ%2Fqf90GQwcpH6Snigo8LymZOFD4%2F%0A4BaFgwIDAQAB%0A-----END%20PUBLIC%20KEY-----)"

    - name: streamap_server_url
      desc: 地图服务地址
      value: "default(streamap_server_url, https://map.streamax.com/streamap-service)"

    - name: streamap_loader_server_url
      desc: 地图动态版本管理库服务地址
      value: "default(streamap_loader_server_url, https://dl-map.streamax.com/upload/starry-map-libs)"

    - name: saas_web_url
      desc: 统一服务入口跳转页面的基础地址
      value: "default(saas_web_url, https://dev-saas-280-8.streamax.com:20551)"

    - name: seek_timeout
      desc: seek超时时间
      value: default(seek_timeout, 30)

    - name: video_buffer_time
      desc: 证据回放的buffer缓冲时间单位s
      value: default(video_buffer_time, 0.5)

    - name: realtime_monitoring_time
      desc: 实时监控的处理时间间隔单位s
      value: default(realtime_monitoring_time, 1)
    
    - name: use_lr_components
      desc: 控制是否初始化LR组件
      value: default(use_lr_components, 1)

    - name: h5_player_start_loading_timeout 
      desc: 开始展示Loading百分比的时间间隔
      value: default(h5_player_start_loading_timeout, 2)

    - name: h5_player_future_cache_time 
      desc: 设置当前播放时间点的未来未播放视频数据的缓冲时间
      value: default(h5_player_future_cache_time, 60)
    
    - name: h5_player_history_cache_size
      desc: 指定播放完成的历史缓存数据最大缓存量
      value: default(h5_player_history_cache_size ,1024)

    - name: google_analytics_measure_id
      desc: google分析衡量id
      value: default(google_analytics_measure_id , '0')

    - name: content_security_policy
      desc: 是否开启CSP安全策略
      value: default(content_security_policy , '0')
      
    - name: videoplayer_perpage_channels_number
      desc: 控制多模式监控通道数量展示
      value: default(videoplayer_perpage_channels_number, 9)

    - name: system_component_style
      desc: 海外风格展示
      value: "default(system_component_style, default)"

    - name: realtime_monitoring_update_frequency
      desc: 实时监控的处理时间间隔单位ms,默认500,计算规则:(车辆数/10000)*500
      value: default(realtime_monitoring_update_frequency, 500)

    - name: interface_exception_capture
      desc: 接口异常捕获是否记录日志 0-关闭 1-开启 默认关闭
      value: default(interface_exception_capture, 0)

    - name: page_iframe_render_ports
      desc: iframe渲染模式代理端口号列表,不启用则配置为"",多个端口时使用逗号分隔
      value: 'default(page_iframe_render_ports, "20552,20553,20554")'
    
    - name: page_iframe_https
      desc: iframe渲染模式是否启用https
      value: default(page_iframe_https, '1')