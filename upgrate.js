const fs = require('fs');
const path = require('path');

console.log('==============前端架构升级数据迁移 开始==============');

const component = 'fe-web';

const home = '/home/<USER>/';
const rootPath = path.resolve(home, '.assets-engine__store');
const oldFilesPath = path.resolve(rootPath, 'module');
const newFilesPath = path.resolve(rootPath, component);

const migrateList = [];

function copyDir(sourceDir, targetDir) {
    if (!fs.existsSync(sourceDir)) return;
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir);
    }
    const files = fs.readdirSync(sourceDir);
    files.forEach((file) => {
        const sourceFilePath = path.join(sourceDir, file);
        const targetFilePath = path.join(targetDir, file);
        const stat = fs.statSync(sourceFilePath);
        if (stat.isDirectory()) {
            if (!fs.existsSync(targetFilePath)) {
                fs.mkdirSync(targetFilePath);
            }
            copyDir(sourceFilePath, targetFilePath);
        } else {
            // 检查文件是否已存在于目标目录
            if (fs.existsSync(targetFilePath)) {
                return;
            }
            // 读取并写入文件到目标目录
            fs.copyFileSync(sourceFilePath, targetFilePath);
        }
    });
}

function syncReleaseJS(placeholder, jsPath, jsonPath) {
    let content = {};
    if (fs.existsSync(jsonPath)) {
        content = require(jsonPath);
    }
    const tpl = `window.${placeholder}={releaseObject}`.replace(
        '{releaseObject}',
        JSON.stringify(content),
    );
    fs.writeFileSync(jsPath, tpl);
}

// 迁移运行时页面资源
function migrateRuntimeFiles() {
    const oldModuleVersionJsonFile = path.resolve(
        rootPath,
        'module-version.json',
    );
    let oldModuleVersion = {};
    if (fs.existsSync(oldModuleVersionJsonFile)) {
        oldModuleVersion = require(oldModuleVersionJsonFile);
    }

    const newModuleVersionJsonFile = path.resolve(
        newFilesPath,
        'module-version.json',
    );
    let newModuleVersion = {};
    if (fs.existsSync(newModuleVersionJsonFile)) {
        newModuleVersion = require(newModuleVersionJsonFile);
    }

    const excludeList = [
        'runtime-starry-core',
        'runtime-starry-base',
        'runtime-starry-pages',
        'runtime-starry-components',
        'streamax-player-version-manager',
    ];
    Object.keys(oldModuleVersion).forEach((key) => {
        if (!excludeList.includes(key) && !newModuleVersion[key]) {
            const sourceDir = path.resolve(oldFilesPath, key);
            const targetDir = path.resolve(newFilesPath, 'module', key);
            console.log(`\x1b[32m迁移运行时页面: ${key}\x1b[0m`);
            copyDir(sourceDir, targetDir);
            newModuleVersion[key] = oldModuleVersion[key];
            migrateList.push(key);
        }
    });
    if (migrateList.length) {
        fs.writeFileSync(
            newModuleVersionJsonFile,
            JSON.stringify(newModuleVersion),
        );
    }
}

// 迁移运行时页面的配置
function migrateRuntimeConfig() {
    const oldRuntimeConfigJsonFile = path.resolve(
        rootPath,
        'runtime-config.json',
    );
    let oldRuntimeConfig = {};
    if (fs.existsSync(oldRuntimeConfigJsonFile)) {
        oldRuntimeConfig = require(oldRuntimeConfigJsonFile);
    }

    const newRuntimeConfigJsonFile = path.resolve(
        newFilesPath,
        'runtime-config.json',
    );
    let newRuntimeConfig = {};
    if (fs.existsSync(newRuntimeConfigJsonFile)) {
        newRuntimeConfig = require(newRuntimeConfigJsonFile);
    }
    migrateList.forEach((key) => {
        newRuntimeConfig[key] = oldRuntimeConfig[key];
    });
    fs.writeFileSync(
        newRuntimeConfigJsonFile,
        JSON.stringify(newRuntimeConfig),
    );
}

migrateRuntimeFiles();
console.log('\x1b[32m迁移运行时页面文件完成\x1b[0m');

migrateRuntimeConfig();
console.log('\x1b[32m迁移运行时页面配置完成\x1b[0m');

syncReleaseJS(
    'MODULE_VERSION_INFO',
    path.resolve(newFilesPath, 'module-version.js'),
    path.resolve(newFilesPath, 'module-version.json'),
);
console.log('\x1b[32m同步版本记录完成\x1b[0m');

syncReleaseJS(
    'RUNTIME_CONFIG_TEMP_INFO',
    path.resolve(newFilesPath, 'runtime-config.js'),
    path.resolve(newFilesPath, 'runtime-config.json'),
);
console.log('\x1b[32m同步运行时配置完成\x1b[0m');

console.log('==============前端架构升级数据迁移 完成==============');