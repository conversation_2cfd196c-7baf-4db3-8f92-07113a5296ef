#!/bin/bash

. `dirname $0`/base_env.sh

# 获取配置值并去除可能存在的引号
ports=$(get_config_value 'page.iframe.render.ports' | tr -d "'")
https=$(get_config_value 'page.iframe.https' | tr -d "'")

# 打印调试信息
echo "ports value: '${ports}'"

NGINX_CONF="${RUN_DIR}/nginx.conf"
EXTEND_TPL="${RUN_DIR}/extend.conf.tpl"

# 检查配置值是否为空、"-"或"''"
if [ "$ports" = "" ] || [ -z "$ports" ] || [ "$ports" = "''" ]; then
    echo "不启动iframe端口代理"
    # 直接将__EXTEND_CONFIG__替换为空
    # 确保保留最后的换行符
    sed -i 's/__EXTEND_CONFIG__//' "$NGINX_CONF"
else
    echo "启用iframe端口代理"
    # 创建临时文件存储生成的配置
    tmp_conf=$(mktemp)
    
    # 使用IFS分割端口字符串
    IFS=',' read -ra port_array <<< "$ports"
    
    # 生成nginx端口配置
    for port in "${port_array[@]}"; do
        # 去除可能的空格
        port=$(echo "$port" | tr -d ' ')
        if [ "$https" = "1" ]; then
            echo "    listen $port ssl http2;" >> "$tmp_conf"
        else
            echo "    listen $port;" >> "$tmp_conf"
        fi
    done

    # 根据https值确定协议
    if [ "$https" = "1" ]; then
        protocol="https"
    else
        protocol="http"
    fi

    # 替换extend.conf.tpl中的占位符
    awk -v r="$(cat $tmp_conf)" -v p="$protocol" '
    {
        gsub(/__LISTEN_PORTS__/, r)
        gsub(/__PROXY_PROTOCOL__/, p)
        print
    }' "$EXTEND_TPL" > "${RUN_DIR}/extend.conf"
    
    # 获取__EXTEND_CONFIG__的行号
    line_num=$(grep -n "__EXTEND_CONFIG__" "$NGINX_CONF" | cut -d: -f1)
    
    if [ -n "$line_num" ]; then
        # 创建临时文件
        tmp_nginx=$(mktemp)
        
        # 提取__EXTEND_CONFIG__之前的内容
        head -n $((line_num - 1)) "$NGINX_CONF" > "$tmp_nginx"
        
        # 添加extend.conf的内容
        cat "${RUN_DIR}/extend.conf" >> "$tmp_nginx"
        
        # 添加一个换行符
        echo "" >> "$tmp_nginx"
        
        # 更新nginx配置
        mv "$tmp_nginx" "$NGINX_CONF"
    else
        echo "警告: 未找到__EXTEND_CONFIG__占位符"
    fi
    
    # 设置文件权限
    if [ -n "$RUN_DIR" ]; then
        chown -R streamax:streamax "${RUN_DIR}"
    else
        echo "警告: RUN_DIR 未定义"
    fi
    
    # 清理临时文件
    rm -f "$tmp_conf"
    if [ -f "${RUN_DIR}/extend.conf" ]; then
        rm -f "${RUN_DIR}/extend.conf"
    fi
fi
