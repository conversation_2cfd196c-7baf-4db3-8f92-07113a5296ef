[{"resourceName": "用户管理", "resourceCode": "@base:@page:user.manage", "resourceUrl": "/base/user-manage", "actions": [{"resourceName": "新增", "resourceCode": "@base:@page:user.manage@action:add", "pageCode": "@base:@page:user.manage.add", "registerApps": [10001]}, {"resourceName": "编辑", "resourceCode": "@base:@page:user.manage@action:edit", "pageCode": "@base:@page:user.manage.edit", "registerApps": [10001]}, {"resourceName": "查看详情", "resourceCode": "@base:@page:user.manage@action:detail", "pageCode": "@base:@page:user.manage.detail", "registerApps": [10001]}, {"resourceName": "删除", "resourceCode": "@base:@page:user.manage@action:delete", "registerApps": [10001]}, {"resourceName": "批量删除", "resourceCode": "@base:@page:user.manage@action:delete.batch", "registerApps": [10001]}, {"resourceName": "启用", "resourceCode": "@base:@page:user.manage@action:enable", "registerApps": [10001]}, {"resourceName": "停用", "resourceCode": "@base:@page:user.manage@action:disable", "registerApps": [10001]}, {"resourceName": "批量停用", "resourceCode": "@base:@page:user.manage@action:disable.batch", "registerApps": [10001]}]}, {"resourceName": "新增用户", "resourceCode": "@base:@page:user.manage.add", "resourceUrl": "/base/user-manage/add"}, {"resourceName": "编辑用户", "resourceCode": "@base:@page:user.manage.edit", "resourceUrl": "/base/user-manage/edit"}, {"resourceName": "用户详情", "resourceCode": "@base:@page:user.manage.detail", "resourceUrl": "/base/user-manage/user-detail", "registerApps": [10001], "actions": [{"resourceName": "启用", "resourceCode": "@base:@page:user.manage.detail@action:enable", "registerApps": [10001]}, {"resourceName": "停用", "resourceCode": "@base:@page:user.manage.detail@action:disable", "registerApps": [10001]}, {"resourceName": "删除", "resourceCode": "@base:@page:user.manage.detail@action:delete", "registerApps": [10001]}], "childs": [{"resourceName": "用户信息", "resourceCode": "@base:@page:user.manage.detail.child.info", "registerApps": [10001], "actions": [{"resourceName": "编辑", "resourceCode": "@base:@page:user.manage.detail@action:edit", "registerApps": [10001]}]}, {"resourceName": "角色权限", "resourceCode": "@base:@page:user.manage.detail.child.role.auth", "registerApps": [10001], "actions": [{"resourceName": "角色授权", "resourceCode": "@base:@page:user.manage.detail@action:role.auth", "pageCode": "@base:@page:role-authorize", "registerApps": [10001]}, {"resourceName": "删除", "resourceCode": "@base:@page:user.manage.detail.child.role.auth.delete", "registerApps": [10001]}, {"resourceName": "查看角色", "resourceCode": "@base:@page:user.manage.detail@action:view.role", "registerApps": [10001]}]}, {"resourceName": "数据权限", "resourceCode": "@base:@page:user.manage.detail.child.data", "registerApps": [10001], "childs": [{"resourceName": "车队", "resourceCode": "@base:@page:user.manage.detail.child.data.group", "registerApps": [10001], "actions": [{"resourceName": "添加包含车队", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:add.include.group"}, {"resourceName": "编辑包含车队", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:edit.include.group"}, {"resourceName": "删除包含车队", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:delete.include.group"}, {"resourceName": "添加剔除车队", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:add.exclude.group"}, {"resourceName": "编辑剔除车组", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:edit.exclude.group"}, {"resourceName": "删除剔除车组", "resourceCode": "@base:@page:user.manage.detail.child.data.group@action:delete.exclude.group"}]}, {"resourceName": "车辆", "resourceCode": "@base:@page:user.manage.detail.child.data.vehicle", "registerApps": [10001], "actions": [{"resourceName": "添加包含车辆", "resourceCode": "@base:@page:user.manage.detail.child.data.vehicle@action:add.include.vehicle"}, {"resourceName": "删除包含车辆", "resourceCode": "@base:@page:user.manage.detail.child.data.vehicle@action:delete.include.vehicle"}, {"resourceName": "添加剔除车辆", "resourceCode": "@base:@page:user.manage.detail.child.data.vehicle@action:add.exclude.vehicle"}, {"resourceName": "删除剔除车辆", "resourceCode": "@base:@page:user.manage.detail.child.data.vehicle@action:delete.exclude.vehicle"}]}, {"resourceName": "司机", "resourceCode": "@base:@page:user.manage.detail.child.data.driver"}]}]}]