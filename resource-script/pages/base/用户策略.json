[{"resourceName": "用户策略", "resourceCode": "@base:@page:policy.manage.user", "resourceUrl": "/base/policy/user", "actions": [{"resourceName": "用户策略详情", "pageCode": "@base:@page:policy.manage.user.detail", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:detail"}, {"resourceName": "新增用户策略", "pageCode": "@base:@page:policy.manage.user.add", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:add"}, {"resourceName": "编辑用户策略", "pageCode": "@base:@page:policy.manage.user.edit", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:edit"}, {"resourceName": "删除用户策略", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:delete"}]}, {"resourceName": "用户策略详情", "resourceCode": "@base:@page:policy.manage.user.detail", "resourceUrl": "/base/policy/user/detail", "actions": [{"resourceName": "删除用户策略", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:delete"}], "childs": [{"resourceName": "策略信息", "resourceCode": "@base:@page:policy.manage.user.detail.child.info", "registerApps": [10001], "actions": [{"resourceName": "编辑用户策略", "pageCode": "@base:@page:policy.manage.user.edit", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:edit"}]}, {"resourceName": "适用范围", "resourceCode": "@base:@page:policy.manage.user.detail.child.scope", "registerApps": [10001], "actions": [{"resourceName": "用户授权", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:authorize"}, {"resourceName": "删除授权", "registerApps": [10001], "resourceCode": "@base:@page:policy.manage.user@action:authorize.delete"}]}]}, {"resourceName": "新增用户策略", "resourceCode": "@base:@page:policy.manage.user.add", "resourceUrl": "/base/policy/user/add"}, {"resourceName": "编辑用户策略", "resourceCode": "@base:@page:policy.manage.user.edit", "resourceUrl": "/base/policy/user/edit"}]