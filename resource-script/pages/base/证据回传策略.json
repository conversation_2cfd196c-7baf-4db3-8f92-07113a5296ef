[{"resourceName": "证据回传策略", "resourceCode": "@base:@page:strategy.evidence", "resourceUrl": "/base/strategy/evidence", "actions": [{"resourceName": "新增", "resourceCode": "@base:@page:strategy.evidence@action:add", "pageCode": "@base:@page:strategy.evidence.add", "registerApps": [10001]}, {"resourceName": "编辑", "resourceCode": "@base:@page:strategy.evidence@action:edit", "pageCode": "@base:@page:strategy.evidence.edit", "registerApps": [10001]}, {"resourceName": "复制", "resourceCode": "@base:@page:strategy.evidence@action:copy", "pageCode": "@base:@page:strategy.evidence.add", "registerApps": [10001]}, {"resourceName": "删除", "resourceCode": "@base:@page:strategy.evidence@action:delete", "registerApps": [10001]}, {"resourceName": "详情", "resourceCode": "@base:@page:strategy.evidence@action:detail", "pageCode": "@base:@page:strategy.evidence.detail", "registerApps": [10001]}]}, {"resourceName": "策略详情", "resourceCode": "@base:@page:strategy.evidence.detail", "resourceUrl": "/base/strategy/evidence/detail", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:strategy.evidence.detail@action:delete", "registerApps": [10001]}], "childs": [{"resourceName": "策略信息", "resourceCode": "@base:@page:strategy.evidence.detail.child.info", "registerApps": [10001], "actions": [{"resourceName": "编辑", "resourceCode": "@base:@page:strategy.evidence.detail.child.info@action:edit", "registerApps": [10001]}]}, {"resourceName": "回传策略", "resourceCode": "@base:@page:strategy.evidence.detail.child.return", "registerApps": [10001], "actions": [{"resourceName": "编辑", "resourceCode": "@base:@page:strategy.evidence.detail.child.return@action:edit", "registerApps": [10001]}]}, {"resourceName": "适用范围", "resourceCode": "@base:@page:strategy.evidence.detail.child.scope", "registerApps": [10001], "actions": [{"resourceName": "添加", "resourceCode": "@base:@page:strategy.evidence.detail.child.scope@action:edit", "registerApps": [10001]}, {"resourceName": "删除", "resourceCode": "@base:@page:strategy.evidence.detail.child.scope@action:delete", "registerApps": [10001]}]}]}, {"resourceName": "新增策略", "resourceCode": "@base:@page:strategy.evidence.add", "resourceUrl": "/base/strategy/evidence/add"}, {"resourceName": "编辑策略", "resourceCode": "@base:@page:strategy.evidence.edit", "resourceUrl": "/base/strategy/evidence/edit"}]