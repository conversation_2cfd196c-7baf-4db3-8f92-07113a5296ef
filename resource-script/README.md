1. base代表中台的页面
2. ftm代表ft-manager的页面
3. ftv代表ft-vision的页面

在对应的地方增加相关资源，其中页面可在文件夹中分模块创建文件

最后执行generate-sql.js 即可生成资源sql文件，把这个文件给后端就可以了。

注意点：
1. 货运复用了中台的页面，其中子页面和操作需要按货运需要的资源来，在对应的资源上增加
"registerApps": [10001]，不需要的就不加。
2. 目前很多操作都没在这里面，这份数据是不全的，而且代码里面也没做，最后需要统一根据货运产品提供的资源数据表来增加资源。
3. 现在每个详情页面的tab页也纳入了资源管控的范围，以子页面的形式存在，所以增加资源的时候需注意，具体可参考应用.json和租户.json
4. 子页面的操作需要挂载到子页面的资源上。
5. 针对货运的某些子页面(根页面和中台除外，根页面和菜单必须要；中台是全量的，只是需不需要注册)，如果产品整理的资源列表里有些操作和子页面不需要管控，这里面不用加，代码里也不用写。


