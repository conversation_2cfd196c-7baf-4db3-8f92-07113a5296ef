[{"resourceName": "车辆管理", "resourceCode": "@base:@page:vehicle.manage", "resourceUrl": "/vehicle-manage", "actions": [{"resourceName": "新增", "resourceCode": "@base:@page:vehicle.manage@action:add", "pageCode": "@base:@page:vehicle.manage:add"}, {"resourceName": "批量删除", "resourceCode": "@base:@page:vehicle.manage@action:batch.delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:vehicle.manage@action:edit", "pageCode": "@base:@page:vehicle.manage:edit"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:vehicle.manage@action:enable.unable"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:vehicle.manage@action:detail"}], "childs": [{"resourceName": "添加车辆", "resourceCode": "@base:@page:vehicle.manage:add", "resourceUrl": "/vehicle-manage/add-vehicle", "actions": []}, {"resourceName": "编辑车辆", "resourceCode": "@base:@page:vehicle.manage:edit", "resourceUrl": "/vehicle-manage/edit-vehicle", "actions": []}, {"resourceName": "车辆详情", "resourceCode": "@base:@page:vehicle.manage:detail", "resourceUrl": "/vehicle-manage/vehicle-detail", "actions": [{"resourceName": "车组详情", "resourceCode": "@base:@page:vehicle.manage:detail@action:goto.group"}, {"resourceName": "编辑详情", "resourceCode": "@base:@page:vehicle.manage:detail@action:edit", "pageCode": "@base:@page:vehicle.manage:edit"}, {"resourceName": "删除车辆", "resourceCode": "@base:@page:vehicle.manage:detail@action:delete"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:vehicle.manage:detail@action:enable.unable"}, {"resourceName": "编辑设备", "resourceCode": "@base:@page:vehicle.manage:detail@action:tab.bind.device:edit"}, {"resourceName": "绑定设备Tab", "resourceCode": "@base:@page:vehicle.manage:detail@action:tab.bind.device"}, {"resourceName": "绑定记录Tab", "resourceCode": "@base:@page:vehicle.manage:detail@action:tab.bind.record"}, {"resourceName": "设备功能-Tab", "resourceCode": "@base:@page:vehicle.manage:detail@action:tab.device.ability", "resourceChargeType": "2"}, {"resourceName": "设备详情", "resourceCode": "@base:@page:vehicle.manage:detail@action:tab.device.detail", "resourceChargeType": "2"}]}]}, {"resourceName": "车组管理", "resourceCode": "@base:@page:group.manage", "resourceUrl": "/group-manage", "childs": [{"resourceName": "编辑车组", "resourceCode": "@base:@page:group.manage.edit", "resourceUrl": "/group-manage/edit-group"}, {"resourceName": "添加车组", "resourceCode": "@base:@page:group.manage.add", "resourceUrl": "/group-manage/add-group"}, {"resourceName": "车组详情", "resourceCode": "@base:@page:group.manage.detail", "resourceUrl": "/group-manage/detail"}], "actions": [{"resourceName": "批量删除", "resourceCode": "@base:@page:group.manage@action:batch.delete"}, {"resourceName": "新增", "resourceCode": "@base:@page:group.manage@action:add"}, {"resourceName": "编辑", "resourceCode": "@base:@page:group.manage@action:edit"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:group.manage@action:detail"}]}, {"resourceName": "司机管理", "resourceCode": "@base:@page:driver.manage", "resourceUrl": "/driver-manage", "childs": [{"resourceName": "添加司机", "resourceCode": "@base:@page:driver.manage:add", "resourceUrl": "/driver-manage/add-driver", "actions": [{"resourceName": "查看详情", "resourceCode": "@base:@page:driver.manage.add@action:view.detail", "pageCode": "@base:@page:driver.manage:detail"}]}, {"resourceName": "编辑司机", "resourceCode": "@base:@page:driver.manage:edit", "resourceUrl": "/driver-manage/edit-driver"}, {"resourceName": "司机详情", "resourceCode": "@base:@page:driver.manage:detail", "resourceUrl": "/driver-manage/detail", "actions": [{"resourceName": "车组详情", "resourceCode": "@base:@page:driver.manage:detail@action:group.detail"}, {"resourceName": "车辆详情", "resourceCode": "@base:@page:driver.manage:detail@action:vehicle.detail"}]}], "actions": [{"resourceName": "批量删除", "resourceCode": "@base:@page:driver.manage@action:batch.delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:driver.manage@action:edit"}, {"resourceName": "新增", "resourceCode": "@base:@page:driver.manage@action:add"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:driver.manage@action:detail"}, {"resourceName": "上传", "resourceCode": "@base:@page:driver.manage@action:upload"}]}, {"resourceName": "设备管理", "resourceCode": "@base:@page:device.manage", "resourceUrl": "/device-manage", "childs": [{"resourceName": "新增设备", "resourceCode": "@base:@page:device.manage:add", "resourceUrl": "/device-manage/add-device"}, {"resourceName": "编辑设备", "resourceCode": "@base:@page:device.manage:edit", "resourceUrl": "/device-manage/edit-device"}, {"resourceName": "设备详情", "resourceCode": "@base:@page:device.manage:detail", "resourceUrl": "/device-manage/device-detail", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:device.manage:detail@action:delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:device.manage:detail@action:edit"}, {"resourceName": "设备配置", "resourceCode": "@base:@page:device.manage:detail@action:device.config", "resourceChargeType": "2"}, {"resourceName": "添加配置", "resourceCode": "@base:@page:device.manage:detail@action:device.config.add", "resourceChargeType": "2"}, {"resourceName": "续期设置", "resourceCode": "@base:@page:device.manage:detail@action:device.config.renewal", "resourceChargeType": "2"}, {"resourceName": "删除配置", "resourceCode": "@base:@page:device.manage:detail@action:device.config.delete", "resourceChargeType": "2"}]}], "actions": [{"resourceName": "激活设备", "resourceCode": "@base:@page:device.manage@action:active"}, {"resourceName": "批量删除设备", "resourceCode": "@base:@page:device.manage@action:batch.delete"}, {"resourceName": "编辑设备", "resourceCode": "@base:@page:device.manage@action:edit"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:device.manage@action:detail"}, {"resourceName": "设备型号", "resourceCode": "@base:@page:channel.setting@action:tab.channel.mode.switch", "resourceChargeType": "2"}]}, {"resourceName": "下级平台管理", "resourceCode": "@base:@page:sub.platform.manage", "resourceUrl": "/sub-platform-manage", "resourceChargeType": "2", "childs": [{"resourceName": "添加下级平台", "resourceCode": "@base:@page:sub.platform.manage:add", "resourceUrl": "/sub-platform-manage/add-platform", "resourceChargeType": "2"}, {"resourceName": "编辑下级平台", "resourceCode": "@base:@page:sub.platform.manage:edit", "resourceUrl": "/sub-platform-manage/edit-platform", "resourceChargeType": "2"}, {"resourceName": "下级平台详情", "resourceCode": "@base:@page:sub.platform.manage:detail", "resourceUrl": "/sub-platform-manage/platform-detail", "resourceChargeType": "2", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:sub.platform.manage:detail@action:delete", "resourceChargeType": "2"}, {"resourceName": "编辑", "resourceCode": "@base:@page:sub.platform.manage:detail@action:edit", "resourceChargeType": "2"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:sub.platform.manage:detail@action:enable.unable", "resourceChargeType": "2"}]}], "actions": [{"resourceName": "添加下级平台", "resourceCode": "@base:@page:sub.platform.manage@action:add", "resourceChargeType": "2"}, {"resourceName": "删除下级平台", "resourceCode": "@base:@page:sub.platform.manage@action:delete", "resourceChargeType": "2"}, {"resourceName": "编辑下级平台", "resourceCode": "@base:@page:sub.platform.manage@action:edit", "resourceChargeType": "2"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:sub.platform.manage@action:detail", "resourceChargeType": "2"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:sub.platform.manage@action:enable.unable", "resourceChargeType": "2"}, {"resourceName": "同步下级平台", "resourceCode": "@base:@page:sub.platform.manage@action:sync", "resourceChargeType": "2"}]}, {"resourceName": "上级平台管理", "resourceCode": "@base:@page:super.platform.manage", "resourceUrl": "/super-platform-manage", "resourceChargeType": "2", "childs": [{"resourceName": "添加上级平台", "resourceCode": "@base:@page:super.platform.manage:add", "resourceUrl": "/super-platform-manage/add-platform", "resourceChargeType": "2"}, {"resourceName": "编辑上级平台", "resourceCode": "@base:@page:super.platform.manage:edit", "resourceUrl": "/super-platform-manage/edit-base", "resourceChargeType": "2"}, {"resourceName": "上级平台详情", "resourceCode": "@base:@page:super.platform.manage:detail", "resourceUrl": "/super-platform-manage/platform-detail", "resourceChargeType": "2", "childs": [{"resourceName": "编辑下级平台", "resourceCode": "@base:@page:super.platform.manage.sub:edit", "resourceUrl": "/super-platform-manage/edit-sub", "resourceChargeType": "2"}], "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:super.platform.manage:detail@action:delete", "resourceChargeType": "2"}, {"resourceName": "编辑", "resourceCode": "@base:@page:super.platform.manage:detail@action:edit", "resourceChargeType": "2"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:super.platform.manage:detail@action:enable.unable", "resourceChargeType": "2"}]}], "actions": [{"resourceName": "添加上级平台", "resourceCode": "@base:@page:super.platform.manage@action:add", "resourceChargeType": "2"}, {"resourceName": "删除上级平台", "resourceCode": "@base:@page:super.platform.manage@action:delete", "resourceChargeType": "2"}, {"resourceName": "编辑上级平台", "resourceCode": "@base:@page:super.platform.manage@action:edit", "resourceChargeType": "2"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:super.platform.manage@action:detail", "resourceChargeType": "2"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:super.platform.manage@action:enable.unable", "resourceChargeType": "2"}]}, {"resourceName": "License绑定查询", "resourceCode": "@base:@page:license.bind.query", "resourceUrl": "/license-bind-query", "resourceChargeType": "2", "actions": [{"resourceName": "车辆详情", "resourceCode": "@base:@page:license.bind.query@action:vehicle.detail", "resourceChargeType": "2"}, {"resourceName": "批量续期", "resourceCode": "@base:@page:license.bind.query@action:bath.vehicle.renewal", "resourceChargeType": "2"}, {"resourceName": "续期", "resourceCode": "@base:@page:license.bind.query@action:vehicle.renewal", "resourceChargeType": "2"}]}]