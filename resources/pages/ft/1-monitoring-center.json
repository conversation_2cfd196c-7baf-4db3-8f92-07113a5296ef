[{"resourceName": "车辆监控", "resourceCode": "@base:@page:realtime.monitoring", "resourceUrl": "/realtime-monitoring", "childs": [{"resourceName": "单车护航", "resourceCode": "@base:@page:car.escort", "resourceUrl": "/car-escort", "actions": [{"resourceName": "视频剪辑", "resourceCode": "@base:@page:car.escort@action:cut.video"}, {"resourceName": "对讲", "resourceCode": "@base:@page:car.escort@action:intercom"}, {"resourceName": "文本下发", "resourceCode": "@base:@page:car.escort@action:messagesend"}, {"resourceName": "文本消息", "resourceCode": "@base:@page:car.escort@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:car.escort@action:audio.message"}, {"resourceName": "常用文本管理", "resourceCode": "@base:@page:car.escort@action:messagesend:common.text.manage"}, {"resourceName": "常用音频管理", "resourceCode": "@base:@page:car.escort@action:messagesend:common.audio.manage"}, {"resourceName": "电子放大", "resourceCode": "@base:@page:car.escort@action:enlarge"}, {"resourceName": "边看边录", "resourceCode": "@base:@page:car.escort@action:realtime.record"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:car.escort@action:messagesend:display.screen", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:car.escort@action:messagesend:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "延时录像", "resourceCode": "@base:@page:car.escort@action:delay.video.record"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:car.escort@action:algorithm.mode", "resourceChargeType": "2"}]}, {"resourceName": "轨迹回放", "resourceCode": "@base:@page:track.playback", "resourceUrl": "/track-playback", "actions": [{"resourceName": "导出电子围栏", "resourceCode": "@base:@page:track.playback@action:save.route"}, {"resourceName": "跳转报警详情", "resourceCode": "@base:@page:track.playback@action:alarm.detail"}, {"resourceName": "风险曲线", "resourceCode": "@base:@page:track.playback@action:common.risk.manage", "resourceChargeType": "2"}, {"resourceName": "查看视频回放", "resourceCode": "@base:@page:track.playback@action:view.playback"}, {"resourceName": "保存轨迹到平台电子围栏", "resourceCode": "@base:@page:track.playback@action:save.route:route.manage", "resourceChargeType": "2", "pageCode": "@base:@page:fence.line.manage:add"}, {"resourceName": "限时线路", "resourceCode": "@base:@page:track.playback@action:save.route:limit.time.route", "pageCode": "@base:@page:fence.time:add"}, {"resourceName": "限速线路", "resourceCode": "@base:@page:track.playback@action:save.route:limit.speed.route", "pageCode": "@base:@page:fence.speed:add"}, {"resourceName": "隐藏视频", "resourceCode": "@base:@page:track.playback@action:hide.video", "resourceChargeType": "2"}]}, {"resourceName": "车辆对讲", "resourceCode": "@base:@page:vehicle.intercom", "resourceUrl": "/vehicle/intercom", "actions": []}, {"resourceName": "车辆实时视频", "resourceCode": "@base:@page:vehicle.realtime.video", "resourceUrl": "/vehicle/realtime-video", "actions": []}], "actions": [{"resourceName": "实时视频", "resourceCode": "@base:@page:realtime.monitoring@action:realtime.video"}, {"resourceName": "单车护航", "resourceCode": "@base:@page:realtime.monitoring@action:single.vehicle.escort"}, {"resourceName": "设备回放", "resourceCode": "@base:@page:realtime.monitoring@action:device.playback"}, {"resourceName": "服务器回放", "resourceCode": "@base:@page:realtime.monitoring@action:serve.playback"}, {"resourceName": "融合回放", "resourceCode": "@base:@page:realtime.monitoring@action:mix.playback"}, {"resourceName": "轨迹回放", "resourceCode": "@base:@page:realtime.monitoring@action:track.playback"}, {"resourceName": "监听", "resourceCode": "@base:@page:realtime.monitoring@action:listen"}, {"resourceName": "对讲", "resourceCode": "@base:@page:realtime.monitoring@action:Intercom"}, {"resourceName": "车辆对讲", "resourceCode": "@base:@page:realtime.monitoring@action:VehicleIntercom"}, {"resourceName": "消息下发", "resourceCode": "@base:@page:realtime.monitoring@action:messagesend"}, {"resourceName": "文本消息", "resourceCode": "@base:@page:realtime.monitoring@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:realtime.monitoring@action:audio.message"}, {"resourceName": "查看报警详情", "resourceCode": "@base:@page:realtime.monitoring@action:real.alarmlist:evidence.detail"}, {"resourceName": "常用文本管理", "resourceCode": "@base:@page:realtime.monitoring@action:messagesend:common.text.manage"}, {"resourceName": "常用音频管理", "resourceCode": "@base:@page:realtime.monitoring@action:messagesend:common.audio.manage"}, {"resourceName": "电子放大", "resourceCode": "@base:@page:realtime.monitoring@action:enlarge"}, {"resourceName": "边看边录", "resourceCode": "@base:@page:realtime.monitoring@action:realtime.record"}, {"resourceName": "查看更多报警", "resourceCode": "@base:@page:realtime.monitoring@action:real.alarmlist:view.more"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:realtime.monitoring@action:messagesend:display.screen", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:realtime.monitoring@action:messagesend:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "查看电子围栏", "resourceCode": "@base:@page:realtime.monitoring@action:view.fence", "resourceChargeType": "2"}, {"resourceName": "查看全部", "resourceCode": "@base:@page:realtime.monitoring@action:alarm.view.all"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:realtime.monitoring@action:algorithm.mode", "resourceChargeType": "2"}, {"resourceName": "隐藏视频", "resourceCode": "@base:@page:realtime.monitoring@action:hide.video", "resourceChargeType": "2"}, {"resourceName": "模式切换", "resourceCode": "@base:@page:realtime.monitoring@action:mode.switch", "resourceChargeType": "2"}]}, {"resourceName": "实时视频", "resourceCode": "@base:@page:realtime.video", "resourceUrl": "/realtime-video", "actions": [{"resourceName": "电子放大", "resourceCode": "@base:@page:realtime.video@action:enlarge"}, {"resourceName": "边看边录", "resourceCode": "@base:@page:realtime.video@action:realtime.record"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:realtime.video@action:algorithm.mode", "resourceChargeType": "2"}]}, {"resourceName": "视频回放", "resourceCode": "@base:@page:playback.mix", "resourceUrl": "/playback-mix", "actions": [{"resourceName": "跳转报警详情", "resourceCode": "@base:@page:playback.mix@action:alarm.detail"}, {"resourceName": "电子放大", "resourceCode": "@base:@page:playback.mix@action:enlarge"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:playback.mix@action:algorithm.mode", "resourceChargeType": "2"}, {"resourceName": "省流模式", "resourceCode": "@base:@page:playback.mix@action:throttle.mode"}, {"resourceName": "延时录像", "resourceCode": "@base:@page:playback.mix@action:delay.video.record"}, {"resourceName": "保存线路", "resourceCode": "@base:@page:playback.mix@action:save.route"}]}, {"resourceName": "设备回放", "resourceCode": "@base:@page:playback.device", "resourceUrl": "/playback-device", "actions": [{"resourceName": "跳转报警详情", "resourceCode": "@base:@page:playback.device@action:alarm.detail"}, {"resourceName": "电子放大", "resourceCode": "@base:@page:playback.device@action:enlarge"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:playback.device@action:algorithm.mode", "resourceChargeType": "2"}, {"resourceName": "省流模式", "resourceCode": "@base:@page:playback.device@action:throttle.mode"}, {"resourceName": "延时录像", "resourceCode": "@base:@page:playback.device@action:delay.video.record"}, {"resourceName": "保存线路", "resourceCode": "@base:@page:playback.device@action:save.route"}]}, {"resourceName": "服务器回放", "resourceCode": "@base:@page:playback.serve", "resourceUrl": "/playback-serve", "actions": [{"resourceName": "跳转报警详情", "resourceCode": "@base:@page:playback.serve@action:alarm.detail"}, {"resourceName": "电子放大", "resourceCode": "@base:@page:playback.serve@action:enlarge"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:playback.serve@action:algorithm.mode", "resourceChargeType": "2"}, {"resourceName": "省流模式", "resourceCode": "@base:@page:playback.serve@action:throttle.mode"}, {"resourceName": "延时录像", "resourceCode": "@base:@page:playback.serve@action:delay.video.record"}, {"resourceName": "保存线路", "resourceCode": "@base:@page:playback.serve@action:save.route"}]}, {"resourceName": "视频墙", "resourceCode": "@base:@page:video.wall", "resourceUrl": "/video-wall", "actions": [], "childs": [{"resourceName": "视频墙播放", "resourceCode": "@base:@page:video.play", "resourceUrl": "/videoplay", "actions": []}]}, {"resourceName": "风险任务", "resourceCode": "@base:@page:risk.task", "resourceUrl": "/risk-task", "actions": [{"resourceName": "电子放大", "resourceCode": "@base:@page:risk.task@action:enlarge"}, {"resourceName": "消息下发", "resourceCode": "@base:@page:risk.task@action:messagesend"}, {"resourceName": "对讲", "resourceCode": "@base:@page:risk.task@action:Intercom"}, {"resourceName": "接受/完成任务", "resourceCode": "@base:@page:risk.task@action:accept.complete.task"}, {"resourceName": "文本下发", "resourceCode": "@base:@page:risk.task@action:messagesend:common.text.manage"}, {"resourceName": "音频下发", "resourceCode": "@base:@page:risk.task@action:messagesend:common.audio.manage"}, {"resourceName": "文本消息", "resourceCode": "@base:@page:risk.task@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:risk.task@action:audio.message"}, {"resourceName": "报警详情", "resourceCode": "@base:@page:risk.task@action:open.alarm.detail"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:risk.task@action:messagesend:display.screen", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:risk.task@action:messagesend:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "算法模式", "resourceCode": "@base:@page:risk.task@action:algorithm.mode", "resourceChargeType": "2"}], "childs": [{"resourceName": "风险任务设置", "resourceCode": "@base:@page:risk.task.setting", "resourceUrl": "/risk-task/setting", "actions": []}]}, {"resourceName": "任务记录", "resourceCode": "@base:@page:risk.task.record", "resourceUrl": "/task-record", "actions": [{"resourceName": "添加标签", "resourceCode": "@base:@page:risk.task.record@action:add.label"}]}]