[{"resourceName": "数据清理", "resourceCode": "@base:@page:system.data.clear", "resourceUrl": "/system/data-clear", "childs": [{"resourceName": "自定义保存有效期设置", "resourceCode": "@base:@page:custom.retention.period.setting", "resourceUrl": "/system/retention-period-setting", "resourceChargeType": "2", "childs": [{"resourceName": "自定义保存有效期详情", "resourceCode": "@base:@page:custom.retention.period.setting:detail", "resourceUrl": "/system/retention-period-setting/detail", "resourceChargeType": "2", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:custom.retention.period.setting:detail@action:delete", "resourceChargeType": "2"}, {"resourceName": "编辑", "resourceCode": "@base:@page:custom.retention.period.setting:detail@action:edit", "pageCode": "@base:@page:custom.retention.period.setting:edit", "resourceChargeType": "2"}, {"resourceName": "添加授权（车组、车辆）", "resourceCode": "@base:@page:custom.retention.period.setting:detail@action:auth.edit", "resourceChargeType": "2"}]}, {"resourceName": "添加自定义保存有效期设置", "resourceCode": "@base:@page:custom.retention.period.setting:add", "resourceUrl": "/system/retention-period-setting/add", "resourceChargeType": "2"}, {"resourceName": "复制自定义保存有效期设置", "resourceCode": "@base:@page:custom.retention.period.setting:copy", "resourceUrl": "/system/retention-period-setting/copy", "resourceChargeType": "2"}, {"resourceName": "编辑自定义保存有效期设置", "resourceCode": "@base:@page:custom.retention.period.setting:edit", "resourceUrl": "/system/retention-period-setting/edit", "resourceChargeType": "2"}, {"resourceName": "保存有效期设置排序", "resourceCode": "@base:@page:custom.retention.period.setting:sort", "resourceUrl": "/system/retention-period-setting/sort/:appId", "resourceChargeType": "2"}], "actions": [{"resourceName": "新增", "resourceCode": "@base:@page:custom.retention.period.setting@action:add", "pageCode": "@base:@page:custom.retention.period.setting:add", "resourceChargeType": "2"}, {"resourceName": "编辑", "resourceCode": "@base:@page:custom.retention.period.setting@action:edit", "pageCode": "@base:@page:custom.retention.period.setting:edit", "resourceChargeType": "2"}, {"resourceName": "复制", "resourceCode": "@base:@page:custom.retention.period.setting@action:copy", "pageCode": "@base:@page:custom.retention.period.setting:copy", "resourceChargeType": "2"}, {"resourceName": "删除", "resourceCode": "@base:@page:custom.retention.period.setting@action:delete", "resourceChargeType": "2"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:custom.retention.period.setting@action:detail", "pageCode": "@base:@page:custom.retention.period.setting:detail", "resourceChargeType": "2"}, {"resourceName": "排序", "resourceCode": "@base:@page:custom.retention.period.setting@action:sort", "pageCode": "@base:@page:custom.retention.period.setting:sort", "resourceChargeType": "2"}, {"resourceName": "默认设置", "resourceCode": "@base:@page:custom.retention.period.setting@action:default", "resourceChargeType": "2"}]}], "actions": [{"resourceName": "数据清理", "resourceCode": "@base:@page:task.clear@action:tab.clear:clear"}, {"resourceName": "保存有效期Tab", "resourceCode": "@base:@page:task.clear@action:tab.validity"}, {"resourceName": "手动清理Tab", "resourceCode": "@base:@page:task.clear@action:tab.clear"}, {"resourceName": "更多", "resourceChargeType": "2", "resourceCode": "@base:@page:task.clear@action:tab.clear:more"}, {"resourceName": "编辑", "resourceCode": "@base:@page:task.clear@action:tab.clear:edit"}, {"resourceName": "查看证据", "resourceCode": "@base:@page:task.clear@action:tab.clear:view.evidence", "pageCode": "@base:@page:evidence.detail"}, {"resourceName": "查看视频", "resourceCode": "@base:@page:task.clear@action:tab.clear:view.video", "pageCode": "@base:@page:video.detail"}]}, {"resourceName": "保存有效期设置查询", "resourceCode": "@base:@page:system.retention.period.setting.query", "resourceUrl": "/system/retention-period-setting-query", "resourceChargeType": "2", "actions": [{"resourceName": "默认保存有效期设置", "resourceChargeType": "2", "resourceCode": "@base:@page:system.retention.period.settings.query@action:default.retention.period.setting"}, {"resourceName": "自定义保存有效期设置", "resourceCode": "@base:@page:system.retention.period.settings.query@action:custom.retention.period.setting", "resourceChargeType": "2"}]}, {"resourceName": "空间管理", "resourceCode": "@base:@page:space.manage", "resourceUrl": "/space-manage", "resourceChargeType": "2", "actions": [{"resourceName": "查看单车存储空间详情", "resourceCode": "@base:@page:space.manage@action:single.vehicle.detail", "resourceChargeType": "2"}, {"resourceName": "空间详情", "resourceCode": "@base:@page:space.manage@action:detail", "resourceChargeType": "2"}, {"resourceName": "车辆使用", "resourceCode": "@base:@page:space.manage@action:tab.vehicle", "resourceChargeType": "2"}, {"resourceName": "车辆使用-批量分配", "resourceCode": "@base:@page:space.manage@action:tab.vehicle:batch.distribute", "resourceChargeType": "2"}, {"resourceName": "车辆使用-车辆详情", "resourceCode": "@base:@page:space.manage@action:tab.vehicle:distribute.detail", "pageCode": "@base:@page:space.manage:vehicle.detail", "resourceChargeType": "2"}, {"resourceName": "子租户分配", "resourceCode": "@base:@page:space.manage@action:tab.subtenant.distribute", "resourceChargeType": "2"}, {"resourceName": "子租户分配-查看详情", "resourceCode": "@base:@page:space.manage@action:tab.subtenant.distribute:detial", "pageCode": "@base:@page:space.manage:subtenant.allocation.detail", "resourceChargeType": "2"}, {"resourceName": "子租户分配-分配", "resourceCode": "@base:@page:space.manage@action:tab.subtenant.distribute:distribute.button", "resourceChargeType": "2"}], "childs": [{"resourceName": "车辆详情", "resourceCode": "@base:@page:space.manage:vehicle.detail", "resourceUrl": "/space-manage/vehicle-detail", "actions": [{"resourceName": "清理", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:clear", "resourceChargeType": "2"}, {"resourceName": "分配明细", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.distribute.detail", "resourceChargeType": "2"}, {"resourceName": "分配明细-单车分配", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.distribute.detail:vehicle", "resourceChargeType": "2"}, {"resourceName": "可清理文件", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file", "resourceChargeType": "2"}, {"resourceName": "可清理文件-批量删除", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file:batch.delete", "resourceChargeType": "2"}, {"resourceName": "查看证据", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file:view.evidence", "resourceChargeType": "2", "pageCode": "@base:@page:evidence.detail"}, {"resourceName": "查看视频", "resourceCode": "@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file:view.video", "resourceChargeType": "2", "pageCode": "@base:@page:video.detail"}], "resourceChargeType": "2"}, {"resourceName": "子租户分配详情", "resourceCode": "@base:@page:space.manage:subtenant.allocation.detail", "resourceUrl": "/space-manage/subtenant-allocation-detail", "actions": [{"resourceName": "分配", "resourceCode": "@base:@page:space.manage:subtenant.allocation.detail@action:distribute", "resourceChargeType": "2"}], "resourceChargeType": "2"}]}, {"resourceName": "视频定时下载", "resourceCode": "@base:@page:system.video.regular.download", "resourceUrl": "/system/video-regular-download", "actions": [{"resourceName": "添加", "resourceCode": "@base:@page:system.video.regular.download@action:add"}, {"resourceName": "批量删除", "resourceCode": "@base:@page:system.video.regular.download@action:batch.del"}, {"resourceName": "编辑", "resourceCode": "@base:@page:system.video.regular.download@action:edit"}, {"resourceName": "详情", "resourceCode": "@base:@page:system.video.regular.download@action:detail"}, {"resourceName": "启停用", "resourceCode": "@base:@page:system.video.regular.download@action:able.enable"}, {"resourceName": "高速下载", "resourceCode": "@base:@page:system.video.regular.download@action:high.speed.download"}], "childs": [{"resourceName": "新增任务", "resourceCode": "@base:@page:system.video.regular.download:add", "resourceUrl": "/system/video-regular-download/add", "actions": [{"resourceName": "延时录像", "resourceCode": "@base:@page:system.video.regular.download:add@action:delay.record"}]}, {"resourceName": "编辑任务", "resourceCode": "@base:@page:system.video.regular.download:edit", "resourceUrl": "/system/video-regular-download/edit", "actions": [{"resourceName": "延时录像", "resourceCode": "@base:@page:system.video.regular.download:edit@action:delay.record"}]}, {"resourceName": "任务详情", "resourceCode": "@base:@page:system.video.regular.download:detail", "resourceUrl": "/system/video-regular-download/detail", "actions": [{"resourceName": "适用范围Tab", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.auth.scope"}, {"resourceName": "任务列表Tab", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list"}, {"resourceName": "启停用", "resourceCode": "@base:@page:system.video.regular.download:detail@action:able.enable"}, {"resourceName": "删除（任务）", "resourceCode": "@base:@page:system.video.regular.download:detail@action:task.del"}, {"resourceName": "编辑", "resourceCode": "@base:@page:system.video.regular.download:detail@action:edit"}, {"resourceName": "授权（车组、车辆）", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.auth.scop.edit"}, {"resourceName": "删除（任务列表）", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list:delete"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list:detail", "pageCode": "@base:@page:video.detail"}, {"resourceName": "批量重试", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list:batch.retry"}, {"resourceName": "置顶", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list:top"}, {"resourceName": "强制执行", "resourceCode": "@base:@page:system.video.regular.download:detail@action:tab.task.list:force.execute"}, {"resourceName": "延时录像", "resourceCode": "@base:@page:system.video.regular.download:detail@action:delay.record"}]}]}, {"resourceName": "区域查车", "resourceCode": "@base:@page:system.region.vehicle.find", "resourceUrl": "/system/region-vehicle-find", "actions": [{"resourceName": "查车任务", "resourceCode": "@base:@page:system.region.vehicle.find@action:task.list"}, {"resourceName": "开始查车", "resourceCode": "@base:@page:system.region.vehicle.find@action:create.task"}]}, {"resourceName": "查车任务", "resourceCode": "@base:@page:system.region.vehicle.find:list", "resourceUrl": "/system/region-vehicle-find-list", "actions": [{"resourceName": "轨迹回放", "resourceCode": "@base:@page:system.region.vehicle.find:list@action:track.playback"}, {"resourceName": "任务详情", "resourceCode": "@base:@page:system.region.vehicle.find:list@action:task.detail"}, {"resourceName": "新增区域查车任务", "resourceCode": "@base:@page:system.region.vehicle.find:list@action:add"}]}, {"resourceName": "消息定时下发", "resourceCode": "@base:@page:system.message.regular.distribute", "resourceUrl": "/system/message-regular-distribute", "actions": [{"resourceName": "添加", "resourceCode": "@base:@page:system.message.regular.distribute@action:add", "pageCode": "@base:@page:system.message.regular.distribute:add"}, {"resourceName": "删除", "resourceCode": "@base:@page:system.message.regular.distribute@action:delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:system.message.regular.distribute@action:edit", "pageCode": "@base:@page:system.message.regular.distribute:edit"}, {"resourceName": "详情", "resourceCode": "@base:@page:system.message.regular.distribute@action:detail", "pageCode": "@base:@page:system.message.regular.distribute:detail"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:system.message.regular.distribute@action:able.enable"}], "childs": [{"resourceName": "添加任务", "resourceCode": "@base:@page:system.message.regular.distribute:add", "resourceUrl": "/system/message-regular-distribute/add", "actions": [{"resourceName": "文本消息", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:audio.message"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:display.location", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "常用文本管理", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:common.text"}, {"resourceName": "常用音频管理", "resourceCode": "@base:@page:system.message.regular.distribute.add@action:common.audio"}]}, {"resourceName": "编辑任务", "resourceCode": "@base:@page:system.message.regular.distribute:edit", "resourceUrl": "/system/message-regular-distribute/edit", "actions": [{"resourceName": "文本消息", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:audio.message"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:display.location", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "常用文本管理", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:common.text"}, {"resourceName": "常用音频管理", "resourceCode": "@base:@page:system.message.regular.distribute.edit@action:common.audio"}]}, {"resourceName": "任务详情", "resourceCode": "@base:@page:system.message.regular.distribute:detail", "resourceUrl": "/system/message-regular-distribute/detail", "actions": [{"resourceName": "文本消息", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:text.message"}, {"resourceName": "音频消息", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:audio.message"}, {"resourceName": "适用范围Tab", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:tab.auth.scope"}, {"resourceName": "任务列表Tab", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:tab.task.list"}, {"resourceName": "启/停用", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:able.enable"}, {"resourceName": "删除（任务）", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:task.delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:edit"}, {"resourceName": "显示屏幕设置", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:display.location", "resourceChargeType": "2"}, {"resourceName": "文本转语音", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:text.to.speech", "resourceChargeType": "2"}, {"resourceName": "编辑授权", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:auth.vehicle.fleet"}, {"resourceName": "删除（任务列表）", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:task.scope.list.delete"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:detail"}, {"resourceName": "常用文本管理", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:common.text"}, {"resourceName": "常用音频管理", "resourceCode": "@base:@page:system.message.regular.distribute.detail@action:common.audio"}]}]}]