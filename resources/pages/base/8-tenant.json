[{"resourceName": "租户详情", "resourceCode": "@base:@page:tanant.detail", "resourceUrl": "/tenant-detail", "actions": [{"resourceName": "租户授权码", "resourceCode": "@base:@page:tenant.detail@action:edit"}, {"resourceName": "更换管理员", "resourceCode": "@base:@page:tenant.detail@action:replace.admin"}, {"resourceName": "订阅套餐", "resourceCode": "@base:@page:tenant.detail@action:tab.function.package"}, {"resourceName": "存储套餐", "resourceCode": "@base:@page:tenant.detail@action:tab.storage.package"}, {"resourceName": "租户配置", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config"}, {"resourceName": "用户状态配置", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config:tab.user.state.config"}, {"resourceName": "用户状态编辑", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config:tab.user.state.config:edit"}, {"resourceName": "用户隐私配置", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config:tab.user.privacy.config", "resourceChargeType": "2"}, {"resourceName": "用户隐私编辑", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config:tab.user.privacy.config:edit", "resourceChargeType": "2"}, {"resourceName": "上级租户访问", "resourceCode": "@base:@page:tenant.detail@action:tab.superior.visit"}, {"resourceName": "License管理", "resourceCode": "@base:@page:tenant.detail@action:tab.license.manage", "resourceChargeType": "2"}, {"resourceName": "导入License", "resourceCode": "@base:@page:tenant.detail@action:tab.license.import", "resourceChargeType": "2"}, {"resourceName": "查看明细", "resourceCode": "@base:@page:tenant.detail@action:tab.license.detail", "resourceChargeType": "2"}, {"resourceName": "导入配置包", "resourceCode": "@base:@page:tenant.detail@action:tab.license.import.config"}, {"resourceName": "导入存储空间License包", "resourceCode": "@base:@page:tenant.detail@action:tab.license.import.storage"}, {"resourceName": "查看详情", "resourceCode": "@base:@page:tenant.detail@action:tab.superior.visit:detail"}, {"resourceName": "处理申请", "resourceCode": "@base:@page:tenant.detail@action:tab.superior.visit:handle"}, {"resourceName": "删除访问", "resourceCode": "@base:@page:tenant.detail@action:tab.superior.visit:delete"}, {"resourceName": "同意/拒绝访问", "resourceCode": "@base:@page:tenant.detail@action:tab.superior.visit:agree.reject"}, {"resourceName": "手机号安全验证", "resourceCode": "@base:@page:tenant.detail@action:tab.tenant.config:mobile.security", "resourceChargeType": "2"}], "childs": [{"resourceName": "套餐详情", "resourceCode": "@base:@page:tenant.detail:function.package.detail", "resourceUrl": "/tenant-detail/set-meal", "actions": []}, {"resourceName": "上级租户访问详情", "resourceCode": "@base:@page:tenant.detail:superior.visit.detail", "resourceUrl": "/sup-tenant-visit/detail", "actions": [{"resourceName": "同意访问/拒绝", "resourceCode": "@base:@page:tenant.detail:superior.visit.detail@action:agree.reject"}]}, {"resourceName": "上级租户访问设置", "resourceCode": "@base:@page:tenant.detail:superior.visit.detail.setting", "resourceUrl": "/sup-tenant-visit/setting", "actions": []}, {"resourceName": "License使用明细", "resourceCode": "@base:@page:tenant.detail:license.use.detail", "resourceUrl": "/tenant-detail/license-use-detail", "resourceChargeType": "2", "actions": []}, {"resourceName": "导入配置包", "resourceCode": "@base:@page:tenant.detail:import.tenant.config", "resourceUrl": "/tenant-detail/import-package", "actions": []}]}, {"resourceName": "子租户管理", "resourceCode": "@base:@page:sub.tanant.manage", "resourceUrl": "/sub-tenant-manage", "actions": [{"resourceName": "子租户编辑", "resourceCode": "@base:@page:sub.tanant.manage@action:edit", "pageCode": "@base:@page:sub.tanant.manage:edit"}, {"resourceName": "子租户详情", "resourceCode": "@base:@page:sub.tanant.manage@action:detail", "pageCode": "@base:@page:sub.tanant.manage:detail"}, {"resourceName": "子租户详情(未激活)", "resourceCode": "@base:@page:sub.tenant.manage@action:detail.unactive", "pageCode": "@base:@page:sub.tenant.manage:detail.unactive"}, {"resourceName": "子租户新增", "resourceCode": "@base:@page:tenant.sub-tenant.manage@action:add"}, {"resourceName": "子租户封停解封", "resourceCode": "@base:@page:sub.tanant.manage@action:lock.unlock"}, {"resourceName": "子租户激活", "resourceCode": "@base:@page:sub.tenant.manage@action:active", "pageCode": "@base:@page:sub.tanant.manage:add"}, {"resourceName": "子租户删除", "resourceCode": "@base:@page:tenant.sub-tenant.manage@action:delete"}], "childs": [{"resourceName": "编辑子租户", "resourceCode": "@base:@page:sub.tanant.manage:edit", "resourceUrl": "/sub-tenant-manage/edit", "actions": [{"resourceName": "编辑子租户保存", "resourceCode": "@base:@page:sub.tanant.manage:edit@action:save"}]}, {"resourceName": "子租户详情", "resourceCode": "@base:@page:sub.tanant.manage:detail", "resourceUrl": "/sub-tenant-manage/detail", "actions": [{"resourceName": "子租户删除", "resourceCode": "@base:@page:sub.tenant.manage:detail@action:delete"}, {"resourceName": "更换管理员", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:replace.admin"}, {"resourceName": "申请访问", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access"}, {"resourceName": "申请访问", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access:apply"}, {"resourceName": "开通套餐", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.package:activted"}, {"resourceName": "取消申请", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access:cancel.apply"}, {"resourceName": "重新申请", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access:reapply"}, {"resourceName": "关闭访问", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access:close.apply"}, {"resourceName": "删除申请", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.apply.access:del.apply"}, {"resourceName": "开通应用", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.open.application:add"}, {"resourceName": "功能配置", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.config"}, {"resourceName": "子租户管理", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.sub.tenant"}, {"resourceName": "开通明细", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.storage.package:activated.list"}, {"resourceName": "开通应用", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.open.application"}, {"resourceName": "开通套餐", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.package"}, {"resourceName": "启用，停用套餐", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.package:en.disable"}, {"resourceName": "删除套餐", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.package:del"}, {"resourceName": "授权协议", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.config:protocol"}, {"resourceName": "授权类型", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.config:alarm"}, {"resourceName": "授权参数", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.function.config:parameter"}, {"resourceName": "存储套餐", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:tab.storage.package"}, {"resourceName": "开通套餐", "resourceCode": "@base:@page:sub.tenant.manage:detail@action:open.meal"}, {"resourceName": "重置密码", "resourceCode": "@base:@page:sub.tanant.manage:detail@action:reset.password"}, {"resourceName": "License管理", "resourceCode": "@base:@page:sub.tenant.manage@action:tab.license.manage", "resourceChargeType": "2"}, {"resourceName": "分配License", "resourceCode": "@base:@page:sub.tenant.manage@action:tab.license.distribute", "resourceChargeType": "2"}], "childs": [{"resourceName": "套餐详情", "resourceCode": "@base:@page:sub.tanant.manage:detail:fuction.package.detail", "resourceUrl": "/sub-tenant-manage/set-meal", "actions": []}]}, {"resourceName": "子租户详情(未激活)", "resourceCode": "@base:@page:sub.tenant.manage:detail.unactive", "resourceUrl": "/sub-tenant-manage/detail-unactive", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:sub.tenant.manage:detail.unactive@action:delete"}, {"resourceName": "激活", "resourceCode": "@base:@page:sub.tenant.manage:detail.unactive@action:active", "pageCode": "@base:@page:sub.tanant.manage:add"}, {"resourceName": "重置密码", "resourceCode": "@base:@page:sub.tenant.manage:detail.unactive@action:reset.password"}]}, {"resourceName": "新增子租户", "resourceCode": "@base:@page:sub.tanant.manage:add", "resourceUrl": "/sub-tenant-manage/add", "actions": [{"resourceName": "查看模版详情", "resourceCode": "@base:@page:sub.tanant.manage:add@action:view.template.detail"}, {"resourceName": "新增子租户保存", "resourceCode": "@base:@page:sub.tanant.manage:add@action:save"}, {"resourceName": "新增子租户保存并激活", "resourceCode": "@base:@page:sub.tanant.manage:add@action:save.and.active"}]}]}, {"resourceName": "租户账单", "resourceCode": "@base:@page:tanant.settlement.bill", "resourceUrl": "/tenant-settlement-bill", "actions": [{"resourceName": "查看设备明细", "resourceCode": "@base:@page:tanant.settlement.bill@action:device.detail", "pageCode": "@base:@page:tanant.settlement.bill:device.detail"}], "childs": [{"resourceName": "设备明细", "resourceCode": "@base:@page:tanant.settlement.bill:device.detail", "resourceUrl": "/tenant-settlement-bill/device-detail"}]}, {"resourceName": "租户模板", "resourceCode": "@base:@page:tenant.template", "resourceUrl": "/tenant-center/tenant-template", "actions": [{"resourceName": "详情", "resourceCode": "@base:@page:tenant.template@action:detail", "pageCode": "@base:@page:tenant.template:detail"}, {"resourceName": "新增", "resourceCode": "@base:@page:tenant.template@action:add", "pageCode": "@base:@page:tenant.template:add"}, {"resourceName": "编辑", "resourceCode": "@base:@page:tenant.template@action:edit", "pageCode": "@base:@page:tenant.template:edit"}, {"resourceName": "复制", "resourceCode": "@base:@page:tenant.template@action:copy", "pageCode": "@base:@page:tenant.template:copy"}, {"resourceName": "删除", "resourceCode": "@base:@page:tenant.template@action:delete"}], "childs": [{"resourceName": "新增模板", "resourceCode": "@base:@page:tenant.template:add", "resourceUrl": "/tenant-center/tenant-template/add"}, {"resourceName": "编辑模板", "resourceCode": "@base:@page:tenant.template:edit", "resourceUrl": "/tenant-center/tenant-template/edit"}, {"resourceName": "复制模板", "resourceCode": "@base:@page:tenant.template:copy", "resourceUrl": "/tenant-center/tenant-template/copy"}, {"resourceName": "模板详情", "resourceCode": "@base:@page:tenant.template:detail", "resourceUrl": "/tenant-center/tenant-template/detail", "actions": [{"resourceName": "删除", "resourceCode": "@base:@page:tenant.template:detail@action:delete"}, {"resourceName": "编辑", "resourceCode": "@base:@page:tenant.template:detail@action:edit", "pageCode": "@base:@page:tenant.template:edit"}, {"resourceName": "预置角色详情", "resourceCode": "@base:@page:tenant.template:detail@action:pre.role.detail", "pageCode": "@base:@page:role.manage:detail"}, {"resourceName": "用户状态配置", "resourceCode": "@base:@page:tenant.template:detail@action:tab.tenant.config:tab.user.state.config"}, {"resourceName": "用户状态编辑", "resourceCode": "@base:@page:tenant.template:detail@action:tab.tenant.config:tab.user.state.config:edit"}, {"resourceName": "用户隐私配置", "resourceCode": "@base:@page:tenant.template:detail@action:tab.tenant.config:tab.user.privacy.config", "resourceChargeType": "2"}, {"resourceName": "用户隐私编辑", "resourceCode": "@base:@page:tenant.template:detail@action:tab.tenant.config:tab.user.privacy.config:edit", "resourceChargeType": "2"}], "childs": [{"resourceName": "编辑用户设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.user.set", "resourceUrl": "/tenant-template/detail/edit-user-set"}, {"resourceName": "编辑报警等级", "resourceCode": "@base:@page:tenant.templat:detail:edit.alarm.level", "resourceUrl": "/tenant-template/detail/edit-alarm-level"}, {"resourceName": "编辑报警分类", "resourceCode": "@base:@page:tenant.templat:detail:edit.alarm.type", "resourceUrl": "/tenant-template/detail/edit-alarm-type"}, {"resourceName": "编辑证据上传设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.evidence.upload", "resourceUrl": "/tenant-template/detail/edit-evidence-upload"}, {"resourceName": "编辑报警通知设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.alarm.notice", "resourceUrl": "/tenant-template/detail/edit-alarm-notice"}, {"resourceName": "编辑报警处理设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.alarm.handle", "resourceUrl": "/tenant-template/detail/edit-alarm-handle"}, {"resourceName": "编辑图片抓拍设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.picture.snap", "resourceUrl": "/tenant-template/detail/edit-picture-snap"}, {"resourceName": "编辑人脸对比设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.face.contras", "resourceUrl": "/tenant-template/detail/edit-face-contras"}, {"resourceName": "编辑通道设置", "resourceCode": "@base:@page:tenant.templat:detail:edit.channel", "resourceUrl": "/tenant-template/detail/edit-channel"}]}]}]