const fs = require('fs');
const path = require('path');
const glob = require('glob');

let lastId = 21001, _lastId = 21001;
const getId = () => lastId++;
const sql = [];
const appId = 0;
const tenantId = 0;
// 77环境
// const _tenantId = 29; 
// dev环境
const _tenantId = 884;
// sit环境
// const _tenantId = 884; 
const getPages = (target, reg = '**/*json') => {
    return new Promise((resolve, reject) => {
        const cwd = path.join(__dirname, '/pages/base', target);
        glob(
            reg,
            {
                cwd,
                dot: false,
            },
            (err, files) => {
                if (err) reject(err);
                const pages = files.reduce((current, file) => {
                    const source = require(path.join(cwd, file));
                    return [...current, ...source];
                }, []);
                resolve(pages);
            },
        );
    });
};
const getAllMemus = (reg = '*json') => {
    return new Promise((resolve, reject) => {
        const cwd = path.join(__dirname, '/menus/base');
        glob(
            reg,
            {
                cwd,
                dot: false,
            },
            (err, files) => {
                if (err) reject(err);
                // console.log(files);
                const menus = files.reduce((current, file) => {
                    const source = require(path.join(cwd, file));
                    // console.log('source');
                    // console.log(source);
                    return [...current, ...source];
                }, []);
                // console.log(menus);
                resolve(menus);
            },
        );
    });
};

const generateSql = ({
    id,
    parentId = 'NULL',
    resourceName,
    resourceCode,
    resourceType = 'NULL',
    resourceUrl,
    icon,
    pageId = 'NULL',
    level = 'NULL',
    appId,
    tenantId,
    management_switch,
    click_response_type = 1
}) => {
    resourceName = resourceName ? `'${resourceName}'` : 'NULL';
    resourceCode = resourceCode ? `'${resourceCode}'` : 'NULL';
    resourceUrl = resourceUrl ? `'${resourceUrl}'` : 'NULL';
    icon = icon ? `'${icon}'` : 'NULL';
    const createType = appId ? 2 : 1;
    return `INSERT INTO \`t_resource\` VALUES (${id}, ${tenantId || 0}, ${
        appId || 0
    }, ${parentId}, ${resourceName}, ${resourceCode}, ${resourceType}, ${resourceUrl}, 1, 1, 1, ${createType}, ${click_response_type}, ${level}, NULL, NULL, '0', ${icon}, ${pageId}, '931', 1619321888, NULL, NULL, '0');`;
};

// 生成slsx和txt文件
const generateSqlForSource = async ({ menus, pages, appId, tenantId }) => {
    const allActions = [];
    const allPages = [];
    const allMenus=[];
    const allSqlObj = [];
    // 插入页面
    const generateSqlForPages = () => {
        const generate = (innerPages, level = 1, parentId = 'NULL') => {
            innerPages.forEach((page) => {
                const { childs, actions } = page;

                const id = getId();
                page.id = id;
                allPages.push(Object.assign({}, page, { id }));
                allSqlObj.push(Object.assign({}, page, {
                    id,
                    level,
                    resourceType: 2,
                    appId,
                    tenantId,
                    parentId,
                }));
                sql.push(
                    generateSql(
                        Object.assign({}, page, {
                            id,
                            level,
                            resourceType: 2,
                            appId,
                            tenantId,
                            parentId,
                        }),
                    ),
                );
                if (childs && childs.length) {
                    generate(childs, level + 1, id);
                }
                if (actions && actions.length) {
                    actions.forEach((action) => {
                        allActions.push(
                            Object.assign({}, action, { parentId: id }),
                        );
                    });
                }
            });
        };
        generate(pages);
    };
    // 插入操作
    const generateSqlForActions = () => {
        allActions.forEach((action) => {
            const { pageCode } = action;
            const bindedPage = allPages.find(
                (page) => page.resourceCode === pageCode,
            );
            const pageId = bindedPage ? bindedPage.id : 'NULL';
            const id = getId();
            allSqlObj.push(Object.assign({}, action, {
                id,
                level: null,
                resourceType: 3,
                pageId,
                appId,
                tenantId,
            }));
            sql.push(
                generateSql(
                    Object.assign({}, action, {
                        id,
                        level: 1,
                        resourceType: 3,
                        pageId,
                        appId,
                        tenantId,
                    }),
                ),
            );
        });
    };
    // clearBase boolean 项目路由是否配置了base。配置了的情况下需要将菜单资源的 resourceUrl 去除 base前缀
    // 插入菜单
    const generateSqlForMenus = (clearBase= false) => {
        const generate = (innerMenus, level = 1, parentId = 'NULL') => {
            innerMenus.forEach((menu) => {
                const { pageCode, childs, resourceUrl, isExternal } = menu;
                const bindedPage = allPages.find(
                    (page) => page.resourceCode === pageCode,
                );
                const pageId = menu.pageId || (bindedPage ? bindedPage.id : 'NULL');
                const id = getId();
                allMenus.push(Object.assign({},menu,{id,level,pageId,parentId}));
                allSqlObj.push(Object.assign({}, menu, {
                    id,
                    level,
                    resourceType: 1,
                    pageId,
                    parentId,
                    appId,
                    tenantId,
                    resourceUrl:
                        resourceUrl ||
                        (bindedPage ? (clearBase ? bindedPage.resourceUrl.replace(/\/[\w]+/,''): bindedPage.resourceUrl) : null),
                    click_response_type: isExternal ? 3 : 1
                }));
                sql.push(
                    generateSql(
                        Object.assign({}, menu, {
                            id,
                            level,
                            resourceType: 1,
                            pageId,
                            parentId,
                            appId,
                            tenantId,
                            resourceUrl:
                                resourceUrl ||
                                (bindedPage ? (clearBase ? bindedPage.resourceUrl.replace(/\/[\w]+/,''): bindedPage.resourceUrl) : null),
                            click_response_type: isExternal ? 3 : 1
                        }),
                    ),
                );
                menu.id = id;
                menu.pageId = pageId;
                if (childs && childs.length) {
                    generate(childs, level + 1, id);
                }
            });
        };
        generate(menus);
    };
    generateSqlForPages();
    generateSqlForActions();
    generateSqlForMenus();
    // 生成用于导入的excel文件
    const relationSheet = '子资源编码	父资源编码'.replace(/[ ]+/,'').split('\t');
    const resourceSheet = '应用ID	资源名称	资源编码	资源类型	资源等级	资源访问路径	资源ICO	菜单排序序列号'.replace(/[ ]+/,'').split('\t');
    const internationalizationSheet='资源编码	中文翻译	英文翻译	西班牙语	葡萄牙语	法语	繁體中文(香港)翻译	俄罗斯语	德语	日语	泰语	阿拉伯语'.replace(/[ ]+/,'').split('\t');
    const relationData = [relationSheet];
    const resourceData = [resourceSheet];
    const internationalizationData=[internationalizationSheet];
    // 重复code检查
    const codeCheck = allSqlObj.filter(item=>{
        var x = allSqlObj.find(i=>{
            return i.resourceCode === item.resourceCode && i.id !== item.id
        });
        return x;
    })
    if(codeCheck.length>0){
        console.log('====code重复，请检查如下code=====');
        console.log(codeCheck);
        return;
    }
    // 超长code检查
    const maxLlenCodeCheck = allSqlObj.filter(item=>{
        return item.resourceCode.length>100;
    })
    if(maxLlenCodeCheck.length>0){
        console.log('====code超长，请检查如下code=====');
        console.log(maxLlenCodeCheck);
        return;
    }
    const ExcelJS = require('exceljs');
    let savedData = new ExcelJS.Workbook();
    const preVersion = fs.readdirSync(path.resolve(__dirname, './previous-version'));
    const preVersionName = (preVersion || []).find(p => p.indexOf('BasePlatform') !== -1);
    var savedRelationSheetData
    await savedData.xlsx.readFile(path.resolve(__dirname, './previous-version', preVersionName)).then(() => {
        let savedRelationSheet = savedData.getWorksheet('Internationalization')
        savedRelationSheetData = savedRelationSheet
    })
    let differentStr = ['JSON文件增加内容：']
    let addResources = [];
    allSqlObj.forEach(item => {
        const { appId, resourceName, resourceCode, resourceType, level = '', resourceUrl = '', icon = '', sortValue = 1, parentId, pageId } = item;
        resourceData.push([appId, resourceName, resourceCode, resourceType, level, resourceUrl, '', sortValue]);
        let i = 1 //i为每一行的索引如A1,A2
        let isNew = true
        //联合文档设置翻译
        while (savedRelationSheetData.getCell(`A${i}`).value) {
            if (resourceCode == savedRelationSheetData.getCell(`A${i}`).value) {
                let C1 = ''
                if (typeof savedRelationSheetData.getCell(`C${i}`).value =='string') {
                    C1 = C1 + savedRelationSheetData.getCell(`C${i}`).value
                } else if(savedRelationSheetData.getCell(`C${i}`).value instanceof Object){
                    C1 = C1 + savedRelationSheetData.getCell(`C${i}`).value.richText[0].text
                }else{
                    C1 = 'en_US' + resourceName
                }
                let D1 = ''
                if (savedRelationSheetData.getCell(`D${i}`).value && savedRelationSheetData.getCell(`D${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`D${i}`).value.richText.forEach(item => {
                        D1 = D1 + item.text
                    })
                } else {
                    D1 = 'es_ES' + resourceName
                }
                let E1 = ''
                if (savedRelationSheetData.getCell(`E${i}`).value && savedRelationSheetData.getCell(`E${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`E${i}`).value.richText.forEach(item => {
                        E1 = E1 + item.text
                    })
                } else {
                    E1 = 'pt_BR' + resourceName
                }
                // 新增
                // 法语
                let F1 = ''
                if (savedRelationSheetData.getCell(`F${i}`).value && savedRelationSheetData.getCell(`F${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`F${i}`).value.richText.forEach(item => {
                        F1 = F1 + item.text
                    })
                } else {
                    F1 = 'fr_FR' + resourceName
                }
                // 繁體中文(香港)
                let G1 = ''
                if (savedRelationSheetData.getCell(`G${i}`).value && savedRelationSheetData.getCell(`G${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`G${i}`).value.richText.forEach(item => {
                        G1 = G1 + item.text
                    })
                } else {
                    G1 = 'zh_HK' + resourceName
                }
                //俄罗斯语
                let H1 = ''
                if (savedRelationSheetData.getCell(`H${i}`).value && savedRelationSheetData.getCell(`H${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`H${i}`).value.richText.forEach(item => {
                        H1 = H1 + item.text
                    })
                } else {
                    H1 = 'ru_RU' + resourceName
                }
                // 德语
                let I1 = ''
                if (savedRelationSheetData.getCell(`I${i}`).value && savedRelationSheetData.getCell(`I${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`I${i}`).value.richText.forEach(item => {
                        I1 = I1 + item.text
                    })
                } else {
                    I1 = 'de_DE' + resourceName
                }
                // 日语 
                let J1 = ''
                if (savedRelationSheetData.getCell(`J${i}`).value && savedRelationSheetData.getCell(`J${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`J${i}`).value.richText.forEach(item => {
                        J1 = J1 + item.text
                    })
                } else {
                    J1 = 'ja_JP' + resourceName
                }
                // 泰语
                let K1 = ''
                if (savedRelationSheetData.getCell(`K${i}`).value && savedRelationSheetData.getCell(`K${i}`).value.richText != undefined) {
                    savedRelationSheetData.getCell(`K${i}`).value.richText.forEach(item => {
                        K1 = K1 + item.text
                    })
                } else {
                    K1 = 'th_TH' + resourceName
                }
                 // 阿拉伯语
                 let L1 = ''
                 if (savedRelationSheetData.getCell(`L${i}`).value && savedRelationSheetData.getCell(`L${i}`).value.richText != undefined) {
                     savedRelationSheetData.getCell(`L${i}`).value.richText.forEach(item => {
                         L1 = L1 + item.text
                     })
                 } else {
                     L1 = 'ar_EG' + resourceName
                 }
                internationalizationData.push(
                    [
                        resourceCode,
                        savedRelationSheetData.getCell(`B${i}`).value,
                        C1,
                        D1,
                        E1,
                        F1,
                        G1,
                        H1,
                        I1,
                        J1,
                        K1,
                        L1
                    ]
                );
                isNew = false
            }
            i++
        }
        if (isNew) {
            internationalizationData.push([resourceCode, resourceName, 'en_US' + resourceName, 'es_ES' + resourceName, 'pt_BR' + resourceName, 'fr_FR' + resourceName, 'zh_HK' + resourceName, 'ru_RU' + resourceName, 'de_DE' + resourceName, 'ja_JP' + resourceName, 'th_TH' + resourceName, 'ar_EG' + resourceName]);
            differentStr.push(`${resourceName}  ${resourceCode}`)
            addResources.push(resourceCode)
        }
        if (parentId !== 'NULL') {
            // console.log('parentId' + parentId);
            const parent = allSqlObj.find(i => i.id === parentId);
            const target = relationData.find(i => i[0] === resourceCode);
            if (!target) {
                relationData.push([resourceCode, parent.resourceCode]);
            } else {
                console.error(`如下code关系重复：${resourceCode},parentId:${parentId}`);
                console.log(target);
            }
        }
        // 菜单与页面的关系也需要加进去
        if (resourceType === 1 && pageId && pageId !== 'NULL') {
            const page = allSqlObj.find(i => i.id === pageId);
            const target = relationData.find(i => i[0] === page.resourceCode);
            if (!target) {
                relationData.push([page.resourceCode, resourceCode]);
            } else {
                console.error(`如下code关系重复：${page.resourceCode},pageId:${pageId}`);
                console.log(target);

            }
        }

    })
    differentStr.push('','--------------','','原文件增加内容:')
    let j =2 //j为每一行的索引如A1,A2
    while (savedRelationSheetData.getCell(`A${j}`).value){
        let isNew = true
        allSqlObj.forEach(item =>{
            const { resourceCode } = item;
            if(savedRelationSheetData.getCell(`A${j}`).value == resourceCode){
                isNew = false
            }
        })
        if(isNew){
            differentStr.push(`${savedRelationSheetData.getCell(`B${j}`).value}  ${savedRelationSheetData.getCell(`A${j}`).value}`)
        }
        j++
    }
    fs.writeFileSync('./resources/export/BP差异报告.txt', differentStr.join('\n'))
    const xlsx = require('xlsx');
    const workbook = {
        SheetNames: ['Relation' ,'Resource','Internationalization'],
        Sheets: {
            Relation: xlsx.utils.aoa_to_sheet(relationData),
            Resource: xlsx.utils.aoa_to_sheet(resourceData),
            Internationalization:xlsx.utils.aoa_to_sheet(internationalizationData)
        },
    };
    // 增量excel文档
    const workbook_inc = {
        SheetNames: ['Relation' ,'Resource','Internationalization'],
        Sheets: {
            Relation: xlsx.utils.aoa_to_sheet(relationData.filter((i,index)=>index===0 || addResources.includes(i[0]))),
            Resource: xlsx.utils.aoa_to_sheet(resourceData.filter((i,index)=>index===0 || addResources.includes(i[2]))),
            Internationalization:xlsx.utils.aoa_to_sheet(internationalizationData.filter((i,index)=>index===0 || addResources.includes(i[0])))
        },
    };
    
    if(fs.existsSync(path.resolve(__dirname,'./resources/export/BasePlatForm.xlsx'))){
        fs.unlinkSync(path.resolve(__dirname,'./resources/export/BasePlatForm.xlsx'));
    } 
    if(fs.existsSync(path.resolve(__dirname,'./resources/export/BasePlatForm_增量.xlsx'))){
        fs.unlinkSync(path.resolve(__dirname,'./resources/export/BasePlatForm_增量.xlsx'));
    }    
    xlsx.writeFile(workbook,'./resources/export/BasePlatForm.xlsx');
    xlsx.writeFile(workbook_inc,'./resources/export/BasePlatForm_增量.xlsx');

    
    addTitle('./resources/export/BasePlatForm.xlsx')
    addTitle('./resources/export/BasePlatForm_增量.xlsx')
    function addTitle(filePath){
        let workbook1 = new ExcelJS.Workbook();
        workbook1.xlsx.readFile(filePath).then(()=>{
            let RelationSheet = workbook1.getWorksheet('Relation')
            RelationSheet.getCell('A1').note = '{"columnName":"son","columnTranslationMapper":{"columnName":"son","translation":"子资源编码"}}'
            RelationSheet.getCell('B1').note = '{"columnName":"father","columnTranslationMapper":{"columnName":"father","translation":"父资源编码"}}'
            let ResourceSheet = workbook1.getWorksheet('Resource')
            ResourceSheet.getCell('A1').note = '{"columnName":"appId","columnTranslationMapper":{"columnName":"appId","translation":"应用ID"}}'
            ResourceSheet.getCell('B1').note = '{"columnName":"resourceName","columnTranslationMapper":{"columnName":"resourceName","translation":"资源名称"}}'
            ResourceSheet.getCell('C1').note = '{"columnName":"resourceCode","columnTranslationMapper":{"columnName":"resourceCode","translation":"资源编码"}}'
            ResourceSheet.getCell('D1').note = '{"columnName":"resourceType","columnTranslationMapper":{"columnName":"resourceType","translation":"资源类型"}}'
            ResourceSheet.getCell('E1').note = '{"columnName":"level","columnTranslationMapper":{"columnName":"level","translation":"资源等级"}}'
            ResourceSheet.getCell('F1').note = '{"columnName":"resourceUrl","columnTranslationMapper":{"columnName":"resourceUrl","translation":"资源访问路径"}}'
            ResourceSheet.getCell('G1').note = '{"columnName":"icon","columnTranslationMapper":{"columnName":"icon","translation":"资源ICO"}}'
            ResourceSheet.getCell('H1').note = '{"columnName":"sortValue","columnTranslationMapper":{"columnName":"sortValue","translation":"菜单排序序列号"}}'
     
            let InternationalizationSheet = workbook1.getWorksheet('Internationalization')
            InternationalizationSheet.getCell('A1').note = '{"columnName":"resourceCode","columnTranslationMapper":{"columnName":"resourceCode","translation":"资源编码"}}'
            InternationalizationSheet.getCell('B1').note = '{"columnName":"zh_CN","columnTranslationMapper":{"columnName":"zh_CN","translation":"中文翻译"}}'
            InternationalizationSheet.getCell('C1').note = '{"columnName":"en_US","columnTranslationMapper":{"columnName":"en_US","translation":"英文翻译"}}'
            InternationalizationSheet.getCell('D1').note = '{"columnName":"es_ES","columnTranslationMapper":{"columnName":"es_ES","translation":"西班牙语"}}'
            InternationalizationSheet.getCell('E1').note = '{"columnName":"pt_BR","columnTranslationMapper":{"columnName":"pt_BR","translation":"葡萄牙语"}}'
            InternationalizationSheet.getCell('F1').note = '{"columnName":"fr_FR","columnTranslationMapper":{"columnName":"fr_FR","translation":"法语"}}'
            InternationalizationSheet.getCell('G1').note = '{"columnName":"zh_HK","columnTranslationMapper":{"columnName":"zh_HK","translation":"繁體中文(香港)"}}'
            InternationalizationSheet.getCell('H1').note = '{"columnName":"ru_RU","columnTranslationMapper":{"columnName":"ru_RU","translation":"俄罗斯语"}}'
            InternationalizationSheet.getCell('I1').note = '{"columnName":"de_DE","columnTranslationMapper":{"columnName":"de_DE","translation":"德语"}}'
            InternationalizationSheet.getCell('J1').note = '{"columnName":"ja_JP","columnTranslationMapper":{"columnName":"ja_JP","translation":"日语"}}'
            InternationalizationSheet.getCell('K1').note = '{"columnName":"th_TH","columnTranslationMapper":{"columnName":"th_TH","translation":"泰语"}}'
            InternationalizationSheet.getCell('L1').note = '{"columnName":"ar_EG","columnTranslationMapper":{"columnName":"ar_EG","translation":"阿拉伯语"}}'
     
            return workbook1.xlsx.writeFile(filePath)
         })
    }
    
    
    // 输出格式化菜单
    let str = [],line='----';
    function formatMenu(menuArr, level=0){
        menuArr.forEach(menu => {
            const prefix = new Array(level).fill(1).reduce((total,index)=>{total+=line;return total},'');
            const childs = menu.childs;
            str.push(prefix+'menu '+menu.resourceName);
            if(menu.childs===undefined){
                formatPage(menu.pageCode,pages,level+1);
            }
            if (childs && childs.length) {
                formatMenu(childs, level + 1);
            }
        })
    }
    formatMenu(menus);
    // 格式化输出页面
    function formatPage(parent_code,getPages,level){
        getPages.forEach(page=>{
            const prefix = new Array(level).fill(1).reduce((total,index)=>{total+=line;return total},'');
            const childs = page.childs;
            // parent 是菜单，判断parent_code===page.resourceCode;
            if(page.resourceCode===parent_code){
                str.push(prefix+'page '+page.resourceName);
                if(page.actions && page.actions.length){
                    page.actions.forEach(item=>{
                        str.push(prefix+'----action '+item.resourceName);
                    })
                }
                if(page.childs && childs.length){
                    page.childs.forEach(item=>{
                        str.push(prefix+'----page '+item.resourceName);
                        if(item.actions && item.actions.length){
                            item.actions.forEach(elem=>{
                                str.push(prefix+'--------action '+elem.resourceName);
                            })
                        }
                    })
                }
            }
            if(childs && childs.length){
                
            }
        })
    }
    function formatChildPage(pageArr,level){
        pageArr.forEach(page=>{
            const prefix = new Array(level).fill(1).reduce((total,index)=>{total+=line;return total},'');
            const childs = page.childs;
            str.push(prefix+'page '+page.resourceName);
            debugger;
            if(childs && childs.length){
                formatChildPage(childs,level+1)
            }
        })
    }

    // pages.forEach(page=>{
    //     if(page.resourceName==="电子路单表"){
    //         console.log(page.childs);
    //         console.log(page);
    //     }
    // })
    //formatPage(pages);
    fs.writeFileSync('./resources/export/BasePlatForm.txt', str.join('\n'))

};


const generateForBus = async () => {
    const pages = await getPages('');
    const menus = await getAllMemus();
    generateSqlForSource({
        appId,
        tenantId,
        menus: JSON.parse(JSON.stringify(menus)),
        pages: JSON.parse(JSON.stringify(pages)),
    });
};

const main = async () => {   
    await generateForBus();
};

main();
