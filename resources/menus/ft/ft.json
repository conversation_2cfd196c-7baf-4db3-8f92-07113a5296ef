[{"resourceName": "FT Vision", "resourceCode": "@BASE:@ENTRY:FTVISION", "resourceUrl": "/ftv", "childs": [{"resourceName": "实时监控", "resourceCode": "@BASE:@MENU:MONITORING.CENTER", "childs": [{"resourceName": "车辆监控", "resourceCode": "@BASE:@MENU:REALTIME.MONITORING", "pageCode": "@base:@page:realtime.monitoring", "resourceUrl": "/realtime-monitoring"}, {"resourceName": "实时视频", "resourceCode": "@BASE:@MENU:REALTIME.VIDEO", "pageCode": "@base:@page:realtime.video", "resourceUrl": "/realtime-video"}, {"resourceName": "视频回放", "resourceCode": "@BASE:@MENU:PLAYBACKMIX", "pageCode": "@base:@page:playback.mix", "resourceUrl": "/playback-mix"}, {"resourceName": "设备回放", "resourceCode": "@BASE:@MENU:PLAYBACKDEVICE", "pageCode": "@base:@page:playback.device", "resourceUrl": "/playback-device"}, {"resourceName": "服务器回放", "resourceCode": "@BASE:@MENU:PLAYBACKSERVE", "pageCode": "@base:@page:playback.serve", "resourceUrl": "/playback-serve"}, {"resourceName": "视频墙", "resourceCode": "@BASE:@MENU:VIDEO.WALL", "pageCode": "@base:@page:video.wall", "resourceUrl": "/video-wall"}, {"resourceName": "风险任务", "resourceCode": "@BASE:@MENU:RISK.TASK", "pageCode": "@base:@page:risk.task", "resourceUrl": "/risk-task"}, {"resourceName": "任务记录", "resourceCode": "@BASE:@MENU:TASK.RECORD", "pageCode": "@base:@page:risk.task.record", "resourceUrl": "/task-record"}]}, {"resourceName": "报警中心", "resourceCode": "@BASE:@MENU:ALARM.CENTER", "childs": [{"resourceName": "报警列表", "resourceCode": "@BASE:@MENU:ALARM.LIST", "pageCode": "@base:@page:alarm.list", "resourceUrl": "/alarm-list"}, {"resourceName": "证据列表", "resourceCode": "@BASE:@MENU:EVIDENCE.LIST", "pageCode": "@base:@page:evidence.list", "resourceUrl": "/evidence-list"}]}, {"resourceName": "视频中心", "resourceCode": "@BASE:@MENU:VIDEO.CENTER", "childs": [{"resourceName": "视频库", "resourceCode": "@BASE:@MENU:VIDEO.LIST", "pageCode": "@base:@page:video.list", "resourceUrl": "/video-list"}]}, {"resourceName": "系统功能", "resourceCode": "@BASE:@MENU:SYSTEM.CENTER", "childs": [{"resourceName": "人脸异常待办", "resourceCode": "@BASE:@MENU:FACE.ABNORMAL.TODO", "pageCode": "@base:@page:face.abnormal", "resourceUrl": "/face-abnormal"}, {"resourceName": "未知司机报警", "resourceCode": "@BASE:@MENU:UNKNOWN.DRIVER.ALARM", "pageCode": "@base:@page:unknown.driver.alarm", "resourceUrl": "/system/unknown-driver-alarm"}, {"resourceName": "人脸对比记录", "resourceCode": "@BASE:@MENU:SYSTEM.FACE.CONTRAST.RECORD", "pageCode": "@base:@page:face.contrast.record", "resourceUrl": "/system/face-contrast-record"}]}, {"resourceName": "流量中心", "resourceCode": "@BASE:@MENU:FLOW.CENTER", "childs": [{"resourceName": "流量总览", "resourceCode": "@BASE:@MENU:FLOW.PREVIEW", "pageCode": "@base:@page:flow.preview", "resourceUrl": "/flow/flow-preview"}, {"resourceName": "设备流量管理", "resourceCode": "@BASE:@MENU:FLOW.MANAGE", "pageCode": "@base:@page:flow.manage", "resourceUrl": "/flow/flow-manage"}, {"resourceName": "设备流量设置", "resourceCode": "@BASE:@MENU:FLOW.SETTING", "resourceUrl": "/flow/flow-setting", "pageCode": "@base:@page:flow.setting", "resourceChargeType": "2"}, {"resourceName": "用户流量管理", "resourceCode": "@BASE:@MENU:USER.FLOW.MANAGE", "resourceUrl": "/flow/user-flow-manage", "pageCode": "@base:@page:user.flow.manage", "resourceChargeType": "2"}]}]}, {"resourceName": "FT Manager", "resourceCode": "@BASE:@ENTRY:FTMANAGER", "resourceUrl": "/ftm", "childs": [{"resourceName": "基础数据", "resourceCode": "@BASE:@MENU:BASIC.DATA", "childs": [{"resourceName": "车组管理", "resourceCode": "@BASE:@MENU:GROUP.MANAGE", "pageCode": "@base:@page:group.manage", "resourceUrl": "/group-manage"}, {"resourceName": "车辆管理", "resourceCode": "@BASE:@MENU:VEHICLE.MANAGE", "pageCode": "@base:@page:vehicle.manage", "resourceUrl": "/vehicle-manage"}, {"resourceName": "司机管理", "resourceCode": "@BASE:@MENU:DRIVER.MANAGE", "pageCode": "@base:@page:driver.manage", "resourceUrl": "/driver-manage"}, {"resourceName": "设备管理", "resourceCode": "@BASE:@MENU:DEVICE.MANAGE", "pageCode": "@base:@page:device.manage", "resourceUrl": "/device-manage"}, {"resourceName": "下级平台管理", "resourceCode": "@BASE:@MENU:SUB.PLATFORM.MANAGE", "pageCode": "@base:@page:sub.platform.manage", "resourceUrl": "/sub-platform-manage", "resourceChargeType": "2"}, {"resourceName": "上级平台管理", "resourceCode": "@BASE:@MENU:SUPER.PLATFORM.MANAGE", "pageCode": "@base:@page:super.platform.manage", "resourceUrl": "/super-platform-manage", "resourceChargeType": "2"}, {"resourceName": "License绑定查询", "resourceCode": "@BASE:@MENU:LICENSE.BIND.QUERY", "pageCode": "@base:@page:license.bind.query", "resourceUrl": "/license-bind-query", "resourceChargeType": "2"}]}, {"resourceName": "电子围栏", "resourceCode": "@BASE:@MENU:FENCE", "childs": [{"resourceName": "出入围栏", "resourceCode": "@BASE:@MENU:FENCE.INOUT", "pageCode": "@base:@page:fence.base", "resourceUrl": "/fence/base"}, {"resourceName": "限速线路", "resourceCode": "@BASE:@MENU.FENCE.SPEED", "pageCode": "@base:@page:fence.speed", "resourceUrl": "/fence/speed"}, {"resourceName": "限时线路", "resourceCode": "@BASE:@MENU.FENCE.TIME", "pageCode": "@base:@page:fence.time", "resourceUrl": "/fence/time"}]}, {"resourceName": "平台电子围栏", "resourceCode": "@BASE:@MENU:PLATFORM.FENCE", "resourceChargeType": "2", "childs": [{"resourceName": "区域管理", "resourceCode": "@BASE:@MENU.AREA.MANAGE", "pageCode": "@base:@page:fence.area.manage", "resourceUrl": "/fence/area-manage", "resourceChargeType": "2"}, {"resourceName": "线路管理", "resourceCode": "@BASE:@MENU.ROUTE.MANAGE", "pageCode": "@base:@page:fence.line.manage", "resourceUrl": "/fence/line-manage", "resourceChargeType": "2"}, {"resourceName": "围栏监控", "resourceCode": "@BASE:@MENU.FENCE.MONITOR", "pageCode": "@base:@page:fence.monitor", "resourceUrl": "/fence-monitor", "resourceChargeType": "2"}]}, {"resourceName": "系统功能", "resourceCode": "@BASE:@MENU:SYSTEM.FEATURE", "childs": [{"resourceName": "数据清理", "resourceCode": "@BASE:@MENU:SYSTEM.DATA.CLEAR", "pageCode": "@base:@page:system.data.clear", "resourceUrl": "/system/data-clear"}, {"resourceName": "保存有效期设置查询", "resourceCode": "@BASE:@MENU:SYSTEM.RETENTION.PERIOD.SETTINGS.QUERY", "pageCode": "@base:@page:system.retention.period.setting.query", "resourceChargeType": "2", "resourceUrl": "/system/retention-period-setting-query"}, {"resourceName": "空间管理", "resourceCode": "@BASE:@MENU:TENANT.SPACE.MANAGE", "resourceUrl": "/space-manage", "pageCode": "@base:@page:space.manage", "resourceChargeType": "2"}, {"resourceName": "租户详情(BB应用)", "resourceCode": "@BASE:@MENU:TENANT.DETAILS.BB", "resourceUrl": "/tenant-detail/detail", "pageCode": "@base:@page:tanant.detail.bb", "resourceChargeType": "2"}, {"resourceName": "视频定时下载", "resourceCode": "@BASE:@MENU:SYSTEM.FEATURE.VIDEO.REGULAR.DOWNLOAD", "resourceUrl": "/system/video-regular-download", "pageCode": "@base:@page:system.video.regular.download"}, {"resourceName": "区域查车", "resourceCode": "@BASE:@MENU:SYSTEM.REGION.VEHICLE.FIND", "pageCode": "@base:@page:system.region.vehicle.find", "resourceUrl": "/system/region-vehicle-find"}, {"resourceName": "查车任务", "resourceCode": "@BASE:@MENU:SYSTEM.REGION.VEHICLE.FIND.LIST", "pageCode": "@base:@page:system.region.vehicle.find:list", "resourceUrl": "/system/region-vehicle-find-list"}, {"resourceName": "消息定时下发", "resourceCode": "@BASE:@MENU:SYSTEM.FEATURE.MESSAGE.REGULAR.DISTRIBUTE", "resourceUrl": "/system/message-regular-distribute", "pageCode": "@base:@page:system.message.regular.distribute"}]}, {"resourceName": "任务中心", "resourceCode": "@BASE:@MENU:TASK.CENTER", "childs": [{"resourceName": "文本下发任务", "resourceCode": "@BASE:@MENU:TASK.CENTER.ISSUE.TEXT", "pageCode": "@base:@page:issue.text", "resourceUrl": "/task/issue-text"}, {"resourceName": "音频下发任务", "resourceCode": "@BASE:@MENU:TASK.CENTER.ISSUE.AUDIO", "pageCode": "@base:@page:issue.audio", "resourceUrl": "/task/issue-audio"}, {"resourceName": "参数下发任务", "resourceCode": "@BASE:@MENU:TASK.CENTER.PARAMETER.ISSUE", "pageCode": "@base:@page:parameter.issue", "resourceUrl": "/task-center/parameter-issue"}, {"resourceName": "围栏下发任务", "resourceCode": "@BASE:@MENU:TASK.CENTER.ELECTRIC.FENCE.SEND", "pageCode": "@base:@page:task.center.electronic", "resourceUrl": "/task/electronic-fence"}, {"resourceName": "数据清理任务", "resourceCode": "@BASE:@MENU:TASK.CENTER.DATA.CLEAR", "pageCode": "@base:@page:task.center.data.clear", "resourceUrl": "/task-center/clear"}]}]}]