[{"resourceName": "BasePlatform", "resourceCode": "@BASE:@ENTRY:BASE", "resourceUrl": "/base", "childs": [{"resourceName": "用户中心", "resourceCode": "@BASE:@MENU:USER", "childs": [{"resourceName": "用户管理", "resourceCode": "@BASE:@MENU:USER.MANAGE", "resourceUrl": "/user-manage", "pageCode": "@base:@page:user.manage"}, {"resourceName": "用户设置", "resourceCode": "@BASE:@MENU:USER.SETTING", "resourceUrl": "/user-center/user-setting", "pageCode": "@base:@page:user.setting"}, {"resourceName": "用户设置查询", "resourceCode": "@BASE:@MENU:USER.SETTING.QUERY", "resourceUrl": "/user-center/user-setting-query", "pageCode": "@base:@page:user.setting.query", "resourceChargeType": "2"}]}, {"resourceName": "权限中心", "resourceCode": "@BASE:@MENU:PERMISSION", "childs": [{"resourceName": "角色管理", "resourceCode": "@BASE:@MENU:PERMISSION.ROLE.MANAGE", "resourceUrl": "/role-manage", "pageCode": "@base:@page:role.manage"}, {"resourceName": "角色授权", "resourceCode": "@BASE:@MENU:PERMISSION.ROLE.AUTHORIZE", "resourceUrl": "/role-authorize", "pageCode": "@base:@page:role.authorize"}, {"resourceName": "数据授权", "resourceCode": "@BASE:@MENU:PERMISSION.DATA.AUTHORIZE", "resourceUrl": "/data-authorize", "pageCode": "@base:@page:data.authorize"}, {"resourceName": "权限查询", "resourceCode": "@BASE:@MENU:PERMISSION.QUERY", "resourceUrl": "/permission-query", "pageCode": "@base:@page:permission.query"}, {"resourceName": "服务管理", "resourceCode": "@BASE:@MENU:SERVER.MANAGE", "resourceUrl": "/server-manage", "pageCode": "@base:@page:server.manage"}]}, {"resourceName": "报警设置", "resourceCode": "@BASE:@MENU:ALARM.SETTING", "childs": [{"resourceName": "报警设置", "resourceCode": "@BASE:@MENU:ALARM.CLASSIFY.SETTING", "resourceUrl": "/alarm-setting", "pageCode": "@base:@page:alarm.setting"}, {"resourceName": "报警提示音", "resourceCode": "@BASE:@MENU:ALARM.SOUND.SETTING", "resourceUrl": "/strategy/default/alarm-sound", "pageCode": "@base:@page:setting.alarm.sound", "resourceChargeType": "2"}, {"resourceName": "证据上传设置", "resourceCode": "@BASE:@MENU:ALARM.EVIDENCE.UPLOAD", "resourceUrl": "/strategy/default/evidence/detail", "pageCode": "@base:@page:setting.default.evidence"}, {"resourceName": "报警通知设置", "resourceCode": "@BASE:@MENU:ALARM.MESSAGE", "resourceUrl": "/strategy/default/alarm-linkage/detail", "pageCode": "@base:@page:setting.default.alarm.linkage"}, {"resourceName": "报警处理设置", "resourceCode": "@BASE:@MENU:ALARM.HANDLE", "resourceUrl": "/strategy/default/auto-handle/detail", "pageCode": "@base:@page:setting.default.auto.handle"}, {"resourceName": "联动设置", "resourceCode": "@BASE:@MENU:ALARM.LINK.SETTING", "resourceUrl": "/alarm-setting/link-setting", "pageCode": "@base:@page:setting.default.config"}, {"resourceName": "报警设置查询", "resourceCode": "@BASE:@MENU:ALARM.SETTING.QUERY", "resourceUrl": "/alarm-setting/query", "pageCode": "@base:@page:alarm.setting.query", "resourceChargeType": "2"}]}, {"resourceName": "抓拍设置", "resourceCode": "@BASE:@MENU:CAPTURE.SETTING", "childs": [{"resourceName": "图片抓拍设置", "resourceCode": "@BASE:@MENU:CAPTURE.SETTING.PICTURE", "resourceUrl": "/strategy/default/picture-capture/detail", "pageCode": "@base:@page:setting.default.picture.capture"}, {"resourceName": "人脸对比设置", "resourceCode": "@BASE:@MENU:CAPTURE.SETTING.FACE.CONTRAST", "resourceUrl": "/strategy/default/face-contrast/detail", "pageCode": "@base:@page:setting.default.face.contrast"}, {"resourceName": "抓拍设置", "resourceCode": "@BASE:@MENU:CAPTURE.SETTING.CAPTURE", "resourceUrl": "/capture-setting/capture", "pageCode": "@base:@page:capture.setting.capture"}, {"resourceName": "抓拍设置查询", "resourceCode": "@BASE:@MENU:CAPTURE.SETTING.QUERY", "resourceUrl": "/capture-setting/query", "pageCode": "@base:@page:capture.setting.query", "resourceChargeType": "2"}]}, {"resourceName": "通用设置", "resourceCode": "@BASE:@MENU:GENERAL.SETTING", "childs": [{"resourceName": "主题设置", "resourceCode": "@BASE:@MENU:THEME.SETTING", "resourceUrl": "/theme-setting", "pageCode": "@base:@page:theme.setting"}, {"resourceName": "下发内容管理", "resourceCode": "@BASE:@MENU:DISTRIBUTE.CONTENT", "resourceUrl": "/distribute-content", "pageCode": "@base:@page:distribute.content"}, {"resourceName": "标签管理", "resourceCode": "@BASE:@MENU:LABEL.MANAGE", "resourceUrl": "/label-manage", "pageCode": "@base:@page:label.manage"}, {"resourceName": "车辆类型管理", "resourceCode": "@BASE:@MENU:VEHICLE.TYPE.MANAGE", "resourceUrl": "/vehicle-type-manage", "pageCode": "@base:@page:vehicle.type.manage"}, {"resourceName": "发件箱设置", "resourceCode": "@BASE:@MENU:SEND.EMAIL.SETTING", "resourceUrl": "/strategy/default/email-sending/detail", "pageCode": "@base:@page:setting.default.email.sending"}, {"resourceName": "发件箱设置查询", "resourceCode": "@BASE:@MENU:EMAIL.SETTING.QUERY", "resourceUrl": "/email-setting/query", "pageCode": "@base:@page:email.setting.query", "resourceChargeType": "2"}, {"resourceName": "邮件模板", "resourceCode": "@BASE:@MENU:EMAIL.TEMPLATE", "resourceUrl": "/mail-template", "pageCode": "@base:@page:email.template"}, {"resourceName": "平台消息通知", "resourceCode": "@BASE:@MENU:PLATFORM.MESSAGE", "resourceUrl": "/platform-message", "pageCode": "@base:@page:platform.message"}, {"resourceName": "通道设置", "resourceCode": "@BASE:@MENU:CHANNEL.SET", "resourceUrl": "/CH-set", "pageCode": "@base:@page:channel.setting"}, {"resourceName": "通道设置查询", "resourceCode": "@BASE:@MENU:CHANNEL.SETTING.QUERY", "resourceUrl": "/CH-set/query", "pageCode": "@base:@page:channel.setting.query", "resourceChargeType": "2"}, {"resourceName": "报警展示设置", "resourceChargeType": "2", "resourceCode": "@BASE:@MENU:ALARM.SHOW.SET", "resourceUrl": "/alarm-show-setting", "pageCode": "@base:@page:alarm.show.setting"}, {"resourceName": "多语言管理", "resourceCode": "@BASE:@MENU:MULTIPLE.LANGUAGES", "resourceUrl": "/multiple-languages", "pageCode": "@base:@page:multiple.languages"}, {"resourceName": "密钥管理", "resourceCode": "@BASE:@MENU:SECRETKEY.MANAGE", "resourceUrl": "/secretkey-manage", "pageCode": "@base:@page:secretkey.manage"}, {"resourceName": "报警类型管理", "resourceCode": "@BASE:@MENU:ALARM.TYPE.MANAGE", "resourceUrl": "/alarm-type-manage", "pageCode": "@base:@page:alarm.type.manage"}, {"resourceName": "参数设置", "resourceCode": "@BASE:@MENU:PARAMETER.SETTING", "resourceUrl": "/parameter-setting", "pageCode": "@base:@page:parameter.setting"}, {"resourceName": "语言设置", "resourceCode": "@BASE:@MENU:LANGUAGE.SETTING", "resourceUrl": "/language-setting", "pageCode": "@base:@page:language.setting"}, {"resourceName": "协议设置", "resourceCode": "@BASE:@MENU:PROTOCOL.SETTING", "resourceUrl": "/protocol-setting", "pageCode": "@base:@page:protocol.setting"}, {"resourceName": "停车设置", "resourceCode": "@BASE:@MENU:PACKING.SETTING", "resourceUrl": "/parking-setting", "pageCode": "@base:@page:packing.setting"}, {"resourceName": "AP设置", "resourceCode": "@BASE:@MENU:AP.SETTING", "resourceUrl": "/ap-setting", "pageCode": "@base:@page:ap.setting"}, {"resourceName": "系统更新", "resourceCode": "@BASE:@MENU:SYSTEM.UPDATE", "resourceUrl": "/system-update", "pageCode": "@base:@page:system.update", "resourceChargeType": "2"}, {"resourceName": "白名单密钥", "resourceCode": "@BASE:@MENU:WHITE.LIST.KEY", "resourceUrl": "/white-list-key", "pageCode": "@base:@page:white.list.key"}]}, {"resourceName": "日志管理", "resourceCode": "@BASE:@MENU:LOG.MANAGE", "childs": [{"resourceName": "登录日志", "resourceCode": "@BASE:@MENU:LOG.MANAGE.LOGIN", "resourceUrl": "/login-log", "pageCode": "@base:@page:login.log.manage"}]}, {"resourceName": "应用中心", "resourceCode": "@BASE:@MENU:APPLICATION", "childs": [{"resourceName": "应用管理", "resourceCode": "@BASE:@MENU:APPLICATION.MANAGE", "resourceUrl": "/applicationManage", "pageCode": "@base:@page:application.manage"}]}, {"resourceName": "租户中心", "resourceCode": "@BASE:@MENU:TANANT", "childs": [{"resourceName": "租户详情", "resourceCode": "@BASE:@MENU:TANANT.DETAIL", "resourceUrl": "/tenant-detail", "pageCode": "@base:@page:tanant.detail"}, {"resourceName": "子租户管理", "resourceCode": "@BASE:@MENU:TANANT.SUB.MANAGE", "resourceUrl": "/sub-tenant-manage", "pageCode": "@base:@page:sub.tanant.manage"}, {"resourceName": "租户账单", "resourceCode": "@BASE:@MENU:TANANT.SETTLEMENT.BILL", "resourceUrl": "/tenant-settlement-bill", "pageCode": "@base:@page:tanant.settlement.bill"}, {"resourceName": "租户模板", "resourceCode": "@BASE:@MENU:TENANT.CENTER.TEMPLATE", "resourceUrl": "/tenant-center/tenant-template", "pageCode": "@base:@page:tenant.template"}]}]}]