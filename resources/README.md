目录结构
```shell
.
├── README.md
├── export                          # 导出文件(需要关注)
│   ├── BaseBusiness.txt            # 通过代码生成的菜单页面操作结构示意图
│   ├── BaseBusiness.xlsx           # 生成的excel资源文件
│   ├── BasePlatForm.txt
│   ├── BasePlatForm.xlsx
│   ├── BP差异报告.txt               # 编写的JSON数据与钉钉文档中的数据的差异
│   └── FT差异报告.txt 
├── menus                            # 菜单资源文件
│   ├── base                         # BasePlatForm入口下的所有菜单资源
│   ├── ft                           # FT入口下的所有菜单资源
├── pages                            # 页面操作资源文件
│   ├── base                         
│   ├── ft                           
├── BaseBusiness.xlsx                # 原文件资源(添加新资源时需在Internationalization表格中添加翻译,可定期把export的xlsx更新为这两个文件夹内容)
├── BasePlatform.xlsx
├── setBaseCode.js                   # 使用 node ./resources/setBaseCode.js 生成上述export内文件
├── setFtCode.js                
```