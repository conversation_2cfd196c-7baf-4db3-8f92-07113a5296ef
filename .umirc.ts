import { defineConfig } from 'umi';
const ExtendSyntaxLessPlugin = require('@streamax/less-plugin-syntax-extend');
const LessFunctionOverrideLessPlugin = require('@streamax/less-plugin-dynamic-variable');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MaterialPlugin = require('@streamax/material/webpack-plugin');

const applyLoaders = (rule, isCSSModules) => {
    rule.use('extract-css-loader')
        .loader(
            require('@umijs/bundler-webpack/lib/webpack/plugins/mini-css-extract-plugin')
                .loader,
        )
        .options({
            publicPath: './',
            hmr: true,
        });
    rule.use('css-loader')
        .loader(require.resolve('@umijs/deps/compiled/css-loader'))
        .options({
            importLoaders: 1,
            ...(isCSSModules
                ? {
                      modules: {
                          localIdentName: '[local]___[hash:base64:5]',
                      },
                  }
                : {}),
        });
    rule.use(require.resolve('@umijs/deps/compiled/less-loader'))
        .loader(require.resolve('@umijs/deps/compiled/less-loader'))
        .options({
            modifyVars: { 'root-entry-name': 'variable' },
            javascriptEnabled: true,
            plugins: [
                new LessFunctionOverrideLessPlugin(),
                new ExtendSyntaxLessPlugin(),
            ],
        });
};

// @ts-ignore
export default defineConfig({
    starry: {
        pages: 'runtime',
        layout: 'hasui',
    },
    ServiceWorker: [
        // 匹配所有行业业务接口
        '/gateway/.*',
        // '/base-server-service/api/v1/tenant/access/list',
        // '/base-server-service/api/v1/user/info',
        // '/base-server-service/api/v1/resource/entrance/query',
        // '/base-config-service/api/v1/language/front/list/new',
        // '/base-server-service/api/v1/user/real/detail',
        // '/base-config-service/api/v1/configure/efficient',
        // '/base-server-service/api/v1/area/time/zone/detail',
        // '/base-server-service/api/v1/area/time/zone/summer/time/page',
        // '/base-server-service/api/v1/user/resource/list',
    ],
    webpack5: {
        lazyCompilation: {},
    },
    chainWebpack: (config) => {
        const rule = config.module.rule('less').test(/\.(less)(\?.*)?$/);
        applyLoaders(rule.oneOf('css-modules').resourceQuery(/modules/), true);
        applyLoaders(rule.oneOf('css'), false);
        config.plugin('copy').use(CopyWebpackPlugin, [
            {
                patterns: [
                    {
                        from: `${process.cwd()}/node_modules/@streamax/streamap-js/dist`,
                        to: `${process.cwd()}/dist`,
                        globOptions: {
                            ignore: ['**/demo', '**/doc'],
                        },
                    },
                ],
            },
        ]);
        config.module
            .rule('worker')
            .test(/\.worker.js$/)
            .use('worker')
            .loader('worker-loader')
            .options({
                inline: 'no-fallback',
            })
            .end();
        config.module
            .rule('pcm')
            .test(/\.(pcm)$/)
            .use('file')
            .loader('file-loader')
            .end();
        config.module
            .rule('mp3')
            .test(/\.(mp3|wasm)$/)
            .use('file')
            .loader('file-loader')
            .end();
        config.plugin('material-plugin').use(MaterialPlugin);

        return config;
    },
    extraBabelPlugins: [
        ['@streamax/babel-plugin-add-sharing-props'],
        [require('@streamax/material/babel-plugin')],
    ],
    plugins: [
        './node_modules/@streamax/iframe-sdk/lib/umi-plugin-iframe-loaded-trigger.js'
    ]
});
