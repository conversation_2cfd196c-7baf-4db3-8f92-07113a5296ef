# 技术上下文

## 技术栈

1. 核心框架
   - React 17.x
   - UMI 3.x
   - DVA 状态管理
   - TypeScript 4.x
   - Ant Design 4.x

2. UI 组件库
   - @streamax/poppy (基于 antd 4.18.0)
   - 自定义业务组件
   - 响应式设计
   - 主题定制

3. 视频相关
   - @streamax/video-sdk
   - WebRTC
   - H5 播放器
   - 视频编解码

4. 地图相关
   - 地图 SDK
   - 轨迹回放
   - 地理编码
   - 电子围栏

## 开发环境

1. 构建工具
   - Webpack 5.x
   - UMI 构建配置
   - ESLint
   - Prettier
   - TypeScript

2. 开发工具
   - VSCode
   - Git
   - Chrome DevTools
   - 性能监控工具

3. 调试工具
   - React DevTools
   - Redux DevTools
   - Chrome 控制台
   - 网络分析工具

4. 测试工具
   - Jest
   - React Testing Library
   - E2E 测试
   - 性能测试

## 项目依赖

1. 基础依赖
   ```json
   {
     "react": "^17.0.0",
     "react-dom": "^17.0.0",
     "umi": "^3.5.0",
     "antd": "^4.18.0",
     "typescript": "^4.1.0"
   }
   ```

2. 业务依赖
   ```json
   {
     "@streamax/poppy": "latest",
     "@streamax/video-sdk": "latest",
     "@streamax/starry-i18n": "latest",
     "@streamax/starry-resource": "latest"
   }
   ```

3. 开发依赖
   ```json
   {
     "@types/react": "^17.0.0",
     "@typescript-eslint/eslint-plugin": "^5.0.0",
     "eslint": "^7.32.0",
     "prettier": "^2.5.0",
     "husky": "^7.0.0"
   }
   ```

## 开发规范

1. 代码规范
   - TypeScript 强类型
   - ESLint 检查
   - Prettier 格式化
   - Git Commit 规范

2. 目录规范
   ```
   src/
     ├── components/        # 通用组件
     ├── runtime-pages/     # 页面组件
     ├── runtime-lib/       # 核心库
     ├── modules/          # 业务模块
     ├── hooks/            # 通用 Hooks
     ├── utils/            # 工具函数
     ├── services/         # API 服务
     └── assets/          # 静态资源
   ```

3. 组件规范
   - 功能单一
   - 职责明确
   - 可复用性
   - TypeScript 类型

4. 命名规范
   - 组件: PascalCase
   - 文件: kebab-case
   - 变量: camelCase
   - 常量: UPPER_CASE

## 构建配置

1. UMI 配置
   ```typescript
   export default {
     nodeModulesTransform: {
       type: 'none',
     },
     routes: [
       { path: '/', component: '@/pages/index' },
     ],
     fastRefresh: {},
   }
   ```

2. TypeScript 配置
   ```json
   {
     "compilerOptions": {
       "target": "esnext",
       "module": "esnext",
       "moduleResolution": "node",
       "jsx": "react-jsx",
       "strict": true
     }
   }
   ```

3. ESLint 配置
   ```json
   {
     "extends": [
       "eslint:recommended",
       "plugin:@typescript-eslint/recommended",
       "plugin:react/recommended"
     ]
   }
   ```

## 部署环境

1. 开发环境
   - 本地开发服务器
   - Mock 数据服务
   - 热更新
   - 源码映射

2. 测试环境
   - 测试服务器
   - 测试数据库
   - 性能监控
   - 错误追踪

3. 生产环境
   - CDN 加速
   - 负载均衡
   - 安全防护
   - 监控告警

## 性能优化

1. 加载优化
   - 路由懒加载
   - 组件按需加载
   - 资源预加载
   - 代码分割

2. 渲染优化
   - 虚拟列表
   - 懒加载图片
   - 防抖节流
   - 缓存优化

3. 网络优化
   - 接口缓存
   - 数据压缩
   - 请求合并
   - 断点续传

## 调试工具

1. 开发工具
   - VSCode 插件
   - Chrome 插件
   - 抓包工具
   - 性能分析

2. 监控工具
   - 错误监控
   - 性能监控
   - 用户行为
   - 资源监控

3. 测试工具
   - 单元测试
   - 集成测试
   - E2E 测试
   - 性能测试

## 持续集成

1. 代码管理
   - Git Flow
   - 分支策略
   - 代码审查
   - 版本管理

2. 自动化构建
   - CI/CD 流程
   - 自动化测试
   - 代码质量
   - 自动部署

3. 发布流程
   - 环境配置
   - 版本发布
   - 回滚机制
   - 监控告警
