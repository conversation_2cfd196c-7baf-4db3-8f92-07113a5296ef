# 前端代码规范

## 文件组织

1. 目录结构
   ```
   src/
     ├── components/        # 通用组件
     ├── runtime-pages/     # 页面组件
     ├── runtime-lib/       # 核心库
     ├── modules/          # 业务模块
     ├── hooks/            # 通用 Hooks
     ├── utils/            # 工具函数
     ├── services/         # API 服务
     └── assets/          # 静态资源
   ```

2. 命名规范
   - 文件夹: kebab-case
   - 组件文件: PascalCase
   - 工具文件: camelCase
   - 样式文件: 同主文件名

3. 文件结构
   - 单一职责
   - 合理分层
   - 逻辑内聚
   - 适度解耦

## 代码风格

1. TypeScript 规范
   ```typescript
   // 使用 interface 定义对象类型
   interface UserProps {
     name: string;
     age: number;
     role?: string;
   }

   // 使用 type 定义联合类型
   type Status = 'pending' | 'success' | 'error';

   // 使用 enum 定义枚举
   enum UserRole {
     Admin = 'admin',
     User = 'user',
     Guest = 'guest',
   }
   ```

2. React 规范
   ```typescript
   // 函数组件
   const UserCard: React.FC<UserProps> = ({ name, age, role = 'user' }) => {
     return (
       <div className="user-card">
         <h3>{name}</h3>
         <p>Age: {age}</p>
         <p>Role: {role}</p>
       </div>
     );
   };

   // Hooks 组件
   const useUser = (userId: string) => {
     const [user, setUser] = useState<UserProps | null>(null);
     const [loading, setLoading] = useState(false);

     useEffect(() => {
       // 实现逻辑
     }, [userId]);

     return { user, loading };
   };
   ```

3. 样式规范
   ```less
   // 使用 BEM 命名
   .block {
     &__element {
       &--modifier {
         // 样式
       }
     }
   }

   // 使用变量
   @primary-color: #1890ff;
   @border-radius: 4px;

   // 使用混入
   .text-ellipsis() {
     overflow: hidden;
     text-overflow: ellipsis;
     white-space: nowrap;
   }
   ```

## 组件规范

1. 组件结构
   ```typescript
   // 1. 导入
   import React, { useState, useEffect } from 'react';
   import type { FC } from 'react';
   import styles from './index.less';

   // 2. 类型定义
   interface Props {
     // ...
   }

   // 3. 常量定义
   const DEFAULT_VALUE = '';

   // 4. 组件实现
   const Component: FC<Props> = () => {
     // hooks
     // 处理函数
     // 渲染函数
     // 返回 JSX
   };

   // 5. 导出
   export default Component;
   ```

2. Hooks 规范
   ```typescript
   // 自定义 Hook
   const useCustomHook = (params: Params) => {
     // 状态定义
     const [state, setState] = useState();

     // 副作用
     useEffect(() => {
       // 实现逻辑
     }, [params]);

     // 返回数据和方法
     return {
       state,
       update: setState,
     };
   };
   ```

3. 性能优化
   ```typescript
   // 使用 memo
   const MemoComponent = React.memo(({ value }) => {
     return <div>{value}</div>;
   });

   // 使用 useCallback
   const handleClick = useCallback(() => {
     // 处理逻辑
   }, []);

   // 使用 useMemo
   const computedValue = useMemo(() => {
     // 计算逻辑
     return result;
   }, [dependencies]);
   ```

## 状态管理

1. DVA 规范
   ```typescript
   // model 定义
   export default {
     namespace: 'user',
     state: {
       list: [],
       loading: false,
     },
     effects: {
       *fetchList({ payload }, { call, put }) {
         // 实现逻辑
       },
     },
     reducers: {
       updateList(state, { payload }) {
         return { ...state, list: payload };
       },
     },
   };
   ```

2. Hooks 状态
   ```typescript
   // 局部状态
   const [value, setValue] = useState('');

   // 上下文状态
   const { state, dispatch } = useContext(Context);

   // 引用状态
   const valueRef = useRef('');
   ```

3. 数据流转
   ```typescript
   // 单向数据流
   const Parent = () => {
     const [value, setValue] = useState('');
     return <Child value={value} onChange={setValue} />;
   };

   // 状态提升
   const Container = () => {
     const [shared, setShared] = useState('');
     return (
       <>
         <CompA shared={shared} />
         <CompB onUpdate={setShared} />
       </>
     );
   };
   ```

## 工程规范

1. Git 规范
   ```
   feat: 新功能
   fix: 修复问题
   docs: 文档修改
   style: 代码格式修改
   refactor: 代码重构
   test: 测试用例修改
   chore: 其他修改
   ```

2. 注释规范
   ```typescript
   /**
    * 组件描述
    * @param {string} prop1 - 参数1描述
    * @param {number} prop2 - 参数2描述
    * @returns {ReactNode}
    */

   // 关键代码注释
   // TODO: 待办事项
   // FIXME: 待修复问题
   // NOTE: 说明注释
   ```

3. 测试规范
   ```typescript
   // 单元测试
   describe('Component', () => {
     it('should render correctly', () => {
       // 测试逻辑
     });
   });

   // 集成测试
   describe('Integration', () => {
     it('should work together', () => {
       // 测试逻辑
     });
   });
   ```

## 性能优化

1. 代码分割
   ```typescript
   // 路由分割
   const Home = lazy(() => import('./Home'));

   // 组件分割
   const Modal = lazy(() => import('./Modal'));
   ```

2. 渲染优化
   ```typescript
   // 虚拟列表
   const VirtualList = () => {
     return (
       <VirtualScroll
         itemCount={1000}
         itemSize={50}
         renderItem={({ index, style }) => (
           <div style={style}>Item {index}</div>
         )}
       />
     );
   };

   // 懒加载
   const LazyImage = () => {
     return <img loading="lazy" src={url} alt="" />;
   };
   ```

3. 缓存优化
   ```typescript
   // 数据缓存
   const useCache = (key: string) => {
     return useMemo(() => {
       // 缓存逻辑
     }, [key]);
   };

   // API 缓存
   const useApiCache = (url: string) => {
     return useQuery(url, {
       staleTime: 5000,
       cacheTime: 10000,
     });
   };
   ```

## 安全规范

1. XSS 防护
   ```typescript
   // 使用 dangerouslySetInnerHTML
   const Content = () => {
     return <div dangerouslySetInnerHTML={{ __html: sanitize(html) }} />;
   };

   // 输入过滤
   const handleInput = (value: string) => {
     return filterXSS(value);
   };
   ```

2. 类型安全
   ```typescript
   // 严格类型检查
   interface Data {
     readonly id: string;
     name: string;
     age?: number;
   }

   // 类型断言
   const getData = (data: unknown): Data => {
     assert(isData(data));
     return data;
   };
   ```

3. 数据加密
   ```typescript
   // 敏感数据加密
   const encryptData = (data: string) => {
     return encrypt(data);
   };

   // Token 处理
   const setToken = (token: string) => {
     localStorage.setItem('token', encrypt(token));
   };
# 前端代码规范 (Frontend Code Standards)

本文档基于公司的"前端代码规范V2.md"，整理了项目开发中需要遵循的前端编码规范和最佳实践。这些规范将在代码生成和检视过程中强制应用，以确保代码质量和一致性。

## 命名规范

### 项目和目录命名
- **【强制】** 项目命名采用小写方式，以中线（-）连接
- **【强制】** 目录命名采用小写方式，以中线（-）连接

### 文件命名
- **【强制】** 类文件、组件命名采用大驼峰
- **【强制】** 其他文件采用小驼峰命名
- **【强制】** 组件文件夹下index入口文件小写，非文件夹形式是大驼峰

### 变量和函数命名
- **【强制】** 常量命名采用大写方式，以下划线（_）连接
- **【强制】** 函数命名采用小驼峰命名
- **【强制】** 类命名采用大驼峰命名
- **【强制】** TypeScript类型定义采用大驼峰命名
- **【推荐】** 函数命名必须与承载的功能符合，如：
  - `can` - 判断是否可以执行某个权限（如 `canLogin`）
  - `has` - 判断是否含有某个值（如 `hasToken`）
  - `is` - 判断是否为某个值（如 `isShowModel`）
  - `get` - 获取某个值（如 `getUserId`）
  - `set` - 设置某个值（如 `setCookie`）
  - `load` - 加载某些数据（如 `loadList`）
  - `update` - 更新某些数据（如 `updateUserInfo`）
  - `del` - 删除某些数据（如 `delMenu`）

### 文件组织
- **【强制】** 带样式的组件必须以文件夹形式组织

## 常量定义

- **【强制】** 常量必须使用全大写字母和下划线命名，如 `const MAX_COUNT = 10`
- **【强制】** 枚举常量必须使用TypeScript枚举，如 `enum STATUS { PENDING = 'pending', SUCCESS = 'success'}`
- **【强制】** URL 路径（不包含参数部分，包括路由参数）必须定义为常量，不得硬编码在组件中
- **【强制】** History操作不允许使用`../`相对路径形式
- **【推荐】** 配置项必须定义为常量变量，如分页大小、请求超时时间等
- **【强制】** 常量必须有明确的类型定义，特别是在 TypeScript 项目中
- **【强制】** 常量命名必须表达其含义，避免使用无意义的名称如 `const X = 1`
- **【强制】** 常量文件必须有清晰的导出方式，优先使用命名导出而非默认导出
- **【强制】** 常量值不得在运行时修改，必须保持不变
- **【推荐】** 常量必须集中管理，放在专门的常量文件或目录中
- **【推荐】** 业务相关的常量必须按模块或功能分组

## 注释规范

- **【强制】** 函数注释必须多行注释，符合[JSDoc规范](https://www.jsdoc.com.cn/)
- **【强制】** 方法内部单行注释，在被注释语句上方另起一行，使用`//`注释
- **【强制】** 方法内部多行注释使用`/* */`注释，注意与代码对齐
- **【强制】** 所有的枚举类型字段必须要有注释，说明每个数据项的用途
- **【推荐】** 与其"半吊子"英文来注释，不如用中文注释把问题说清楚。专有名词与关键字保持英文原文即可
- **【推荐】** 代码修改的同时，注释也要进行相应的修改，尤其是参数、返回值、异常、核心逻辑等的修改
- **【推荐】** 在类中删除未使用的任何字段、方法、内部类；在方法中删除未使用的任何参数声明与内部变量
- **【参考】** 谨慎注释掉代码。在上方详细说明，而不是简单地注释掉。如果无用，则删除

## 代码格式

- **【强制】** 源代码单文件不超过500行
- **【强制】** 每行代码长度不超过 100 个字符，超过部分必须换行
- **【强制】** 代码块大括号必须使用 Egyptian 风格，左大括号与声明在同一行，如 `if (condition) {`
- **【强制】** 运算符两侧必须有空格，如 `a + b` 而非 `a+b`
- **【强制】** 函数参数括号前不允许有空格，括号内参数间用逗号加空格分隔
- **【强制】** 对象字面量中冒号后必须有一个空格，冒号前不允许有空格
- **【强制】** 关键字后必须有空格，如 `if (condition)` 而非 `if(condition)`
- **【强制】** 缩进使用 4 空格
- **【强制】** 句尾分号
- **【推荐】** 函数、类和文件之间必须用空行分隔，提高代码可读性
- **【推荐】** import 组件和 import less 需要空行，less 在最后引入
- **【推荐】** 英文单词书写正确。建议安装并开启 Spell Checker插件

## React 规范

- **【强制】** hook命名强制以useXXX命名
- **【强制】** 只在最顶层使用 Hook
- **【强制】** 只在 React 函数组件中调用 Hook
- **【强制】** 列表渲染强制指定唯一的 key，禁止使用循环 index 下标作为 key
- **【强制】** 创建的定时器、第三方组件实例（如地图、地图上的各种图层）强制在组件销毁时清除
- **【推荐】** 减少useState使用，部分场景可使用useRef
- **【推荐】** 避免滥用useEffect，避免监听大数据变量、高频变化变量作为依赖，造成性能问题
- **【推荐】** 变量定义类hook必须在组件顶部定义，如useState、useRef
- **【推荐】** 自定义hook返回值定位为对象结构，便于扩展和维护

## 标签规范

- **【强制】** 所有 HTML 标签必须闭合，自闭合标签以 / 结尾，如 `<img src="..." alt="..." />`
- **【推荐】** 标签属性值为 true 时可以简写，如 `<input disabled />` 而非 `<input disabled={true} />`
- **【强制】** 必须使用语义化标签，如 `<button>` 用于按钮，而非 `<div>` 加点击事件
- **【强制】** 图片标签必须添加 alt 属性，提高可访问性
- **【强制】** 标签嵌套必须符合 HTML5 规范，如 `<p>` 中不能嵌套块级元素
- **【强制】** HTML 注释必须格式清晰，使用 `{/* 注释内容 */}` 格式
- **【强制】** 避免过多的 `<div>` 嵌套，可使用 React Fragment `<>...</>` 减少不必要的嵌套

## TypeScript规范

- **【强制】** 所有变量、参数和返回值必须显式声明类型
- **【强制】** 接口类型名称必须以大写字母 I 开头，如 IUserProfile，区分接口和类
- **【强制】** 类型别名必须以大写字母 T 开头，如 TUserData，表明其为类型而非值
- **【强制】** 枚举类型必须使用 PascalCase 命名，并以 Enum 结尾，如 StatusEnum
- **【强制】** 联合类型和交叉类型较复杂时必须提取为独立的类型别名
- **【强制】** 必须使用 readonly 修饰不应被修改的属性，特别是对象中的常量值
- **【推荐】** 必须为可选属性提供默认值处理逻辑，避免运行时错误
- **【推荐】** 避免使用 any 类型
- **【推荐】** 泛型参数命名必须有意义，单字母仅用于简单场景，复杂场景使用描述性名称

## 定时器规范

- **【强制】** 定时器必须在组件卸载时清除，避免内存泄漏
- **【强制】** React 组件中的定时器必须在 useEffect 的清理函数中清除
- **【强制】** 定时器 ID 必须保存在 ref 中，而非 state，避免不必要的重渲染
- **【强制】** 定时器回调函数必须使用 useCallback 包裹，避免不必要的重新创建
- **【强制】** 避免嵌套定时器，一个定时器结束后再设置下一个定时器
- **【强制】** 定时器延时时间必须定义为常量，不得使用魔术数字，必须注释时长的背景和原因
- **【强制】** 长时间运行的定时器必须考虑性能影响，避免阻塞主线程
- **【强制】** 复杂的定时任务必须封装为自定义 Hook，提高复用性
- **【强制】** 定时器命名必须表明其用途，如 pollDataTimer 而非 timer1
- **【强制】** 定时器必须有合理的错误处理机制，避免因回调执行错误导致应用崩溃
- **【强制】** 避免使用 setTimeout 模拟 setInterval，除非有特殊需求，使用就必须写注释说明原因
- **【推荐】** 对于频繁触发的事件，必须使用防抖（debounce）或节流（throttle）处理

## 样式规范

- **【强制】** 类名必须使用小写字母和连字符命名，如 `.user-profile`，不使用驼峰命名
- **【强制】** 有主题色的系统，属于主题样式变量的属性必须使用属性变量，而不是写死颜色
- **【强制】** Less 和 Sass 中的变量、函数、混合必须使用小写字母和连字符命名
- **【推荐】** 颜色值使用十六进制表示法时，必须使用小写字母，能简写的必须简写，如 `#fff` 而非 `#FFFFFF`
- **【强制】** 避免使用 !important，通过提高选择器优先级解决样式冲突，使用注释必须说明原因
- **【强制】** 移动端样式必须使用 rem、vw 等相对单位，不使用绝对px单位
- **【推荐】** 选择器嵌套不得超过 3 层，避免过度嵌套导致特异性过高
- **【推荐】** 页面、组件样式，建议有唯一的classname做生效范围约束
- **【推荐】** 使用 CSS 简写属性，如 `margin: 10px 20px` 而非分别设置四个方向
- **【推荐】** CSS属性书写顺序：定位、盒模型、文本、其他

## 安全规范

- **【强制】** 密码类严禁明文传输，需要使用 RSA 2048 以上加密
- **【强制】** URL 严禁携带敏感信息，如 token
- **【强制】** 代码和配置文件中严禁写明文密码等敏感信息
- **【强制】** 文件上传需要限定上传文件类型、大小、数量
- **【强制】** 退出系统需要确保退出接口成功后系统才退出
- **【推荐】** 前端重定向跳转 URL 需要校验要跳转的 URL 是业务范围内的 URL，防钓鱼攻击
- **【强制】** 服务端设置的 Cookie 设置为 HttpOnly

## 二方库、三方库规范

- **【强制】** 第三方组件引入必须组长或 SE 评估后才能引入
- **【强制】** 二方库（自己开发或修改开源库而发布的二方库）必须以 @streamax/ 开头
- **【强制】** 二方库都必须发布在公司私有源中，不可外发

## 编码红线

- **【红线】** 文件上传需要限定上传文件大小、数量，文件类型根据实际情况定
- **【红线】** 禁止硬编码秘钥、密码、用户口令
- **【红线】** 禁止代码中存在明文秘钥、密码、口令
- **【红线】** 禁止私自引入第三方组件，使用三方库必须通过SE组评审
- **【红线】** 禁止发布组件到三方源，必须发布到公司私有源
- **【红线】** 二开三方源，必须修改包名称，加上@streamax/原名
- **【红线】** 代码和注释中都要避免使用任何语言的种族歧视性词语

## 配套校验工具

配置eslint + prettier 校验插件，规则继承@streamax/lint即可开启自动校验
