# 原子组件设计文档

## 功能设计

本设计文档描述了基于物料库规范的原子组件设计，包括基础选择框（BaseSelect）及其衍生的设备机型（Device）、车组（Fleet）和车辆（Vehicle）相关组件。设计遵循物料库规范中的目录结构规范、设计规范、开发规范和文档规范。

### 模块结构图

```mermaid
graph TD
    subgraph "基础能力层 /base"
        BaseSelect["BaseSelect 基础原子选择框"]
    end
    
    subgraph "单业务能力层 /modules"
        subgraph "Fleet 车组模块"
            FleetSelect["FleetSelect 车组选择框"]
            FleetSelector["FleetSelector 车组选择器"]
            FleetList["FleetList 车组列表"]
        end
        
        subgraph "Vehicle 车辆模块"
            VehicleTransfer["VehicleTransfer 车辆穿梭框"]
            VehicleSelector["VehicleSelector 车辆选择器"]
            VehicleList["VehicleList 车辆列表"]
        end
        
        subgraph "Device 设备模块"
            DeviceModelSelect["DeviceModelSelect 设备机型选择框"]
            DeviceModelSelector["DeviceModelSelector 设备机型选择器"]
            DeviceModelList["DeviceModelList 设备机型列表"]
        end
    end
    
    subgraph "业务使用层"
        FenceEventDetail["FenceEventDetail 围栏事件详情"]
    end
    
    BaseSelect --> DeviceModelSelect
    BaseSelect --> FleetSelect
    
    FleetSelect --> VehicleTransfer
    FleetSelect --> FleetSelector
    
    DeviceModelSelect --> DeviceModelSelector
    VehicleTransfer --> VehicleSelector
    
    DeviceModelSelector --> DeviceModelList
    FleetSelector --> FleetList
    VehicleSelector --> VehicleList
    
    FleetList --> FenceEventDetail
    VehicleList --> FenceEventDetail
    DeviceModelList --> FenceEventDetail
    
    style BaseSelect fill:#d0e0ff,stroke:#333,stroke-width:1px
    style DeviceModelSelect fill:#ffe0b0,stroke:#333,stroke-width:1px
    style DeviceModelSelector fill:#ffe0b0,stroke:#333,stroke-width:1px
    style DeviceModelList fill:#ffe0b0,stroke:#333,stroke-width:1px
    style FleetSelect fill:#d0ffe0,stroke:#333,stroke-width:1px
    style FleetSelector fill:#d0ffe0,stroke:#333,stroke-width:1px
    style FleetList fill:#d0ffe0,stroke:#333,stroke-width:1px
    style VehicleTransfer fill:#ffd0d0,stroke:#333,stroke-width:1px
    style VehicleSelector fill:#ffd0d0,stroke:#333,stroke-width:1px
    style VehicleList fill:#ffd0d0,stroke:#333,stroke-width:1px
    style FenceEventDetail fill:#d0d0ff,stroke:#333,stroke-width:1px
```

### 数据流设计

所有组件遵循三层架构设计模式：

```mermaid
flowchart TD
    subgraph "组件架构"
        UI["UI层 (视图渲染)"]
        Controller["Controller层 (状态管理)"]
        Data["Data层 (数据处理)"]
        
        UI --> Controller
        Controller --> Data
        Data --> API["后端API"]
        
        API --> Data
        Data --> Controller
        Controller --> UI
    end
```

**数据流向**：
1. UI层接收用户交互，调用Controller层方法
2. Controller层更新状态，调用Data层方法获取数据
3. Data层与后端API交互，获取原始数据并处理
4. Data层将处理后的数据返回给Controller层
5. Controller层更新状态，触发UI层重新渲染

**状态管理**：
- 每个组件的Controller层使用React Hooks管理组件状态
- 状态变更通过Controller层方法统一处理
- UI层通过Controller层提供的状态和方法进行渲染和交互

### 组件间数据传递

以下是业务层、List、Selector和Select之间的简化数据流图，重点展示它们之间的数据传递关系：

```mermaid
flowchart TD
    subgraph "业务层"
        Business["FenceEventDetail"]
    end
    
    subgraph "组件层"
        List["List组件"]
        Selector["Selector组件"]
        Select["Select组件"]
    end
    
    %% 业务层到List层
    Business -->|"resourcesCode\n(权限码)"| List
    Business -->|"eventId\n(围栏ID)"| List
    
    %% List层到Selector层
    List -->|"disabledKeys\n(禁用的key)"| Selector
    List -->|"onSelect\n(选择回调)"| Selector
    
    %% Selector层到Select层
    Selector -->|"disabledKeys\n(禁用的key)"| Select
    
    %% 数据回流
    Select -->|"onChange\n(选中变更)"| Selector
    Selector -->|"onSelect\n(选择结果)"| List
    List -->|"onDelete/onRefresh\n(操作回调)"| Business
    
    %% 样式设置
    classDef business fill:#d0d0ff,stroke:#333,stroke-width:1px
    classDef components fill:#ffe0b0,stroke:#333,stroke-width:1px
    
    class Business business
    class List,Selector,Select components
```

这个简化数据流图清晰展示了：
1. 业务层向List传递权限码和围栏ID
2. List向Selector传递禁用键和选择回调
3. Selector向Select传递禁用键
4. 数据回流路径：Select → Selector → List → 业务层

## 通用模块设计

### 1. 基础原子选择框（BaseSelect）

**目录结构**：
```
/base
  /components
    /BaseSelect
      /demo
      /ui
        index.tsx
        index.less
      /controller
        index.tsx
      /versions
        /V1
        index.tsx
      interface.ts
      index.tsx
      index.md
```

**Data层设计**：
```typescript
// BaseSelect/data/index.ts
export class BaseSelectData<T = any> {
  // 执行数据加载
  async executeLoadData(loadDataFn: () => Promise<T[]>): Promise<T[]>;
}
```

**Controller层设计**：
```typescript
// BaseSelect/controller/index.ts
import { create } from 'zustand';

type BaseSelectState<T = any> = {
  loading: boolean;
  options: T[];
  error: Error | null;
}

export class BaseSelectController<T = any> {
  private data: BaseSelectData<T>;
  public useDataStore = create<BaseSelectState<T>>((set) => ({
    loading: false,
    options: [],
    error: null
  }));
  
  constructor() {
    this.data = new BaseSelectData<T>();
  }
  
  /**
   * 加载数据
   */
  public async fetchData(loadDataFn: () => Promise<T[]>): Promise<T[]> {
    try {
      this.useDataStore.setState({ loading: true, error: null });
      const options = await this.data.executeLoadData(loadDataFn);
      this.useDataStore.setState({ loading: false, options, error: null });
      return options;
    } catch (error) {
      this.useDataStore.setState({ loading: false, error: error as Error });
      return [];
    }
  }
  
  /**
   * 重置状态
   */
  public reset(): void {
    this.useDataStore.setState({
      loading: false,
      options: [],
      error: null
    });
  }
}

export const controller = new BaseSelectController();
```

**UI层设计**：
```typescript
// BaseSelect/ui/index.tsx
export function BaseSelectUI<ValueType = any>({
  loadData,
  loadOnMount = true,
  onChange,
  disabled,
  ...restProps
}: BaseSelectProps<ValueType>): JSX.Element;
```

### 2. 三个业务模块的统一设计

三个业务模块（Fleet、Vehicle、Device）采用统一的设计模式，每个模块包含三个层次的组件：

1. **基础选择组件**：
   - Fleet模块：FleetSelect（树形选择框）
   - Vehicle模块：VehicleTransfer（穿梭框）
   - Device模块：DeviceModelSelect（下拉选择框）

2. **选择器组件**：
   - Fleet模块：FleetSelector（添加按钮+添加弹窗）
   - Vehicle模块：VehicleSelector（添加按钮+添加弹窗）
   - Device模块：DeviceModelSelector（添加按钮+添加弹窗）

3. **列表组件**：
   - Fleet模块：FleetList（列表+搜索条件+分页）
   - Vehicle模块：VehicleList（列表+搜索条件+分页）
   - Device模块：DeviceModelList（列表+搜索条件+分页）

**目录结构**（以Fleet模块为例）：
```
/modules
  /Fleet
    /FleetSelect
      /demo
      /ui
        index.tsx
        index.less
      /controller
        index.tsx
      /data
        index.tsx
      /versions
        /V1
        index.tsx
      interface.ts
      index.tsx
      index.md
    /FleetSelector
      /...
    /FleetList
      /...
```

## 第三方依赖交互流程与影响范围

### 三方依赖包

| 包名称 | 引入方 | 用途 |
| --- | --- | --- |
| @streamax/poppy | 组件库 | 提供基础UI组件如Select、Button、Modal等 |
| @streamax/poppy-icons | 图标库 | 提供图标组件 |
| @base-app/runtime-lib | 运行时库 | 提供国际化、工具函数等基础能力 |
| @streamax/starry-components | 组件库 | 提供ListDataContainer等高级组件 |

### 交互流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Components as 原子组件
    participant Poppy as @streamax/poppy
    participant RuntimeLib as @base-app/runtime-lib
    participant API as 后端API

    App->>Components: 使用组件
    Components->>Poppy: 使用UI组件
    Components->>RuntimeLib: 使用i18n、工具函数
    Components->>API: 请求数据
    API-->>Components: 返回数据
    Components-->>App: 渲染UI、触发回调
```

## 关键流程设计

### 1. 选择框数据加载流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as UI层
    participant Controller as Controller层
    participant Data as Data层
    participant API as 后端API

    User->>UI: 点击选择框
    UI->>Controller: 触发loadData方法
    Controller->>Controller: 设置loading=true
    Controller->>Data: 请求数据
    Data->>API: 发送API请求
    API-->>Data: 返回数据
    Data-->>Controller: 返回处理后的数据
    Controller->>Controller: 更新options, loading=false
    Controller-->>UI: 状态更新触发重渲染
    UI->>UI: 显示选项列表
    User->>UI: 选择选项
    UI->>Controller: 触发handleChange方法
    Controller->>UI: 更新选中值
    UI->>UI: 显示选中值
```

### 2. 选择器使用流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as UI层
    participant Controller as Controller层
    participant Data as Data层
    participant API as 后端API
    participant Parent as 父组件

    User->>UI: 点击添加按钮
    UI->>Controller: 调用showModal方法
    Controller->>Controller: 设置visible=true
    Controller-->>UI: 状态更新触发重渲染
    UI->>UI: 显示Modal
    
    alt 加载禁用项
        UI->>Data: 请求已选择的项
        Data->>API: 发送API请求
        API-->>Data: 返回数据
        Data-->>UI: 返回处理后的数据
        UI->>UI: 禁用已选择的项
    end
    
    User->>UI: 在Modal中选择数据
    User->>UI: 点击确认按钮
    UI->>Controller: 调用handleSelect方法
    Controller->>Controller: 设置visible=false
    Controller-->>UI: 状态更新触发重渲染
    UI->>UI: 关闭Modal
    Controller->>Parent: 触发onSelect回调
    Parent->>API: 调用保存接口
    API-->>Parent: 操作成功
    Parent->>Parent: 更新列表
```

### 3. 列表操作流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as UI层
    participant Controller as Controller层
    participant Data as Data层
    participant API as 后端API

    User->>UI: 访问列表页面
    UI->>Controller: 调用loadData方法
    Controller->>Controller: 设置loading=true
    Controller->>Data: 请求数据
    Data->>API: 发送API请求
    API-->>Data: 返回数据
    Data-->>Controller: 返回处理后的数据
    Controller->>Controller: 更新dataSource, loading=false
    Controller-->>UI: 状态更新触发重渲染
    UI->>UI: 显示数据列表
    
    alt 搜索/筛选
        User->>UI: 输入搜索条件
        User->>UI: 点击搜索按钮
        UI->>Controller: 调用loadData方法(带筛选参数)
        Controller->>Data: 请求数据(带筛选参数)
        Data->>API: 发送API请求(带筛选参数)
        API-->>Data: 返回筛选后数据
        Data-->>Controller: 返回处理后的数据
        Controller-->>UI: 状态更新触发重渲染
        UI->>UI: 显示筛选结果
    end
    
    alt 删除操作
        User->>UI: 点击删除按钮
        UI->>Controller: 调用handleDelete方法
        Controller->>Data: 请求删除数据
        Data->>API: 发送删除请求
        API-->>Data: 返回结果
        Data-->>Controller: 返回处理后的结果
        Controller->>Controller: 调用loadData重新加载数据
        Controller-->>UI: 状态更新触发重渲染
        UI->>UI: 显示更新后的列表
    end
```

## 异常场景

### 1. 数据加载失败

**场景**：网络问题导致API请求失败
**处理**：
- Controller层捕获异常，更新error状态
- UI层根据error状态显示错误提示和重试按钮
- 用户点击重试按钮，重新调用loadData方法

### 2. 空数据场景

**场景**：没有符合条件的数据
**处理**：
- Controller层检测数据为空，更新空状态
- UI层根据空状态显示空状态提示
- 提供添加按钮，引导用户添加数据

### 3. 权限不足

**场景**：用户没有添加/删除权限
**处理**：
- UI层根据传入的权限码判断，隐藏或禁用相应的操作按钮
- 对于需要权限的操作，在Controller层进行权限检查

### 4. 批量操作限制

**场景**：批量删除时未选择任何项
**处理**：
- Controller层检测选择项为空，显示提示信息
- 禁用批量操作按钮，直到用户选择了至少一项

### 5. 弱网环境

**场景**：网络连接不稳定，数据加载缓慢
**处理**：
- Controller层设置loading状态，UI层显示加载指示器
- 设置请求超时处理，超时后显示重试选项

## 页面修改范围

| 服务 | 类型 | 页面/模块 | 修改项 |
| --- | --- | --- | --- |
| 围栏事件 | 组件 | FenceEventDetail | 将BingingFleet.tsx拆分为FleetSelector和FleetList，增加data和controller层 |
| 围栏事件 | 组件 | FenceEventDetail | 将BindingVehicle.tsx拆分为VehicleSelector和VehicleList，增加data和controller层 |
| 设备管理 | 组件 | 新增 | 新增BaseSelect、DeviceModelSelect、DeviceModelSelector和DeviceModelList组件，包含完整的UI、Controller和Data层 |
| 基础组件 | 组件 | 新增 | 新增BaseSelect组件，作为选择框的基础组件 |

## 验证方案

| 场景 | 步骤 | 预期结果 |
| --- | --- | --- |
| 基础选择框功能验证 | 1. 点击选择框<br>2. 观察加载状态<br>3. 选择一个选项 | 1. 下拉菜单打开<br>2. 加载时显示loading图标<br>3. 选择成功，值更新 |
| 车组选择功能验证 | 1. 点击FleetSelect<br>2. 搜索车组<br>3. 选择一个车组<br>4. 勾选"包含子车组" | 1. 显示车组树<br>2. 筛选出匹配的车组<br>3. 选择成功，值更新<br>4. 包含子车组状态更新 |
| 车辆穿梭框功能验证 | 1. 打开VehicleTransfer<br>2. 在左侧选择车辆<br>3. 点击向右箭头<br>4. 在右侧查看已选车辆 | 1. 显示车辆列表<br>2. 选中的车辆高亮<br>3. 车辆移动到右侧<br>4. 右侧显示已选车辆 |
| 设备机型选择功能验证 | 1. 点击DeviceModelSelect<br>2. 选择一个设备机型 | 1. 显示设备机型列表<br>2. 选择成功，值更新 |
| 选择器功能验证 | 1. 点击添加按钮<br>2. 在弹窗中选择项<br>3. 点击确认 | 1. 弹窗正常打开<br>2. 可以选择项<br>3. 确认后触发onSelect回调，传递选中数据 |
| 列表功能验证 | 1. 查看列表<br>2. 使用搜索条件筛选<br>3. 选择多个项进行批量删除 | 1. 列表正常显示<br>2. 筛选结果正确<br>3. 批量删除成功，列表刷新 |
| 弱网环境测试【测试协助】 | 1. 在网络限速条件下加载列表<br>2. 观察加载状态和超时处理 | 1. 显示加载状态<br>2. 超时后显示错误提示和重试按钮 |
| 数据异常测试【测试协助】 | 1. 模拟API返回异常数据<br>2. 观察组件处理方式 | 1. 组件不崩溃<br>2. 显示友好的错误提示 |

## 资源&配置清单

### 资源

| 资源code | 资源类型 | 增值资源 | 编辑类型 | 资源描述 |
| --- | --- | --- | --- | --- |
| - | 组件 | 否 | 新增 | 基础选择框组件 |
| - | 组件 | 否 | 新增 | 车组选择框组件 |
| - | 组件 | 否 | 新增 | 车组选择器组件 |
| - | 组件 | 否 | 新增 | 车组列表组件 |
| - | 组件 | 否 | 新增 | 车辆穿梭框组件 |
| - | 组件 | 否 | 新增 | 车辆选择器组件 |
| - | 组件 | 否 | 新增 | 车辆列表组件 |
| - | 组件 | 否 | 新增 | 设备机型选择框组件 |
| - | 组件 | 否 | 新增 | 设备机型选择器组件 |
| - | 组件 | 否 | 新增 | 设备机型列表组件 |

### 配置

无

## 对开放接口的影响

本次改动不涉及开放接口的修改，仅对内部组件结构进行调整和优化。所有API调用保持不变，确保向下兼容性。

## 组件详细设计

### 1. 车组模块（Fleet）

#### FleetSelect（车组选择框）

**功能**：树形选择框，用于选择车组
**特点**：
- 支持树形结构展示
- 支持搜索过滤
- 支持"包含子车组"选项
- 支持禁用已选择的车组

**接口设计**：
```typescript
interface FleetSelectProps extends Omit<BaseSelectProps, 'onChange'> {
  /** 已选择的车组ID列表，用于禁用选项 */
  disabledKeys?: string[];
  /** 是否显示包含子车组的选项 */
  showSubFleetOption?: boolean;
  /** 是否默认包含子车组 */
  defaultIncludeSubFleet?: boolean;
  /** 值变化回调 */
  onChange?: (value: string, option: FleetOption, includeSubFleet?: boolean) => void;
}
```

#### FleetSelector（车组选择器）

**功能**：添加按钮+添加弹窗，用于选择车组
**特点**：
- 包含触发新增的按钮，按钮支持禁用
- 点击按钮后，modal对话框形式展示新增车组下拉表单
- 使用FleetSelect构成表单
- 添加的车组，通过事件对外抛出

**接口设计**：
```typescript
interface FleetSelectorProps {
  /** 是否禁用添加按钮 */
  disabled?: boolean;
  /** 添加按钮文本，默认为"添加车组" */
  buttonText?: string;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'link' | 'text';
  /** 已选择的车组ID列表，用于禁用已选择的车组 */
  disabledKeys?: string[];
  /** 是否默认包含子车组 */
  defaultIncludeSubFleet?: boolean;
  /** 选择车组后的回调函数，由外部处理保存逻辑 */
  onSelect?: (selectedFleets: FleetOption[], includeSubFleet: boolean) => void;
  /** 自定义Modal标题 */
  modalTitle?: string;
  /** 自定义Modal宽度 */
  modalWidth?: number | string;
}
```

#### FleetList（车组列表）

**功能**：列表+搜索条件+分页，用于展示车组列表
**特点**：
- 支持列表展示
- 支持搜索重置等操作
- 提供上中下三个插槽
- 支持外部设置或传入额外的过滤条件
- 支持传入额外的queryForm属性进行扩展查询条件

**接口设计**：
```typescript
interface FleetListProps {
  /** 事件ID */
  eventId: string;
  /** 资源权限码 */
  resourcesCode?: Record<string, any>;
  /** 额外的过滤条件 */
  extraFilters?: Record<string, any>;
  /** 额外的查询表单属性 */
  extraQueryFormProps?: Record<string, any>;
  /** 上方插槽 */
  topSlot?: React.ReactNode;
  /** 中间插槽（工具栏区域） */
  middleSlot?: React.ReactNode;
  /** 下方插槽 */
  bottomSlot?: React.ReactNode;
  /** 空表格钩子 */
  emptyTableHook?: EmptyTableHookType;
  /** 表格加载完成回调 */
  onLoaded?: (data: any) => void;
  /** 表格刷新回调 */
  onRefresh?: () => void;
  /** 表格引用 */
  tableRef?: React.RefObject<any>;
}
```

### 2. 车辆模块（Vehicle）

#### VehicleTransfer（车辆穿梭框）

**功能**：穿梭框，用于选择车辆
**特点**：
- 基于Transfer组件实现穿梭框功能
- 在左侧搜索区域集成FleetSelect组件作为车组筛选条件
- 支持车牌号搜索
- 支持自定义渲染列表项
- 支持大数据量的分页加载

**接口设计**：
```typescript
interface VehicleTransferProps {
  /** 已选择的车辆列表 */
  value?: VehicleOption[];
  /** 值变化回调 */
  onChange?: (value: VehicleOption[]) => void;
  /** 禁用状态 */
  disabled?: boolean;
  /** 标题配置 */
  titles?: [string, string];
  /** 搜索框占位符 */
  searchPlaceholder?: string;
  /** 列表高度 */
  listHeight?: number;
  /** 列表样式 */
  listStyle?: React.CSSProperties;
  /** 操作按钮文案 */
  operations?: [string, string];
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 是否显示全选勾选框 */
  showSelectAll?: boolean;
  /** 自定义渲染列表项 */
  render?: (item: VehicleOption) => React.ReactNode;
  /** 自定义过滤方法 */
  filterOption?: (inputValue: string, item: VehicleOption) => boolean;
  /** 自定义底部渲染 */
  footer?: (props: any, info: { direction: string }) => React.ReactNode;
  /** 额外的搜索条件 */
  extraFilters?: Record<string, any>;
}
```

#### VehicleSelector（车辆选择器）

**功能**：添加按钮+添加弹窗，用于选择车辆
**特点**：
- 包含触发新增的按钮，按钮支持禁用
- 点击按钮后，modal对话框形式展示新增车辆穿梭框
- 使用VehicleTransfer构成表单
- 添加的车辆，通过事件对外抛出

**接口设计**：
```typescript
interface VehicleSelectorProps {
  /** 是否禁用添加按钮 */
  disabled?: boolean;
  /** 添加按钮文本，默认为"添加车辆" */
  buttonText?: string;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'link' | 'text';
  /** 已选择的车辆ID列表，用于禁用已选择的车辆 */
  disabledKeys?: string[];
  /** 选择车辆后的回调函数，由外部处理保存逻辑 */
  onSelect?: (selectedVehicles: VehicleOption[]) => void;
  /** 自定义Modal标题 */
  modalTitle?: string;
  /** 自定义Modal宽度 */
  modalWidth?: number | string;
}
```

#### VehicleList（车辆列表）

**功能**：列表+搜索条件+分页，用于展示车辆列表
**特点**：
- 支持列表展示
- 支持搜索重置等操作
- 提供上中下三个插槽
- 支持外部设置或传入额外的过滤条件
- 支持传入额外的queryForm属性进行扩展查询条件
- 支持批量删除操作

**接口设计**：
```typescript
interface VehicleListProps {
  /** 事件ID */
  eventId: string;
  /** 资源权限码 */
  resourcesCode?: Record<string, any>;
  /** 额外的过滤条件 */
  extraFilters?: Record<string, any>;
  /** 额外的查询表单属性 */
  extraQueryFormProps?: Record<string, any>;
  /** 上方插槽 */
  topSlot?: React.ReactNode;
  /** 中间插槽（工具栏区域） */
  middleSlot?: React.ReactNode;
  /** 下方插槽 */
  bottomSlot?: React.ReactNode;
  /** 空表格钩子 */
  emptyTableHook?: EmptyTableHookType;
  /** 表格加载完成回调 */
  onLoaded?: (data: any) => void;
  /** 表格刷新回调 */
  onRefresh?: () => void;
  /** 表格引用 */
  tableRef?: React.RefObject<any>;
}
```

### 3. 设备模块（Device）

#### DeviceModelSelect（设备机型选择框）

**功能**：下拉选择框，用于选择设备机型
**特点**：
- 基于BaseSelect实现
- 支持过滤已选设备
- 支持搜索过滤
- 支持禁用已选择的设备机型

**接口设计**：
```typescript
interface DeviceModelSelectProps extends Omit<BaseSelectProps, 'onChange'> {
  /** 已选择的设备ID列表，用于禁用选项 */
  disabledKeys?: string[];
  /** 值变化回调 */
  onChange?: (value: string, option: DeviceModelOption) => void;
}
```

#### DeviceModelSelector（设备机型选择器）

**功能**：添加按钮+添加弹窗，用于选择设备机型
**特点**：
- 包含触发新增的按钮，按钮支持禁用
- 点击按钮后，modal对话框形式展示新增设备机型下拉表单
- 使用DeviceModelSelect构成表单
- 添加的设备机型，通过事件对外抛出

**接口设计**：
```typescript
interface DeviceModelSelectorProps {
  /** 是否禁用添加按钮 */
  disabled?: boolean;
  /** 添加按钮文本，默认为"添加设备" */
  buttonText?: string;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'link' | 'text';
  /** 已选择的设备ID列表，用于禁用已选择的设备 */
  disabledKeys?: string[];
  /** 选择设备后的回调函数，由外部处理保存逻辑 */
  onSelect?: (selectedDevices: DeviceModelOption[]) => void;
  /** 自定义Modal标题 */
  modalTitle?: string;
  /** 自定义Modal宽度 */
  modalWidth?: number | string;
}
```

#### DeviceModelList（设备机型列表）

**功能**：列表+搜索条件+分页，用于展示设备机型列表
**特点**：
- 支持列表展示
- 支持搜索重置等操作
- 提供上中下三个插槽
- 支持外部设置或传入额外的过滤条件
- 支持传入额外的queryForm属性进行扩展查询条件
- 支持批量删除操作

**接口设计**：
```typescript
interface DeviceModelListProps {
  /** 额外的过滤条件 */
  extraFilters?: Record<string, any>;
  /** 额外的查询表单属性 */
  extraQueryFormProps?: Record<string, any>;
  /** 上方插槽 */
  topSlot?: React.ReactNode;
  /** 中间插槽（工具栏区域） */
  middleSlot?: React.ReactNode;
  /** 下方插槽 */
  bottomSlot?: React.ReactNode;
  /** 删除设备回调 */
  onDelete?: (deviceIds: string[]) => Promise<void>;
  /** 表格加载完成回调 */
  onLoaded?: (data: any) => void;
  /** 表格刷新回调 */
  onRefresh?: () => void;
  /** 表格引用 */
  tableRef?: React.RefObject<any>;
}
```
