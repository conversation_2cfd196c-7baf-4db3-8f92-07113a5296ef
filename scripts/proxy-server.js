/**
 * 本地联调 iframe渲染使用，用于将iframe页面端口请求转到开发服务端口上
 */
const http = require('http');

function createProxyServer(proxyPort, targetPort) {
    const server = http.createServer((req, res) => {
        const options = {
            hostname: 'localhost',
            port: targetPort,
            path: req.url,
            method: req.method,
            headers: req.headers,
        };

        const proxyReq = http.request(options, (proxyRes) => {
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        req.pipe(proxyReq);

        proxyReq.on('error', (error) => {
            console.error(`Proxy error for port ${proxyPort}:`, error);
            res.writeHead(500);
            res.end('Proxy error');
        });
    });

    server.listen(proxyPort, () => {
        console.log(
            `Proxy server running on port ${proxyPort} -> ${targetPort}`,
        );
    });

    server.on('error', (error) => {
        console.error(`Server error for port ${proxyPort}:`, error);
    });
}

// Get ports from command line arguments
const [, , ...ports] = process.argv;
const [targetPort, ...proxyPorts] = ports.map(Number);

// Create proxy servers
proxyPorts.forEach((port) => {
    createProxyServer(port, targetPort);
});
