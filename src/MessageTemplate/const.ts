import { MessageUtils } from '@base-app/runtime-lib';

import {
    utils
} from '@base-app/runtime-lib';

export type Labels = {
    name: string;
    value: string;
    format?: (text: any) => string;
}[];
const {
    getSpeedFormat,
    zeroTimeStampToFormatTime,
} = utils.formator;
const { getUnitName } = MessageUtils;

// 报警类型的模板标签
export const labels: Labels = [
    {
        name: 'alarmTypeName',
        value: '{{alarmTypeName}}',
    },
    {
        name: 'vehicleNo',
        value: '{{vehicleNo}}',
    },
    {
        name: 'driverName',
        value: '{{driverName}}',
    },
    {
        name: 'deviceNo',
        value: '{{deviceNo}}',
    },
    {
        name: 'alarmTime',
        value: '{{alarmTime}}',
        format: (text: any) => {
            return zeroTimeStampToFormatTime(text) || '-';
        },
    },
    {
        name: 'alarmSpeed',
        value: '{{alarmSpeed}}',
        format: (text: any) => {
            return getSpeedFormat(text / 10) + getUnitName();
        },
    },
];


// 是否展示详情按钮
export const DETAIL_BTN = {
    HIDDEN: 0,
    SHOW: 1,
};