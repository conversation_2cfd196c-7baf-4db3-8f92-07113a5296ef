import { i18n, MessageUtils } from '@base-app/runtime-lib';
import { Btn, MessageInfo, ModalData, JumpOptions } from "./type";
import { queryAlarmExist } from '@/service/alarm';

const { jumpPage } = MessageUtils;

export const getBtn = (modelData: ModalData, pageConfig?: any, history?: any): Btn => {
    const btn = {
        text: i18n.t('action', '查看报警详情'),
        onclick: () => {
            // 校验是否有权限  有权限才跳转，无权限不跳转
            queryAlarmExist({
                alarmId: modelData?.alarmId,
                appId: modelData?.appId,
            }).then(() => {
                const jumpOption: JumpOptions = {
                    pageType: 'alarm',
                    suffixPath: modelData?.alarmId,
                    appId: modelData?.appId,
                };
                jumpPage(jumpOption, pageConfig, history);
            });
            // 跳转报警列表
            // window.location.href = 'https://dev-saas-2154.streamax.com/ftv2/alarm-list?endTime=1724194799&handleStatus=0&includeSubFleet=1&page=1&startTime=1723590000&vehicleSearchType=vehicle&vehicleSearchValue=';
        },
    }
    return btn;
}

export const getMessage = (messageInfo: MessageInfo) => {
    const { titleId, title } = messageInfo;
    return titleId ? i18n.t(titleId, title) : title;
}

export const getMessageContent = (messageInfo: MessageInfo) => {
    const { contentId, content } = messageInfo;
    return contentId ? i18n.t(contentId, content) : content;
}