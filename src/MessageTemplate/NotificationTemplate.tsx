import { MessageDefaultNotificationRender, MessageUtils, MessageBtnDetail} from '@base-app/runtime-lib';

import { NotificationTemplateProps } from "./type";
import { getBtn, getMessage, getMessageContent } from './utils';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils

export default (props: NotificationTemplateProps) => {
    const {
        messageInfo, 
        handleClickNotice,
        prefixCls,
        history
    } = props;
    const {
        modelData,
        showDetailBtn
    }  = messageInfo;

    // 获取alarm类型btn按钮对象配置
    const btnObj = getBtn(modelData, undefined, history);
    // 获取报警类型对应的标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    

    // 获取格式化后的报警类型标题和内容
    // const formatMessage = getFormatMessage(message, labels, modelData);
    // const formatDescription = getFormatDescription(content, labels, modelData);
    const formatMessage = '测试自定义报警模板标题';
    const formatDescription = '测试自定义报警模板测试自定义报警模板测试自定义报警模板测试自定义报警模板测试自定义报警模板测试自定义报警模板测试自定义报警模板测试自定义报警模板';
    
    // 查看详情按钮
    const BtnEle = (
        <MessageBtnDetail
            btn={btnObj}
            messageInfo={messageInfo}
            clickCallback={() => handleClickNotice?.(messageInfo)}
        />
    );

    return (
        <MessageDefaultNotificationRender
            type="warning"
            message={formatMessage}
            description={formatDescription}
            prefixCls={prefixCls}
            btn={showDetailBtn === DETAIL_BTN.SHOW ? BtnEle : undefined}
        />
    );
}