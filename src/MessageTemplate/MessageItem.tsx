import { MessageDefaultMessageItem, MessageBtnDetail, MessageUtils } from '@base-app/runtime-lib';
import { MessageInfo, MessageItemProps } from "./type";
import { getBtn, getMessage, getMessageContent } from './utils';
import { useHistory } from '@base-app/runtime-lib/core';
import { DETAIL_BTN, labels } from "./const";

const { getFormatMessage, getFormatDescription } = MessageUtils

export default (props: MessageItemProps) => {   
    const {
        messageInfo, 
    } = props;
    const {
        modelData,
        showDetailBtn
    }  = messageInfo;
    const history = useHistory();
    
    // 获取报警类型对应的标题和内容
    const message = getMessage(messageInfo);
    const content = getMessageContent(messageInfo);
    // const getFormatMessage = () => {
    //     return '测试自定义报警模板标题';
    // };

    // const getFormatDescription = () => {
    //     return '测试自定义报警模板内容内容内容内容'
    // }


    // 查看详情按钮
    const getMessageItemBtnDetail = (messageInfo: MessageInfo, pageConfig: any) => {
        const btn = getBtn(modelData, pageConfig, history);
        return <MessageBtnDetail btn={btn} messageInfo={messageInfo} />;
    };

    return (
        <MessageDefaultMessageItem 
            showBtn={showDetailBtn === DETAIL_BTN.SHOW}
            getBtnDetail={getMessageItemBtnDetail}
            getMessage={() => getFormatMessage(message, labels, modelData)}
            getDescription={() => getFormatDescription(content, labels, modelData)}
            messageInfo={messageInfo}
        />
    )
}