





export interface IConfigItem {
    resourceCode: string;
    resourceId: string;
    resourceUrl: string;
};

export interface IConfigItemWithStatus {
    success: IConfigItem | null;
    fail: IConfigItem | null;
};

export interface IJumpConfig {
    alarm: IConfigItem;
    evidence: IConfigItemWithStatus;
    video: IConfigItemWithStatus;
    licenseBind: IConfigItem;
};

export interface JumpOptions {
    pageType: keyof IJumpConfig;
    suffixPath: string;
    appId: number;
    pageStatus?: keyof IConfigItemWithStatus;
};

export type Btn = {
    text: string;
    onclick: () => void;
    handleDownloadExpire?: boolean;
};

export type ModalData = {
    appId: number;
    [key: string]: any;
};

export type PersonalCenterMenuKeys = 
    "personalInfo" | 
    "messageList" | 
    "toolsCenter" | 
    "appVersion" | 
    "fileExport";

export type OpenPersonalCenter = (key: PersonalCenterMenuKeys, params?: any) => void;


export type MessageInfo = {
    notificationType: number;
    modelData: ModalData;
    [key: string]: any;
};

export type MessageItemProps = {
    messageInfo: MessageInfo;
    openPersonalCenter: OpenPersonalCenter;
};

export type NotificationTemplateProps = {
    messageInfo: MessageInfo;
    handleClickNotice?: (messageInfo: MessageInfo) => void;
    prefixCls?: string;
    history?: any;
};