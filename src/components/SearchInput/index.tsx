import { useState, useEffect } from 'react';
import {Input, Dropdown, Form, Menu, Select, Space} from '@streamax/poppy';
import { i18n, useSystemComponentStyle } from '@base-app/runtime-lib';
import { DownOutlined } from '@ant-design/icons';
import type { IMenuOption } from '@base-app/runtime-lib';
import { MultipleSearchDropdownLabel } from '@base-app/runtime-lib';
const { useDropdownLabel } = MultipleSearchDropdownLabel;

type Type = 'vehicle' | 'device' | 'driver';


interface VehicleSearchInputProps {
    value?: any;
    searchOptions?: Type[];
    onChange?: (value: any) => void;
    disabled?: boolean;
}


export default (props: VehicleSearchInputProps) => {
    const { searchOptions: propSearchOptions = ['vehicle', 'device'], onChange, value: propValue, disabled = false } = props;

    // 确保 searchOptions 不为空且只包含有效值
    const searchOptions = propSearchOptions?.filter(
        (option) => option === 'vehicle' || option === 'device' || option === 'driver'
    ) || [];

    // 如果过滤后为空数组，则使用默认值
    const validSearchOptions = searchOptions.length > 0
        ? searchOptions
        : ['vehicle', 'device'] as Type[];

    // 在组件中使用useDropdownLabel
    const { defaultDropDownMenu, fieldProps } = useDropdownLabel({
        menuOptions: [
            validSearchOptions.indexOf('vehicle') > -1 ? {
                label: i18n.t('name', '车牌号码'),
                value: 'vehicle',
                placeholder: i18n.t('message', '请输入车牌号码')
            }: null,
            validSearchOptions.indexOf('device') > -1 ? {
                label: i18n.t('name', '车辆设备'),
                value: 'device',
                placeholder: i18n.t('message', '请输入车辆设备')
            } : null,
            validSearchOptions.indexOf('driver') > -1 ? {
                label: i18n.t('name', '司机名称'),
                value: 'driver',
                placeholder: i18n.t('message', '请输入司机名称')
            } : null,
        ].filter(Boolean) as IMenuOption[],
        onSelect: (newType) => {
            handleSelect(newType)
        },
        dropdownConfig:{
            disabled
        },
        defaultKey: propValue?.type
    });
    const [current, setCurrent] = useState<Type>('vehicle');
    const [value, setValue] = useState<any>('');


    useEffect(() => {
        if (propValue && propValue.value) {
            setValue(propValue.value);
            handleSelect(propValue.type, propValue.value);
        }
    }, [JSON.stringify(propValue)]);

    const handleSelect = (type: Type, propValue?: string) => {
        setCurrent(type);
        onChange &&
            onChange?.({
                type: type,
                value: value || propValue,
            });
    };

    const handleChange = (e: any) => {
        const v = e.target.value;
        setValue(v);
        onChange &&
            onChange?.({
                type: current,
                value: v,
            });
    };

    return (
        <>
            <Space direction="vertical" style={{ width: '100%' }}>
                {defaultDropDownMenu}
                <Input
                    allowClear
                    value={value}
                    maxLength={50}
                    onChange={handleChange}
                    disabled={disabled}
                    {...fieldProps}  // 使用hooks返回的表单属性,包含海外的下拉组件配置
                />
            </Space>
        </>
    );
};
