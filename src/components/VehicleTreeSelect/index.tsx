import { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import { TreeSelect, StarryTreeSelect, Spin, Empty } from '@streamax/poppy';
import { getAppGlobalData, i18n } from '@base-app/runtime-lib';
import React from 'react';
import classNames from 'classnames';
import { FLEET_PREFIX, UN_GROUP_FLEET_ID } from '@/const/vehicle';
import { getTreeVehicleKey, getVehicleIdByTreeKey } from '@/hooks/useFleetTreeData/useTransformTreeData';
import type { Vehicle as RTVehicle } from '@/hooks/useRTData/useRealtimeMonitoring';
import type { CountFleet as RTFleet } from '@/hooks/useRTData/util';
import type {
    Fleet as BaseFleet,
    Vehicle as BaseVehicle,
} from '@/hooks/useRTData/useFleetVehicleData';
import type { DataNode } from '@streamax/poppy/lib/tree';
import { getVehiclerListByPage, VehicleStateMenu, type VehicleOrderState } from '@/service/vehicle';
import { useAsyncEffect, useDebounceFn, useGetState } from '@streamax/hooks';
import './index.less';
import { NodeType, TreeData } from '@/modules/vehicle-tree/data/useTreeDataManager/useTreeDataStore';
import useOnceFn from '@/hooks/useOnceFn';
import GenerateTreeTitleV2 from './compontents/GenerateTreeTitleV2';
import { scrollToTreeNode } from '../VehicleTree/compontents/utils';
import { getRTDataStore } from '@/modules/vehicle-tree/data/realtime-data/useRTDataManager';
const convertTreeData2Node = (item: TreeData, instanceId = 'default') => {
    return Object.assign(
        {
            title: <GenerateTreeTitleV2 className='tree-node-search'  hasBracket={false} baseInfo={item} type={item.type} instanceId={instanceId}  />,
            showIcon: true,
            switcherIcon: false,
        },
        item,
    );
};
const { TreeNode } = TreeSelect;

const fleetPrefix = FLEET_PREFIX;

type TreeNodeType = DataNode & (BaseFleet | BaseVehicle);
type RtTreeNodeType = DataNode & (RTFleet | RTVehicle);
interface VehicleTreeSelectModel {
    disabled?: boolean;
    loading?: boolean;
    vehicleId: any;
    size?: string;
    onlySelectOnline?: boolean;
    showState?: boolean;
    onChange: (vehicleId: string, vehicleInfo: Record<string, any>, deviceId?: string) => void;
    isExpandFirstGroup?: boolean;
    type?: string;
    fleetList: RTFleet[];
    vehicleList: BaseVehicle[];
    baseVehicleList: BaseVehicle[];
    treeData: (TreeNodeType | RtTreeNodeType)[];
    loadFleets: (fIds: string[]) => Promise<void>;
    loadedKeys: string[];
    vehicleStateConfig?: VehicleOrderState[];
    instanceId?: string;
    onAutoRemoveSelectedVehicle?: () => void;
    onTreeNodeGenerate?: () => void;
    onTreeMounted?: () => void;
     // 自定义过滤
    getCustomFilter?: (value: string, data: any[]) => any[];
     // 树可视范围变动回调
    onVisibleChange: (renderList: any[], mergedData: any[]) => void;
}

interface RefVehicleTreeSelectProps {
    positionVehicle: (vehicleId: string, fleetId: string) => void;
}

const VehicleTreeSelect: React.ForwardRefRenderFunction<
    RefVehicleTreeSelectProps,
    VehicleTreeSelectModel
> = (props, ref) => {
    const {
        onChange,
        onlySelectOnline = false,
        showState = true,
        loading,
        type,
        disabled,
        vehicleList,
        loadFleets,
        vehicleStateConfig,
        fleetList,
        isExpandFirstGroup = true,
        onAutoRemoveSelectedVehicle,
        onTreeNodeGenerate,
        treeData,
        onTreeMounted,
        loadedKeys,
        size,
        instanceId = 'default',
        getCustomFilter,
        treeDataStore,
        vehicleId,
        onVisibleChange,
        baseVehicleList
    } = props;
    const [selectedVehicle, setSelectedVehicle] = useState<any>();
    const [expandedKeys, setExpandedKeys, getExpandedKeys] = useGetState<string[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const innerSearchValue = useRef('');
    const treeRef = useRef();
    const [isSearching, setIsSearching] = useState<boolean>(false); // 搜索loading状态
    const [isInitDataFinish, setIsInitDataFinish] = useState(false); // 初始化数据是否完成
    const selectVehicleItem = treeDataStore
        .getState()
        .loadFleetsManager.selectVehicleItem;
    
    const rtDataStore = getRTDataStore(instanceId);
    const vehicleInfo = rtDataStore(
        (state) => state.rtDataMap[vehicleId],
    );
    const selectVehicleStates = JSON.stringify(vehicleInfo?.vStates || []);
    // 选择搜索结果中的车辆后，在树中进行选中操作
    async function positionVehicle(vehicleId: string, fleetId?: string, deviceId?: string) {
        let fId = fleetId;
        if (!vehicleId) return;
        // 如何fleetId 未传，需要查询出fleetId
        const vehicle = vehicleList.find((i: any) => {
            if(fId) {
                return i.vId === vehicleId && i.fId === fId;
            } else {
                return i.vId === vehicleId;
            }
        });
        if (!vehicle) return;
        if (!fId && vehicle) {
            fId = vehicle.fId;
        }
        const { key } = getTreeVehicleKey(fId || UN_GROUP_FLEET_ID, vehicleId);
        if(fId) {
            const newKeys = (getExpandedKeys() || []).concat(vehicle?.path.map((fId) => `fleet-${fId}`));
            const newExpandKeys = [...new Set(newKeys)];
            setExpandedKeys(newExpandKeys);
            await selectVehicleItem?.(vehicleId, fId);
            fId && (await loadFleets(vehicle?.path));
        }
        scrollToTreeNode(key);
        setSelectedVehicle(key);
        vehicle && onChange(String(vehicleId), vehicle,deviceId);
    }
    useImperativeHandle(ref, () => ({
        positionVehicle,
        toggleExpand,
    }));
    useAsyncEffect(async () => {
        if(vehicleInfo) {
            await selectVehicleItem?.(vehicleId, vehicleInfo.fId);
            vehicleInfo.fId && (await loadFleets(vehicleInfo?.path));
        }
    }, [selectVehicleStates]);
    useEffect(() => {
        if (treeData.length > 0) {
            setIsInitDataFinish(true);
        }
    }, [treeData]);
    useEffect(() => {
        // 设备停用等场景会导致树中此车辆节点去除，此时需要去除已选择状态
        if (selectedVehicle) {
            const vId = getVehicleIdByTreeKey(selectedVehicle);
            if (!baseVehicleList.some((i) => i.vId === vId)) {
                setSelectedVehicle(null);
                onChange('', {});
                onAutoRemoveSelectedVehicle?.();
            }
        }
    }, [baseVehicleList]);



    useEffect(() => {
        if (isExpandFirstGroup && isInitDataFinish) {
            // 展开一级车组
            setFirstExpand(treeData);
            onTreeMounted?.();
        }
    }, [isExpandFirstGroup, isInitDataFinish]);
    const setFirstExpand = useOnceFn(async (treeDataNode) => {
        // 展开一级车组
        const expandedKeys = treeDataNode.filter((node) => !node.parentKey).map((item) => item.key);
        if (expandedKeys.length > 0) {
            // 最后执行异步操作，防止异步操作阻塞时，展开车组不成功
            setExpandedKeys(expandedKeys);
            await loadFleets(expandedKeys.map((key) => key.replace('fleet-', '')));
        }
    });
    const toggleExpand = async (key) => {
        const realExpandKey = getExpandedKeys();
        if(realExpandKey.includes(key)) {
            setExpandedKeys([...realExpandKey.filter(item => item !== key)]);
        } else {
            setExpandedKeys([...realExpandKey, key]);
            await loadFleets(realExpandKey.map((key) => key.replace('fleet-', '')));
        }
    };

    // 加载车队下的车辆数据或设备下的通道数据
    async function loadVehicleData(node: any, callback?: any) {
        return new Promise<void>((resolve) => {

            const { key, children } = node;
            const [type] = key?.split("-");
            if (type === NodeType.VEHICLE) {
                // TODO 临时解决 后面原子化改造
                loadedKeys.push(key);
                resolve();
                return;
            }
            // 车队节点的处理逻辑
            const id = key.replace(fleetPrefix, '');
            if ((children && children.length > 0) || loadedKeys.includes(id)) {
                resolve();
                return;
            }
            loadFleets([id]).then(() => {
                resolve();
            });
        });
    }

    const onSelect = (e: any) => {
        if (!e) {
            setSearchValue('');
            innerSearchValue.current = '';
            return;
        }
        const infoArr = e.split('-');
        const nodeType = infoArr[0]; // 获取节点类型
        
        // 如果是设备节点，需要找到其上级车辆节点
        if (nodeType === NodeType.DEVICE) {
            // 设备节点的key格式为: device-fleetId-vehicleId-deviceId
            const fleetId = infoArr[1];
            const vehicleId = infoArr[2];
            const deviceId = infoArr[3];
            positionVehicle(vehicleId, fleetId, deviceId);
        } else {
            // 车辆节点处理逻辑
            const vehicleId = infoArr[infoArr.length - 1];
            const fleetId = infoArr[1];
            const vehicle = vehicleList.find((i: any) => i.vId === vehicleId) || {};
            // 搜索时选中的需要加载树种父节点数据
            if (!!searchValue) {
                positionVehicle(vehicleId, fleetId);
            }
            setSelectedVehicle(e);
            onChange(vehicleId, vehicle);
        }
    };

    
    const [filteredTreeData, setFilteredTreeData] = useState<TreeData[]>([]);
    const { run: searchDebounceFn } = useDebounceFn(() => {
        const searchText = innerSearchValue.current;
        
        // 如果没有搜索文本，不需要进行搜索
        if (!searchText) {
            setIsSearching(false);
            return;
        }
        
        // 开始搜索，确保isSearching为true
        setIsSearching(true);
        
        getVehiclerListByPage({
            page: 1,
            pageSize: 50,
            vehicleNumberOrDeviceNoOrDeviceAlias: searchText
                ? encodeURIComponent(searchText)
                : searchText,
            decode: searchText ? 1 : 0,
            fields: 'fleet,device',
            appId: getAppGlobalData("APP_ID"),
            vehicleState: VehicleStateMenu.ENABLE,
            filterDeviceUnactivated: true
        }).then(data => {
            const result = data.list.flatMap((item, nameIndex) => {
                // 产品要求取第一个车组，对于一车多车组不需要考虑
                const deviceExist = item.deviceList?.length;
                const fleet = item.fleetList?.[0];
                if (!deviceExist) {
                    return [{
                        ...item,
                         pos: '0',
                        level: 0,
                        parentKey: null,
                        isLeaf: true,
                        name: item.vehicleNumber,
                        key: `vehicle-${fleet.fleetId}-${item.vehicleId}`,
                        type: NodeType.VEHICLE,
                        nameIndex,
                        id: item.vehicleId,
                        fId: fleet.fleetId,
                        parentId:fleet.fleetId,
                    }];
                }
                // 先映射设备列表，创建节点对象
                const deviceNodes = item.deviceList?.map((device, index) => {
                    return {
                        ...item,
                        pos: '0',
                        level: 0,
                        parentKey: null,
                        isLeaf: true,
                        name: `${item.vehicleNumber}(${device?.deviceAlias || device.deviceNo})`,
                        key: `device-${fleet.fleetId}-${item.vehicleId}-${device.deviceId}`,
                        type: NodeType.DEVICE,
                        nameIndex: index,
                        id: device.deviceId,
                        fId: fleet.fleetId,
                        parentId: item.vehicleId
                    };
                }) || [];
                
                // TODO 后端目前不支持，需要前端自己零时处理过滤
                return deviceNodes.filter(node => {
                    // 默认过滤：确保名称包含搜索文本
                    if (searchText) {
                        return node?.name?.toLowerCase().includes(searchText.toLowerCase());
                    }
                    return true;
                });
            });
            const dataNode = result.map(item => {
                return convertTreeData2Node(item, instanceId);
            });
            // @ts-ignore
            setFilteredTreeData(dataNode);
            
            // 搜索完成，设置loading状态为false
            setIsSearching(false);
        }).catch(error => {
            // 处理错误，确保loading状态被重置
            setIsSearching(false);
        });
    });
    // 内部维护一个 innerSearchValue 是为了解决将大数据量直接给 TreeSelect 组件,TreeSelect 组件在频繁输入搜索字符时，存在卡顿的性能问题
    const onSearch = (value: any) => {
        //搜索之前情况上次搜索的内容
        setFilteredTreeData([]);
        const searchText = value.slice(0, 50);
        if(!value) {
            // 解决搜索后闪动问题
            setTimeout(() => {
                setSearchValue(searchText);
                setIsSearching(false); // 清空搜索时设置为false
            }, 220);
        }else{
            setSearchValue(searchText);
            setIsSearching(true); // 有搜索内容时设置为true
        }
        innerSearchValue.current = searchText;
        searchDebounceFn();
    };

    const treeExpandChange = (keys: any) => {
        setExpandedKeys(keys);
    };

    return (
        <div
            className={classNames('vehicle-tree-select', {
                dark: type === 'dark',
            })}
        >
            {/* @ts-ignore */}
            <StarryTreeSelect
                ref={treeRef}
                size={size}
                style={{ width: '100%' }}
                disabled={disabled}
                dropdownClassName={classNames('vehicle-tree-drop-box', {
                    dark: type === 'dark',
                })}
                listHeight={400}
                placeholder={i18n.t('message', '请选择车辆')}
                treeLine={false}
                notFoundContent={<div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: 106 }}>
                    {(!loading || isSearching) ? <Spin spinning style={{ position: "absolute", top: '50%', left: "50%", transform: "translate(-50%, -50%)" }} /> : <Empty imageStyle={{
                        height: 45,
                        background: "#454F5B"
                    }} />}
                </div>}
                treeData={!!searchValue ? filteredTreeData : treeData}
                loadData={loadVehicleData}
                onVisibleChange={onVisibleChange}
                treeExpandedKeys={expandedKeys}
                onTreeExpand={treeExpandChange}
                treeLoadedKeys={loadedKeys}
                onSelect={onSelect}
                value={selectedVehicle}
                showSearch={true}
                searchValue={searchValue}
                onSearch={onSearch}
                getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                filterTreeNode={false}
                fieldNames={{
                    value: 'key'
                }}
            />
        </div>
    );
};

const VehicleTree = forwardRef<RefVehicleTreeSelectProps, VehicleTreeSelectModel>(
    VehicleTreeSelect,
) as (
    props: React.PropsWithChildren<VehicleTreeSelectModel> & {
        ref?: React.Ref<RefVehicleTreeSelectProps>;
    },
) => React.ReactElement;

export default VehicleTree;

