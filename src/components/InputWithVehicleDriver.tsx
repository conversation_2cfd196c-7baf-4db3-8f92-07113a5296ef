import { useState, useEffect } from 'react';
import {Input, Dropdown, Form, Menu, Select, Space} from '@streamax/poppy';
import { i18n,useSystemComponentStyle } from '@base-app/runtime-lib';
import { DownOutlined } from '@ant-design/icons';
import type { IMenuOption } from '@base-app/runtime-lib';
import { MultipleSearchDropdownLabel } from '@base-app/runtime-lib';
const { useDropdownLabel } = MultipleSearchDropdownLabel;

type Type = 'vehicle' | 'driver' | 'device';
const { Option } = Select;
interface InputWithVehicleDriverProps {
    value?: { type: Type; value: string };
    onChange?: (value: any) => void;
    // 选择展示模式 车牌号、司机名称、设备编号
    mode?: Type[];
}

const MODE_DEFAULT: Type[] = ['vehicle', 'driver', 'device'];


export default (props: InputWithVehicleDriverProps) => {
    const { value: initValue, onChange, mode: propMode = MODE_DEFAULT } = props;

    // 确保 mode 不为空且只包含有效值
    const mode = propMode?.filter(
        (option) => option === 'vehicle' || option === 'driver' || option === 'device'
    ) || [];

    // 如果过滤后为空数组，则使用默认值
    const validMode = mode.length > 0
        ? mode
        : MODE_DEFAULT;

    // 在组件中使用useDropdownLabel
    const { defaultDropDownMenu, fieldProps } = useDropdownLabel({
        menuOptions: [
            validMode.findIndex((item) => item == 'vehicle') > -1 ? {
                label: i18n.t('name', '车牌号码'),
                value: 'vehicle',
                placeholder: i18n.t('message', '请输入车牌号码')
            }: null,
            validMode.findIndex((item) => item == 'driver') > -1 ? {
                label: i18n.t('name', '司机名称'),
                value: 'driver',
                placeholder: i18n.t('message', '请输入司机名称')
            } : null,
            validMode.findIndex((item) => item == 'device') > -1 ? {
                label: i18n.t('name', '设备编号'),
                value: 'device',
                placeholder: i18n.t('message', '请输入设备编号')
            } : null
        ].filter(Boolean) as IMenuOption[],
        onSelect: (newType) => {
            handleSelect(newType)
        },
        defaultKey: initValue?.type
    });
    const [currentType, setCurrentType] = useState<Type>('vehicle');
    const [value, setValue] = useState<string | undefined>(undefined);

    const handleSelect = (type: Type) => {
        setCurrentType(type);
        onChange?.(
            value
                ? {
                      type: type,
                      value: value,
                  }
                : undefined,
        );
    };
    useEffect(() => {
        setCurrentType(initValue?.type || 'vehicle');
    }, [initValue]);

    const handleChange = (e: any) => {
        const v = e.target.value;
        setValue(v);
        onChange?.(
            v
                ? {
                      type: currentType,
                      value: v,
                  }
                : undefined,
        );
    };
    return (
        <>
            <Space direction="vertical" style={{ width: '100%' }}>
                {defaultDropDownMenu}
                <Input
                    allowClear
                    value={initValue?.value}
                    onChange={handleChange}
                    maxLength={50}
                    {...fieldProps}  // 使用hooks返回的表单属性,包含海外的下拉组件配置
                />
            </Space>
        </>
    );
};
