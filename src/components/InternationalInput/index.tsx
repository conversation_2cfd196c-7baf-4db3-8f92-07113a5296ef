import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Input, Modal, Form, Button } from '@streamax/poppy';
import {
    i18n,
    getAppGlobalData,
    utils,
    StarryAbroadFormItem as AFormItem,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import { IconLanguage } from '@streamax/poppy-icons';
import { fetchAuthorityLanguageList, getLanguageList } from '../../service/language';
import { languageLabelList } from '../../utils/commonFun';
import { debounceAsyncRepeatFn } from '../../utils/asyncDebounce';
import type { FormInstance, Rule } from '@streamax/poppy/lib/form';
import type { InputProps } from '@streamax/poppy/lib/input';
import "./index.scoped.less";

type InternationalType =
    | 'app'
    | 'menu'
    | 'page'
    | 'operation'
    | 'role'
    | 'alarmLevel'
    | 'alarmType'
    | 'themeColor'
    | 'alarmCategory'
    | 'userStrategy'
    | 'alarmLinkageStrategy'
    | 'evidenceReturnStrategy'
    | 'emailSendingStrategy'
    | 'faceComparisonStrategy'
    | 'alarmWebResponseStrategy'
    | 'protocol'
    | 'parameter'
    | 'resourcegroup'
    | 'pictureCaptureStrategy'
    | 'tenantTemplate'
    | 'vehicleTypeEnum'

interface InternationalInputProps extends InputProps {
    modalType: 'add' | 'edit'; // 新增或编辑
    internationalType: InternationalType; // 国际化类型
    entryKey?: string; // 原对象名称
    entryIdOrCode?: number | string; // 原对象ID,(internationalType = menu|page|operation)则应该是code
    onChange?: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    onSave?: (values: any) => void;
    onInit?: () => void; // 初始化完成
    rules?: Rule[],
    formValidate?: (rule: any, value: string) => Promise<any>; 
}

interface TranslationItem {
    langType: string;
    translation: string;
}

interface RefInternationalProps {
    form: FormInstance<any>;
}

const getNameMap = () => {
    const nameMap: Record<string, { title: string; label: string }> = {
        app: {
            title: i18n.t('message', '应用国际化'),
            label: i18n.t('name', '应用名称'),
        },
        menu: {
            title: i18n.t('message', '菜单国际化'),
            label: i18n.t('name', '菜单名称'),
        },
        page: {
            title: i18n.t('message', '页面国际化'),
            label: i18n.t('name', '页面名称'),
        },
        operation: {
            title: i18n.t('message', '操作国际化'),
            label: i18n.t('name', '操作名称'),
        },
        role: {
            title: i18n.t('message', '角色国际化'),
            label: i18n.t('name', '角色名称'),
        },
        alarmLevel: {
            title: i18n.t('message', '报警等级国际化'),
            label: i18n.t('name', '报警等级'),
        },
        alarmType: {
            title: i18n.t('message', '报警类型国际化'),
            label: i18n.t('name', '报警类型'),
        },
        themeColor: {
            title: i18n.t('message', '主题颜色国际化'),
            label: i18n.t('name', '主题颜色名称'),
        },
        alarmCategory: {
            title: i18n.t('message', '报警分类国际化'),
            label: i18n.t('name', '报警分类'),
        },
        userStrategy: {
            title: i18n.t('message', '用户设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        alarmLinkageStrategy: {
            title: i18n.t('message', '报警联动设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        evidenceReturnStrategy: {
            title: i18n.t('message', '证据回传设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        emailSendingStrategy: {
            title: i18n.t('message', '邮件发送设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        faceComparisonStrategy: {
            title: i18n.t('message', '人脸比对设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        alarmWebResponseStrategy: {
            title: i18n.t('message', '自动处理设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        protocol: {
            title: i18n.t('message', '协议国际化'),
            label: i18n.t('name', '协议名称'),
        },
        parameter: {
            title: i18n.t('message', '参数国际化'),
            label: i18n.t('name', '参数名称'),
        },
        resourcegroup: {
            title: i18n.t('message', '服务国际化'),
            label: i18n.t('name', '服务名称'),
        },
        pictureCaptureStrategy: {
            title: i18n.t('message', '图片抓拍设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        tenantTemplate: {
            title: i18n.t('message', '租户模板国际化'),
            label: i18n.t('name', '模板名称'),
        },
        flowLimitConfig: {
            title: i18n.t('message', '流量设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        channelSettingStrategy: {
            title: i18n.t('message', '通道设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        channelDeviceSettingStrategy: {
            title: i18n.t('message', '通道设置国际化'),
            label: i18n.t('name', '设置名称'),
        },
        dataCleanStrategy: {
            title: i18n.t('message', '保存有效期国际化'),
            label: i18n.t('name', '设置名称'),
        },
        vehicleTypeEnum: {
            title: i18n.t('message', '车辆类型国际化'),
            label: i18n.t('name', '车辆类型名称'),
        },
    };
    return nameMap;
};

const InternationalInput: React.ForwardRefRenderFunction<
    RefInternationalProps,
    InternationalInputProps
> = (props, ref) => {
    const {
        modalType,
        internationalType,
        entryKey,
        onChange,
        onSave,
        onInit,
        className,
        value,
        entryIdOrCode,
        rules,
        formValidate,
        ...otherProps
    } = props;
    const [visible, setVisible] = useState<boolean>(false);
    const [authorityLanguageList, setAuthorityLanguageList] = useState<string[]>([]);
    const [hasTranslationValue, setHasTranslationValue] = useState<boolean>(false);
    const [entryItem, setEntryItem] = useState<any>('');
    const [langKey, setLangKey] = useState<string>('');
    const cacheAddDataRef = useRef<any>();
    const cacheEditDataRef = useRef<any>();
    const currentType = useRef<string>(''); // 记录当前输入框编辑的类型，编辑名称为objectName，其他为translate

    const [form] = Form.useForm();
    const loadedEntryKeyLanguage = useRef<boolean>(false);

    const getEntryKeyLanguage = (key: string) => {
        const langKey = `@i18n:@${internationalType}__${key}`;
        getLanguageList({
            langKey,
        })
            .then((rs: any) => {
                if (rs) {
                    setLangKey(langKey);
                    setEntryItem(rs);
                    const fieldsValue: any = {};
                    (rs.languageList || []).forEach((p: TranslationItem) => {
                        fieldsValue[p.langType] = p.translation;
                    });
                    if (fieldsValue[getAppGlobalData('APP_LANG')]) {
                        setHasTranslationValue(true);
                    }
                    // 将value赋值给当前语言
                    fieldsValue[getAppGlobalData('APP_LANG')] = value;
                    fieldsValue['objectName'] = entryKey;
                    form.setFieldsValue(fieldsValue);
                    cacheEditDataRef.current = fieldsValue;
                } else {
                    form.setFieldsValue({
                        objectName: entryKey,
                    });
                    cacheEditDataRef.current = {
                        objectName: entryKey,
                    };
                }
            })
            .finally(() => {
                onInit?.();
            });
    };
    useImperativeHandle(ref, () => ({
        form,
        setInnerCacheData: (data: any) => cacheAddDataRef.current = data
    }));
    useEffect(() => {
        // 编辑时不做修改，也要把值传出去
        if (entryItem) {
            const defaultValue = {
                translationList: entryItem.languageList?.map(
                    ({ langType, translation }: { langType: string; translation: string }) => ({
                        noUpperLangType: langType,
                        langType: langType.toUpperCase(),
                        translationValue: translation,
                    }),
                ) || [],
                langKey,
                langId: entryItem.id,
                objectName: entryKey,
            };
            onSave?.(defaultValue);
        }
    }, [entryItem]);

    useEffect(() => {
        fetchAuthorityLanguageList({
            tenantId: getAppGlobalData('APP_USER_INFO')['tenantId'],
            // tenantId: 0
        }).then((rs: any) => {
            setAuthorityLanguageList(rs);
        });
    }, []);

    useEffect(() => {
        initData();
    }, [entryIdOrCode, value, entryKey]);
    const initData = () => {
        if (modalType === 'edit') {
            if (entryIdOrCode && value && !loadedEntryKeyLanguage.current) {
                getEntryKeyLanguage(entryIdOrCode as any);
                loadedEntryKeyLanguage.current = true;
            } else {
                if (hasTranslationValue) {
                    form.setFieldsValue({
                        [getAppGlobalData('APP_LANG')]: value,
                    });
                } else {
                    form.setFieldsValue({
                        objectName: value,
                    });
                    cacheEditDataRef.current = {
                        objectName: value,
                    };
                }
            }
        }
        if( modalType === 'add') {
            form.setFieldsValue({
                [getAppGlobalData('APP_LANG')]: value,
            });
        }
    };
    const handleOk = (close: boolean = true) => {
        // 增加timeout延迟获取表单值，搜狗输入法，输入中文时，input从form.getFieldsValue拿不到最新值，只能获取到带'分隔符的拼音
        // 带'分隔符为特殊字符，校验不通过，造成不会调用handelOk，最终外层表单保存时，存储的值有误
        setTimeout(() => {
            form.validateFields().then((values: any) => {
                const result: any = {};
                const translationList: any = [];
                Object.keys(values || {}).forEach((p: any) => {
                    if (p !== 'objectName') {
                        translationList.push({
                            langType: p,
                            translationValue: values[p]?.trim() || '',
                            translation: values[p]?.trim() || '',
                        });
                    }
                });
                result.translationList = translationList;
                if (values.objectName) {
                    result.objectName = values[getAppGlobalData('APP_LANG')]?.trim();
                }
                if (entryItem) {
                    result.langId = entryItem.id;
                }
                result.langKey = langKey;
                if (close) {
                    onChange?.(
                   {
                        target: {
                                  value:(result.objectName||values[getAppGlobalData('APP_LANG')])?.trim(),
                                },
                            }
                    );
                }
                onSave?.(result);
                if (modalType === "add" && close) {
                    cacheAddDataRef.current =translationList?.reduce(
                        (pre, cur) => ({ ...pre, [cur.langType]: cur.translationValue?.trim() }),
                        {},
                    );
                    form.setFieldsValue(cacheAddDataRef.current);
                }
                if (modalType === "edit" && close) {
                    cacheEditDataRef.current = translationList?.reduce(
                        (pre, cur) => ({ ...pre, [cur.langType]: cur.translationValue?.trim() }),
                        {},
                    );
                    cacheEditDataRef.current.objectName = values.objectName?.trim();
                    form.setFieldsValue(cacheEditDataRef.current);
                };

                if (close) {
                    modalType !== "edit" && setEntryItem({ ...entryItem, languageList: translationList });
                    setVisible(false);
                }
            });
        }, 100);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, type: string) => {
        const objectName = form.getFieldValue('objectName');
        // 需要考虑当值为空且有翻译值时，需要展示原名称。同时将翻译值同步为空
        onChange?.(
            // @ts-ignore
            e.target.value === '' && hasTranslationValue
                ? {
                      target: {
                          value: objectName,
                      },
                  }
                : e,
        );
        // 将翻译值同步为空
        if (e.target.value === '') {
            form.setFieldsValue({
                [getAppGlobalData('APP_LANG')]: '',
            });
            setHasTranslationValue(false);
        }else{
            // 将输入框的值赋值给当前语言
            form.setFieldsValue({
                [getAppGlobalData('APP_LANG')]: e.target.value,
            });
        }
        handleOk(false);
        // 输入框编辑时，记录当前编辑的输入框类型
        currentType.current = type;
    };

    const handleCancel = () => {
        if (modalType === 'add') {
            // 新增回退上一次保存的数据
            const formValue = cacheAddDataRef.current;
            formValue ? form.setFieldsValue(formValue) : form.resetFields();
        } else {
            const objectName = form.getFieldValue('objectName');
            const currentLangTranslation = form.getFieldValue(getAppGlobalData('APP_LANG'));
            const formValues = {};
            const languageMap = entryItem?.languageList?.map((item: any) => {
                // 若之前操作的是名称字段，并且有当前语言国际化，则点击取消时，国际化也不需要恢复成原始值
                const transitionResult = item.langType.toUpperCase()=== getAppGlobalData('APP_LANG').toUpperCase() && currentType.current === 'objectName' ? currentLangTranslation :item.translation;
                formValues[item.langType] = transitionResult;
                return {...item, translation: transitionResult};
            });
            const currentLangue = formValues[getAppGlobalData('APP_LANG')];
            currentLangue ? setHasTranslationValue(true) : setHasTranslationValue(false);
            let objectNameResult = entryKey;
            if (currentType.current === 'objectName' && currentLangue) {
                objectNameResult = currentLangTranslation;
            } else if (currentType.current === 'objectName') {
                objectNameResult = objectName;
            }
            const defaultValue = {
                translationList: entryItem?.languageList?.map(
                    ({ langType, translation }: { langType: string; translation: string }) => {
                        const lang = langType.toUpperCase();
                        return {
                            langType: lang,
                            translationValue: formValues[langType],
                        }
                    },
                ),
                langKey,
                langId: entryItem.id,
                // 取消点击时，若之前操作的是名称字段（非model弹窗内的），则取消时objectName不需要恢复成原始值
                objectName: objectNameResult,
            };
            onSave?.(defaultValue); // 处理取消后保存时带有编辑的国际化内容
            onChange?.({
                //@ts-ignore
                target: {
                    value: currentLangTranslation,
                },
            });
            setEntryItem({
                ...entryItem,
                languageName: defaultValue.objectName,
                languageList: languageMap
            });
            form.resetFields();
            form.setFieldsValue(cacheEditDataRef.current);
        }
        setVisible(false);
    };

    const illegalCharacter = (rule: any, value: string) => {
        return new Promise((resolve, reject) => {
            if (value && /[\\/*,？?‘’''“”""<>|｜]/g.test(value)) {
                reject(
                    i18n.t('message', '非法字符'),
                );
            }
            resolve('');
        })
    };
    return (
        <>
            <div
                style={{
                    display: 'inline-block',
                    width: '100%',
                    position: 'relative',
                }}
            >
                <Input
                    {...otherProps}
                    className={`${className || ''} international-input-control`}
                    onChange={(e) => handleInputChange(e, 'objectName')}
                    value={value}
                />
                <a className="language-map-icon" onClick={() => setVisible(true)}>
                    <StarryAbroadIcon>
                        <IconLanguage />
                    </StarryAbroadIcon>
                </a>
            </div>
            <Modal
                centered
                visible={visible}
                maskClosable={false}
                destroyOnClose
                title={getNameMap()[internationalType]?.title}
                onCancel={handleCancel}
                forceRender
                wrapClassName='international-input-modal'
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        {i18n.t('action', '取消')}
                    </Button>,
                    <Button key="submit" type="primary" onClick={() => handleOk()}>
                        {i18n.t('name', '确定')}
                    </Button>,
                ]}
            >
                <Form
                    form={form as any}
                    layout="vertical"
                    preserve={false}
                    className="international-form-layout"
                >
                    {modalType === 'edit' && (
                        <AFormItem
                            label={getNameMap()[internationalType]?.label}
                            name="objectName"
                            rules={
                                rules
                                    ? rules
                                    : [
                                          { required: true },
                                          {
                                              validator: debounceAsyncRepeatFn(formValidate || illegalCharacter),
                                          },
                                      ]
                            }
                        >
                            <Input
                                maxLength={50}
                                {...(!hasTranslationValue ? { onChange: handleInputChange } : {})}
                            />
                        </AFormItem>
                    )}
                    {(authorityLanguageList || []).map((lang: string) => {
                        const inputProps: any = {};
                        if (lang === getAppGlobalData('APP_LANG')) {
                            inputProps.onChange = (e: any) => {
                                setHasTranslationValue(e.target.value !== '');
                                handleInputChange(e);
                            };
                        }
                        return (
                            <AFormItem
                                label={languageLabelList[lang]}
                                key={lang}
                                name={lang}
                                rules={
                                    rules
                                        ? rules
                                        : [
                                              {
                                                  validator: formValidate || utils.validator.illegalCharacter,
                                              },
                                          ]
                                }
                            >
                                <Input maxLength={50} {...inputProps} />
                            </AFormItem>
                        );
                    })}
                </Form>
            </Modal>
        </>
    );
};
const InternationalInputWrapper = forwardRef<RefInternationalProps, InternationalInputProps>(
    InternationalInput,
) as (
    props: React.PropsWithChildren<InternationalInputProps> & {
        ref?: React.Ref<RefInternationalProps>;
    },
) => React.ReactElement;
export default InternationalInputWrapper;
