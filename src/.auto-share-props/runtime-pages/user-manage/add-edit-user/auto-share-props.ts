/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { RoleAuthorizeShareDataProps } from "../../../../runtime-pages/user-manage/add-edit-user/user-role-authorize/index";
import type { UserDataAuthorizeShareProps } from "../../../../runtime-pages/user-manage/add-edit-user/user-data-authorize/index";


interface PageShareProps {
    'UserRoleAuthorize'?: RoleAuthorizeShareDataProps;
    'UserDataAuthorize'?: UserDataAuthorizeShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;