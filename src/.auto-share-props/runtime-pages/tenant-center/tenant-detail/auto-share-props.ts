/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { TenantInfoShareProps } from "../../../../runtime-pages/tenant-center/components/TenantInfo";
import type { TenantSettingShareProps } from "../../../../runtime-pages/tenant-center/tenant-detail/Setting/index";


interface PageShareProps {
    'TenantInfo'?: TenantInfoShareProps;
    'Setting'?: TenantSettingShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;