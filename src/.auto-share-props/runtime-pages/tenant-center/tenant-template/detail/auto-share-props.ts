/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { TenantSettingShareProps } from "../../../../../runtime-pages/tenant-center/tenant-template/components/tenant-setting/index";
import type { AbilityShareProps } from "../../../../../runtime-pages/tenant-center/tenant-template/components/ability-setting/index";
import type { PresetFileShareProps } from "../../../../../runtime-pages/tenant-center/tenant-template/components/preset-file/index";
import type { extendConfigShareProps } from "../../../../../runtime-pages/tenant-center/tenant-template/components/application-info/index";
import type { ExtendConfigShareProps } from "../../../../../runtime-pages/tenant-center/tenant-template/components/extend-config/index";


interface PageShareProps {
    'TenantSetting'?: TenantSettingShareProps;
    'AbilitySetting'?: AbilityShareProps;
    'PresetFile'?: PresetFileShareProps;
    'ApplicationInfo'?: extendConfigShareProps;
    'ExtendConfig'?: ExtendConfigShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;