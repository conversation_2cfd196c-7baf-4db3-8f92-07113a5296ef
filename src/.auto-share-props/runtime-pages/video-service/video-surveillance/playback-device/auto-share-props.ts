/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { DevicePlayBackControlShareProps } from "../../../../../runtime-pages/video-service/video-surveillance/playback-device/components/player/index";
import type { DevicePlayBackTipShareProps } from "../../../../../runtime-pages/video-service/video-surveillance/playback-device/components/controller/index";
import type { PlayBackAlarmInfoShareProps } from "../../../../../runtime-pages/video-service/video-surveillance/playback-device/components/AlarmInfo/index";


interface PageShareProps {
    'Playback.Player'?: DevicePlayBackControlShareProps;
    'Playback.Controller'?: DevicePlayBackTipShareProps;
    'Playback.AlarmInfo'?: PlayBackAlarmInfoShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;