/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { RiskTaskHandleProps } from "../../../../runtime-pages/monitoring-center/risk-task/components/OperateBar/index";
import type { MapTripShareProps } from "../../../../runtime-pages/monitoring-center/risk-task/components/MapTrip/index";
import type { AlarmStepsShareProps } from "../../../../runtime-pages/monitoring-center/risk-task/components/AlarmSteps/index";
import type { RiskTaskShareProps } from "../../../../runtime-pages/monitoring-center/risk-task/components/TaskList/index";
import type { TaskItemShareProps } from "../../../../runtime-pages/monitoring-center/risk-task/components/TaskList/TaskItem";


interface PageShareProps {
    'OperateBar'?: RiskTaskHandleProps;
    'MapTrip'?: MapTripShareProps;
    'AlarmSteps'?: AlarmStepsShareProps;
    'RiskList'?: RiskTaskShareProps;
    'RiskList.TaskItem'?: TaskItemShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;