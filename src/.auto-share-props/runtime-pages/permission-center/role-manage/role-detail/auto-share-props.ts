/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { AuthorizedUserShareProps } from "../../../../../runtime-pages/permission-center/role-manage/role-detail/authorized-user/index";
import type { RoleDetailInfoShareProps } from "../../../../../runtime-pages/permission-center/role-manage/role-detail/components/RoleInfo";


interface PageShareProps {
    'UserAuthorized'?: AuthorizedUserShareProps;
    'RoleInfo'?: RoleDetailInfoShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;