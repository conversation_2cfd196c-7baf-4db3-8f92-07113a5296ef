/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { DefaultBasicInfoDetailShareProps } from "../../../../../runtime-pages/strategy-center/base/components/DefaultBasicInfoDetail";
import type { FaceSettingShareProps } from "../../../../../runtime-pages/strategy-center/face-contrast/components/FaceSetting";


interface PageShareProps {
    'DefaultBasicInfoDetail'?: DefaultBasicInfoDetailShareProps;
    'FaceSetting'?: FaceSettingShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;