/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { EvidenceListDoneShareProps } from "../../../runtime-pages/evidence-list/components/Done/index";
import type { EvidenceListCardShareProps } from "../../../runtime-pages/evidence-list/components/Card";
import type { EvidenceListDoingShareProps } from "../../../runtime-pages/evidence-list/components/Doing/index";
import type { EvidenceListWaitingShareProps } from "../../../runtime-pages/evidence-list/components/Waiting/index";
import type { EvidenceListWaitingShareProps as EvidenceListWaitingShareProps1 } from "../../../runtime-pages/evidence-list/components/Fail/index";


interface PageShareProps {
    'Done'?: EvidenceListDoneShareProps;
    'Done.Card'?: EvidenceListCardShareProps;
    'Doing'?: EvidenceListDoingShareProps;
    'Waiting'?: EvidenceListWaitingShareProps;
    'Fail'?: EvidenceListWaitingShareProps1;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;