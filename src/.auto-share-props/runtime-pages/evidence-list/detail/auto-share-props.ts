/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { EvidenceToolsProps } from "../../../../components/EvidenceTools/index";
import type { EvidenceDetailBaseInfoProps } from "../../../../runtime-pages/evidence-list/detail/components/BaseInfo";


interface PageShareProps {
    'EvidenceTools'?: EvidenceToolsProps;
    'BaseInfo'?: EvidenceDetailBaseInfoProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;