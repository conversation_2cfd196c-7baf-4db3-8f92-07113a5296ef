/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { VideoLibraryDoneShareProps } from "../../../runtime-pages/video-library/components/Done/index";
import type { UploadSettingShareProps } from "../../../runtime-pages/video-library/create/components/UploadSetting/index";
import type { VideoLibraryCardShareProps } from "../../../runtime-pages/video-library/components/Card/index";
import type { VideoLibraryDoingShareProps } from "../../../runtime-pages/video-library/components/Doing/index";
import type { VideoLibraryWaitingShareProps } from "../../../runtime-pages/video-library/components/Waiting/index";
import type { VideoLibraryFailShareProps } from "../../../runtime-pages/video-library/components/Fail/index";


interface PageShareProps {
    'Done'?: VideoLibraryDoneShareProps;
    'Done.Create.VideoUploadSetting'?: UploadSettingShareProps;
    'Done.Card'?: VideoLibraryCardShareProps;
    'Doing'?: VideoLibraryDoingShareProps;
    'Waiting'?: VideoLibraryWaitingShareProps;
    'Fail'?: VideoLibraryFailShareProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;