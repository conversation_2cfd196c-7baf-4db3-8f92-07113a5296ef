/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

// 引入类型
import type { EvidenceToolsProps } from "../../../../../components/EvidenceTools/index";
import type { AlarmEvidenceShareProps } from "../../../../../runtime-pages/alarm-center/alarm-list/alarm-detail/components/AlarmEvidence/index";
import type { AlarmInfoShareDataProps } from "../../../../../runtime-pages/alarm-center/alarm-list/alarm-detail/components/AlarmInfo/index";
import type { AlarmProcessShareDataProps } from "../../../../../runtime-pages/alarm-center/alarm-list/alarm-detail/components/AlarmProcessHistory/index";


interface PageShareProps {
    'EvidenceTools'?: EvidenceToolsProps;
    'AlarmEvidence'?: AlarmEvidenceShareProps;
    'AlarmInfo'?: AlarmInfoShareDataProps;
    'AlarmProcessHistory'?: AlarmProcessShareDataProps;
}

interface ExportShareProps {
    sharePropData?: PageShareProps;
}

export default ExportShareProps;