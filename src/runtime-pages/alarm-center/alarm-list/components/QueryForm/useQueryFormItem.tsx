import { Select } from '@streamax/poppy';
import type { QueryFormProps as PoppyQueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import { i18n, Auth } from '@base-app/runtime-lib';
//@ts-ignore
import { FleetTreeSelect } from '@base-app/runtime-lib';
import moment from 'moment';
import DateRange from '../../../../../components/DateRange';
import VehicleInput from '../VehicleSearchInput';
import { disabledAfterDate, getInitTimeRange } from '@/utils/commonFun';
import { getCustomItems } from '@/utils/pageReuse';
import { EvidenceStateEnum } from '@/modules/evidence/types';
import { AlarmHandleStatusEnum, getAlarmHandleStatusLabel } from '@/modules/alarm';
import { useAlarmTypeSelectFormItemObject } from '@/modules/alarm/alarmType';
import { useAlarmLevelSelectFormItemObject } from '@/modules/alarm/alarmLevel';
import { useAlarmCategorySelectFormItemObject } from '@/modules/alarm/alarmCategory';
import { useLabelSelectFormItemObject } from '@/modules/label';
import { ListPageQueryForm } from '@/types/pageReuse/pageReuseBase';
import { LabelSelectRefType } from '@/components/LabelSelect';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
const MAX_SELECTED_NUMBER = 5; // 最多选择查询标签个数
export type ItemConfig = PoppyQueryFormProps['items'][0];
interface QueryFormItemOptions {
    getQueryForm: ListPageQueryForm['getQueryForm'];
    onFleetInitDone: () => void;
    labelSelectRef: LabelSelectRefType;
}
const useQueryFormItem = (options: QueryFormItemOptions)=>{
    const { getQueryForm, onFleetInitDone, labelSelectRef } = options;
    const initTimeRange = getInitTimeRange(7);

    const evidenceStateOptions = [
        {
            label: <span className="evidence-state-options-have">{i18n.t('name', '无证据')}</span>,
            options: [
                {
                    label: i18n.t('name', '证据被清理'),
                    value: EvidenceStateEnum.CLEAN,
                },
                {
                    label: i18n.t('name', '未获取证据'),
                    value: EvidenceStateEnum.UNOBTAINED,
                },
            ],
        },
        {
            label: <span className="evidence-state-options-have">{i18n.t('name', '有证据')}</span>,
            options: [
                {
                    label: i18n.t('name', '已完成'),
                    value: EvidenceStateEnum.DONE,
                },
                {
                    label: i18n.t('name', '进行中'),
                    value: EvidenceStateEnum.DOING,
                },
                {
                    label: i18n.t('name', '等待中'),
                    value: EvidenceStateEnum.WAITING,
                },
                {
                    label: i18n.t('name', '失败'),
                    value: EvidenceStateEnum.FAIL,
                },
            ],
        },
    ];
    const alarmTypeSelectObject = useAlarmTypeSelectFormItemObject();
    const alarmLevelSelectObject = useAlarmLevelSelectFormItemObject();
    const alarmCategorySelectObject = useAlarmCategorySelectFormItemObject();
    const {formItem: labelSelectObject} = useLabelSelectFormItemObject();
    const labelSelectProps = {
        maxSelectedNumber: MAX_SELECTED_NUMBER,
        showAddOption: Auth.check('@base:@page:alarm.list@action:add.label'),
        showAddOptionCode: '@base:@page:alarm.list@action:add.label',
        ref: labelSelectRef,
        getPopupContainer: (element) => {
            return element.parentNode as HTMLElement;
        },
        listHeight: 150,

    };
    Object.assign(labelSelectObject.fieldProps, labelSelectProps);
    const queryFormItem = [
        {
            field: VehicleInput,
            name: 'vehicleSearch',
            colSize: 1,
            itemProps: {
                initialValue: {
                    type: 'vehicle',
                    value: '',
                },
            },
        },
        {
            label: i18n.t('name', '报警状态'),
            name: 'handleStatus',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('message', '请选择报警状态'),
                options: [
                    {
                        value: AlarmHandleStatusEnum.DONE,
                        label: getAlarmHandleStatusLabel(AlarmHandleStatusEnum.DONE)
                    },
                    {
                        value: AlarmHandleStatusEnum.WAITING,
                        label: getAlarmHandleStatusLabel(AlarmHandleStatusEnum.WAITING)
                    }
                ],
                maxTagCount: 'responsive',
                getPopupContainer: (element) => {
                    return element.parentNode as HTMLElement;
                },
                listHeight: 150,
                allowClear: true,
                showArrow: true,
            },
        },
        {
            ...alarmTypeSelectObject,
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'fleet',
            field: FleetTreeSelect,
            fieldProps: {
                // showSubFleetContent: true,
                placeholder: i18n.t('message', '请选择归属车组'),
                getPopupContainer: (element) => {
                    return element.parentNode as HTMLElement;
                },
                listHeight: 150,
                onInitDone: onFleetInitDone,
                // dropdownMatchSelectWidth:true
                // maxLength: 50
            },
        },
        alarmLevelSelectObject,
        alarmCategorySelectObject,
        {
            label: i18n.t('name', '报警时间范围'),
            name: 'alarmTime',
            field: DateRange,
            colSize: 2,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    allowClear: true,
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    ranges: getPickerRangeTWM(),
                    disabledDate: disabledAfterDate,
                },
            },
        },
        labelSelectObject,
        {
            label: i18n.t('name', '证据状态'),
            name: 'evidence_state',
            field: Select,
            colSize: 1,
            fieldProps: {
                placeholder: i18n.t('message', '请选择证据状态'),
                options: evidenceStateOptions,
                maxTagCount: 'responsive',
                allowClear: true,
                showArrow: true,
                getPopupContainer: (element) => {
                    return element.parentNode as HTMLElement;
                },
                listHeight: 150,
            },
        },
    ];
    const queryItems: ItemConfig[] = getCustomItems(getQueryForm, queryFormItem,  {
        onFleetInitDone, // 车组下拉选项加载完毕后调用
        labelSelectRef, // 标签组件的ref
    });
    return queryItems;
}
export default useQueryFormItem;
