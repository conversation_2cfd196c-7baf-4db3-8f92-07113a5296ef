/*
 * @LastEditTime: 2023-12-12 16:14:10
 */
import { useState, useEffect } from 'react';
import {Input, Dropdown, Form, Menu, Space} from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { DownOutlined } from '@ant-design/icons';
import { MultipleSearchDropdownLabel } from '@base-app/runtime-lib';
const { useDropdownLabel } = MultipleSearchDropdownLabel;

type Type = 'vehicle' | 'device' | 'driver';

interface VehicleSearchInputProps {
    value?: any;
    onChange?: (value: any) => void;
}

export default (props: VehicleSearchInputProps) => {
    const { onChange, value: propValue } = props;
    // 在组件中使用useDropdownLabel
    const { defaultDropDownMenu, fieldProps, updateSelectKey } = useDropdownLabel({
        menuOptions: [
            {
                label: i18n.t('name', '车牌号码'),
                value: 'vehicle',
                placeholder: i18n.t('message', '请输入车牌号码')
            },
            {
                label: i18n.t('name', '司机名称'),
                value: 'driver',
                placeholder: i18n.t('message', '请输入司机名称')
            },
        ].filter(Boolean),
        onSelect: (newType) => {
            handleSelect(newType);
        },
        defaultKey: propValue?.type
    });
    const [current, setCurrent] = useState<Type>('vehicle');
    const [value, setValue] = useState<string>('');
    useEffect(() => {
        if (propValue && propValue.value) {
            setValue(propValue.value);
            updateSelectKey(propValue.type);
            handleSelect(propValue.type, propValue.value);
        } else {
            setValue(propValue.value);
        }
    }, [JSON.stringify(propValue)]);

    const handleSelect = (type: Type, propValue?: string) => {
        setCurrent(type);
        onChange &&
            onChange?.({
                type: type,
                value: value || propValue,
            });
    };

    const handleChange = (e: any) => {
        const v = e.target.value;
        setValue(v);
        onChange &&
            onChange?.({
                type: current,
                value: v,
            });
    };
    return (
        <>
            <Space direction="vertical" style={{ width: '100%' }}>
                {defaultDropDownMenu}
                <Input
                    allowClear
                    value={value}
                    onChange={handleChange}
                    maxLength={50}
                    {...fieldProps}  // 使用hooks返回的表单属性,包含海外的下拉组件配置
                />
            </Space>
        </>
    );
};
