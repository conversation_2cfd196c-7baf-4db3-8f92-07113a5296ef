import { useState, useEffect } from 'react';
import {Starry<PERSON>readcrumb, StarryCard, StarryModal, useSystemComponentStyle} from '@base-app/runtime-lib';
import {Container, Empty, Select, Tabs} from '@streamax/poppy';
import {
    i18n,
    getAppGlobalData,
    Auth,
    RouterPrompt,
    useUrlSearchStore,
} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import { useAppList } from '@/hooks';
import PictureCapture from '@/modules/strategy/PictureCaptureStrategy';
import FaceContrast from '@/modules/strategy/FaceContrastStrategy';
import { getStrategyListByPage } from '@/service/strategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import './index.less';
import { IconDoublearrowRight } from '@streamax/poppy-icons';

interface TabItem {
    title: string;
    type: number;
    content: () => React.ReactNode;
}

const { TabPane } = Tabs;

export default () => {
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const [detailInfo, setDetailInfo] = useState<any>({});
    const [currentAppId, setCurrentAppId] = useState<number | undefined>(undefined);
    const [activeKey, setActiveKey] = useState(String(STRATEGY_TYPE.PICTURE_CAPTURE));
    const location: any = useLocation();
    const [when, setWhen] = useState(false);
    const { isAbroadStyle } = useSystemComponentStyle();
    const searchStore = useUrlSearchStore();
    const history = useHistory();

    const setSearchStore = (propName: string, value: string | number) => {
        searchStore.set({
            ...searchStore.get(),
            [propName]: value,
        });
    };
    useEffect(() => {
        if (history.action === 'POP') {
            const { activeTab } = searchStore.get();
            setActiveKey(activeTab);
        }
    }, [searchStore]);

    useEffect(() => {
        location.query?.tabKey && setActiveKey(location.query?.tabKey);
        if (history.action === 'PUSH') {
            setSearchStore('activeTab', STRATEGY_TYPE.PICTURE_CAPTURE);
        }
    }, []);

    useEffect(() => {
        const { appId } = searchStore.get();
        if (appId) {
            setCurrentAppId(Number(appId));
        } else {
            if (!inSaaS) {
                setCurrentAppId(Number(getAppGlobalData('APP_ID')));
            } else {
                setCurrentAppId(appList?.[0]?.value);
            }
        }
    }, [appList]);

    useEffect(() => {
        getDetailInfo(activeKey);
    }, [currentAppId, activeKey]);

    const getDetailInfo = (type: string | number) => {
        type &&
            getStrategyListByPage({
                appId: currentAppId,
                configureType: Number(type),
                configureLevelType: 1,
                page: 1,
                pageSize: 1,
            }).then((rs: any) => {
                setDetailInfo(rs.list[0]);
            });
    };
    const openIsWhen = (flag: boolean) => {
        setWhen(flag);
    };

    const selectChange = (value: any) => {
        if(when){
            setWhen(false);
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setWhen(false);
                    setCurrentAppId(value);
                    setSearchStore('appId', value);
                },
                onCancel() {
                    setWhen(true);
                },
            });
        }else{
            setCurrentAppId(value);
            setSearchStore('appId', value);
        }
    };

    const tabChange = (_activeKey: string) => {
        if (when) {
            setWhen(false);
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setWhen(false);
                    setDetailInfo({});
                    setActiveKey(_activeKey);
                    setSearchStore('activeTab', _activeKey);
                },
                onCancel() {
                    setWhen(true);
                    setActiveKey(activeKey);
                },
            });
        } else {
            setDetailInfo({});
            setActiveKey(_activeKey);
            setSearchStore('activeTab', _activeKey);
        }
    };

    const operations = (
        <div style={{ height: 46 }}>
            <Select
                style={{ width: 280 }}
                value={currentAppId}
                placeholder={i18n.t('message', '请选择归属应用')}
                options={appList}
                onChange={selectChange}
            />
        </div>
    );
    const goMore = (url: string) => {
        history.push(url, { choosedAppId: currentAppId || '' });
    };

    const tabItems = [
        Auth.check('@base:@page:capture.setting.capture@action:tab.picture.capture') && {
            title: i18n.t('name', '图片抓拍'),
            type: STRATEGY_TYPE.PICTURE_CAPTURE,
            content: function () {
                return (
                    <Container>
                        <div className="capture-setting-capture-tab-header">
                            <span className="title">{i18n.t('name', '图片抓拍设置')}</span>
                            <Auth code="@base:@page:capture.setting.capture@action:tab.picture.capture:more">
                                <a onClick={() => goMore('/strategy/picture-capture')}>
                                    {i18n.t('action', '更多')}
                                    <IconDoublearrowRight />
                                </a>
                            </Auth>
                        </div>
                        {currentAppId ? (
                            // @ts-ignore
                            <PictureCapture
                                activeKey={activeKey}
                                appId={currentAppId as number}
                                type={this.type}
                                title={i18n.t('name', '抓拍设置')}
                                data={detailInfo}
                                updateData={getDetailInfo}
                                authCode={{
                                    editCode:
                                        '@base:@page:capture.setting.capture@action:tab.picture.capture:edit',
                                }}
                                openIsWhen={openIsWhen}
                            />
                        ) : (
                            <div
                                style={{
                                    height: '500px',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <Empty
                                    imageStyle={{
                                        height: 80,
                                    }}
                                />
                            </div>
                        )}
                    </Container>
                );
            },
        },
        Auth.check('@base:@page:capture.setting.capture@action:tab.face.contrast') && {
            title: i18n.t('name', '人脸对比'),
            type: STRATEGY_TYPE.FACE_CONTRAST,
            content: function () {
                return (
                    <Container>
                        <div className="capture-setting-capture-tab-header">
                            <span className="title">{i18n.t('name', '人脸对比设置')}</span>
                            <Auth code="@base:@page:capture.setting.capture@action:tab.face.contrast:more">
                                <a onClick={() => goMore('/strategy/face-contrast')}>
                                    {i18n.t('action', '更多')}
                                    <IconDoublearrowRight />
                                </a>
                            </Auth>
                        </div>
                        {currentAppId ? (
                            // @ts-ignore
                            <FaceContrast
                                activeKey={activeKey}
                                appId={currentAppId}
                                type={this.type}
                                title={i18n.t('name', '设置信息')}
                                data={detailInfo}
                                updateData={getDetailInfo}
                                authCode={{
                                    editCode:
                                        '@base:@page:capture.setting.capture@action:tab.face.contrast:edit',
                                }}
                                driverCardSnapAuthCode={
                                    '@base:@page:setting.default.face.contrast@action:driver.card.snap'
                                }
                                openIsWhen={openIsWhen}
                            />
                        ) : (
                            <div
                                style={{
                                    height: '500px',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <Empty
                                    imageStyle={{
                                        height: 80,
                                    }}
                                />
                            </div>
                        )}
                    </Container>
                );
            },
        },
    ].filter((item) => item) as TabItem[];

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <Tabs
                    tabBarExtraContent={inSaaS ? operations : null}
                    onChange={tabChange}
                    activeKey={activeKey}
                    hiddenLine={isAbroadStyle}
                >
                    {tabItems.map((item) => (
                        <TabPane tab={item?.title} key={item?.type}>
                            {item?.content?.()}
                        </TabPane>
                    ))}
                </Tabs>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
