import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import PictureCapture from '@/modules/strategy/PictureCaptureStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';

export default () => {
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.PICTURE_CAPTURE}
            subComponent={
                // @ts-ignore
                <PictureCapture
                    title={i18n.t('name', '抓拍配置')}
                    authCode={{
                        editCode: '@base:@page:setting.default.picture.capture@action:edit',
                    }}
                />
            }
        />
    );
};
