import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import FaceContrast from '@/modules/strategy/FaceContrastStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';

export default () => {
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.FACE_CONTRAST}
            subComponent={
                // @ts-ignore
                <FaceContrast
                    title={i18n.t('name', '抓拍条件')}
                    authCode={{
                        editCode: '@base:@page:setting.default.face.contrast@action:edit',
                    }}
                />
            }
        />
    );
};
