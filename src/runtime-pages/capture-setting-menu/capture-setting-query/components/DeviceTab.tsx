import {Input, Select, Form} from '@streamax/poppy';
import type {QueryFormProps} from '@streamax/poppy/lib/pro-form/query-form';
import type {ColumnsType} from '@streamax/poppy/lib/table';
import {
    i18n,
    getAppGlobalData,
    useUrlSearchStore,
    utils,
    StarryAbroadOverflowEllipsisContainer
} from '@base-app/runtime-lib';
// @ts-ignore
import {StarryTable, FleetTreeSelect} from '@base-app/runtime-lib';
import {useHistory} from '@base-app/runtime-lib/core';
import {cloneDeep, omit} from 'lodash';
import {useState, useEffect, useRef} from 'react';
import {getCompanyByPage} from '../../../../service/company';
import {deviceEfficientStrategy} from '../../../../service/strategy';
import type {TabItemProps, ParamsType} from '../index';
import DeviceShow from '@/components/DeviceShow';
import type {DeviceData} from '@/components/DeviceShow';
import {useAppSelect} from '@/hooks/useAppSelect';
import MultiCriteriaSearch from '@/components/MultiCriteriaSearch';

const DeviceTab = ({inSaaS, appList, tabKey, strategyTypeToName}: TabItemProps) => {
    const [appId, setAppId] = useState<string | number | undefined>('');
    const FleetTreeSelectRef = useRef<any>();
    const [form] = Form.useForm<any>();
    const searchStore = useUrlSearchStore();
    const history: any = useHistory();
    const containerRef = useRef<any>();
    const {disableProps, onSelectChange} = useAppSelect(inSaaS);
    // 定义变量动态设置 StarryTable 的 fetchDataAfterMount 属性的值，从详情页回退时设置为true,切换Tab页时设置为false
    let fetchDataFlag;
    const STRATEGY_REFLECT = {
        FACE: 8,
        PICTURE: 11,
    };

    if (history.action === 'POP') {
        fetchDataFlag = true;
    }

    useEffect(() => {
        const appId = appList?.filter((p: any) => p.value != 0)[0]?.value;
        setAppId(appId);
        onSelectChange(appId);
        containerRef.current?.loadDataSource({
            appId,
            page: 1,
            pageSize: 20,
        });
    }, [appList]);
    const handleAppChange = (value: number) => {
        setAppId(value);
        form.setFieldsValue({
            ...form.getFieldsValue(),
            fleetId: undefined,
        });
        onSelectChange(value);
    };

    useEffect(() => {
        FleetTreeSelectRef.current?.loadData();
    }, [appId]);

    const fetchFleet = async () => {
        if (appId) {
            const {list} = await getCompanyByPage({
                page: 1,
                pageSize: 1e8,
                appId,
                // @ts-ignore
                ...utils.general.getSortParam(),
            });
            return list.map((i: any) => {
                return {
                    key: i['fleetId'],
                    id: i['fleetId'],
                    title: i['fleetName'],
                    parentId: i['parentId'],
                    value: i['fleetId'],
                };
            });
        }
        return [];
    };

    const deviceForm: QueryFormProps['items'] | any[] = [
        inSaaS && {
            label: i18n.t('name', '归属应用'),
            name: 'appId',
            field: Select,
            fieldProps: {
                options: appList?.filter((p: any) => p.value != 0),
                placeholder: i18n.t('message', '请选择归属应用'),
                onChange: (value: number) => handleAppChange(value),
            },
            itemProps: {
                initialValue: appList?.filter((p: any) => p.value != 0)?.[0]?.value,
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'fleetId',
            field: FleetTreeSelect,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择车组'),
                treeNodeFilterProp: 'title',
                ref: FleetTreeSelectRef,
                getDataSource: fetchFleet,
            },
        },
        {
            name: 'multiVehicleDeviceInfo',
            field: MultiCriteriaSearch,
        },
    ].filter((item) => item);

    const handleRender = ({list, type, params}: { list: any; type: number; params: any }) => {
        const strategyItem = list.find((p: any) => p.configureType == type);
        const {limited, configureId, configureName} = strategyItem;
        return (
            strategyTypeToName?.({
                limited,
                configureId,
                configureName,
                type,
                ...params,
            }) || '-'
        );
    };

    const deviceColumns: ColumnsType = [
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            ellipsis: true,
            render: (text: string) =>
                <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>,
        },
        {
            title: i18n.t('name', '车辆设备'),
            dataIndex: 'deviceNo',
            ellipsis: true,
            render: (text: string, record: DeviceData) => <DeviceShow deviceInfo={record}/>,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            ellipsis: true,
            render: (text: string) => text || '-',
        },
        {
            title: i18n.t('name', '人脸对比设置'),
            dataIndex: 'faceCompareConfigureName',
            ellipsis: true,
            render: (text: string, record: any) =>
                <StarryAbroadOverflowEllipsisContainer>
                    {
                        handleRender({
                            list: record.effectiveConfigureResDTOList,
                            type: STRATEGY_REFLECT.FACE,
                            params: {
                                prefix: 'faceComparisonStrategy',
                                path: '/strategy/face-contrast/detail',
                                defaultDetailPath: '/strategy/default/face-contrast/detail',
                                authCode: '@base:@page:capture.setting.query@action:default.face.contrast',
                                moreAuthCode:
                                    '@base:@page:capture.setting.query@action:custom.face.contrast',
                            },
                        })
                    }
                </StarryAbroadOverflowEllipsisContainer>,
        },
        {
            title: i18n.t('name', '图片抓拍设置'),
            dataIndex: 'pictureCaptureConfigureName',
            ellipsis: true,
            render: (text: string, record: any) =>
                <StarryAbroadOverflowEllipsisContainer>
                    {
                        handleRender({
                            list: record.effectiveConfigureResDTOList,
                            type: STRATEGY_REFLECT.PICTURE,
                            params: {
                                prefix: 'pictureCaptureStrategy',
                                path: '/strategy/picture-capture/detail',
                                defaultDetailPath: '/strategy/default/picture-capture/detail',
                                authCode:
                                    '@base:@page:capture.setting.query@action:default.picture.capture',
                                moreAuthCode:
                                    '@base:@page:capture.setting.query@action:custom.picture.capture',
                            },
                        })
                    }
                </StarryAbroadOverflowEllipsisContainer>
            ,
        },
    ];

    useEffect(() => {
        if (!inSaaS) {
            fetchDataFlag = false;
            setAppId(getAppGlobalData('APP_ID'));
        }
    }, []);

    useEffect(() => {
        const {appId, fleetId, type, includeSubFleet, vehicleNumberOnly,deviceNoOnly} = searchStore.get();
        if (appId) {
            setAppId(appId);
        }
        if (history.action === 'POP') {
            form.setFieldsValue({
                appId: appId ? Number(appId) : null,
                fleetId: {fleetIds: fleetId, includeSubFleet: includeSubFleet},
                multiVehicleDeviceInfo: {
                    type,
                    value: type==="device"  ? deviceNoOnly:vehicleNumberOnly
                }
            });
        }
    }, []);

    // 重置设置appId的初始值
    const handleReset = () => {
        let {appId: newAppId} = form.getFieldsValue();
        if (!inSaaS) {
            newAppId = Number(getAppGlobalData('APP_ID'));
        }
        setAppId(newAppId);
        return true;
    };

    const fetchPage = (params: any) => {
        const curParams = cloneDeep(params);
        if (!inSaaS) {
            curParams.appId = Number(appId) || 0;
        }
        if (appId && params.appId) {
            if (params.fleetId) {
                curParams.fleetId = (params.fleetId as any).fleetIds;
                curParams.includeSubFleet = curParams.fleetId ? (params.fleetId as any)?.includeSubFleet : undefined;
            }
            if (params?.hasOwnProperty("multiVehicleDeviceInfo")) {
                const { multiVehicleDeviceInfo} = params || {};
                const { value, type } = multiVehicleDeviceInfo || {};
                const key = type=== "device" ?"deviceNoOnly" :"vehicleNumberOnly" ;
                curParams[key] = value?.trim();
                curParams.type = type;
                delete curParams.multiVehicleDeviceInfo;
            }
            searchStore.set({
                ...curParams,
                activeTab: tabKey
            });
            return deviceEfficientStrategy(omit(curParams, "type")).then((rs: any) => {
                return {
                    list: rs.list,
                    total: rs.total,
                };
            });
        }
        // 无应用id即为重置(中台)
        return Promise.resolve({
            list: [],
            total: 0,
        });
    };
    const {page, pageSize} = searchStore.get();
    return (
        <StarryTable
            aroundBordered
            fetchDataFunc={fetchPage}
            ref={containerRef}
            fetchDataAfterMount={fetchDataFlag}
            queryProps={{
                items: deviceForm,
                onReset: handleReset,
                form,
                ...disableProps,
            }}
            pagination={{
                defaultCurrent: Number(page) || 1,
                defaultPageSize: Number(pageSize) || 20,
            }}
            columns={deviceColumns}
            toolbar={{
                columnSetting: {
                    disabledKeys: ['vehicleNumber'],
                    storageKey: '@base:@page:capture.setting.query.device',
                },
            }}
        />
    );
};
export default DeviceTab;
