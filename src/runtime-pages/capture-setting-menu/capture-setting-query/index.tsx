import {i18n, useUrlSearchStore, Auth, getAppGlobalData, useSystemComponentStyle} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { Tabs } from '@streamax/poppy';
// @ts-ignore
import { StarryBreadcrumb, StarryCard, Action } from '@base-app/runtime-lib';
import { useAppList } from '../../../hooks';
import DeviceTab from './components/DeviceTab';
import './index.less';
import { STRATEGY_QUERY_LIMIT, STRATEGY_TYPE } from '@/utils/constant';
import classNames from 'classnames';

const { TabPane } = Tabs;

type PrefixType =
    | 'userStrategy'
    | 'alarmLinkageStrategy'
    | 'evidenceReturnStrategy'
    | 'emailSendingStrategy'
    | 'faceComparisonStrategy'
    | 'dataCleanStrategy'
    | 'autoUploadStrategy'
    | 'alarmWebResponseStrategy'
    | 'pictureCaptureStrategy';
export interface ParamsType {
    page: number;
    pageSize: number;
    appId?: number;
    fleetId: { label: string; value: string } | string;
    vehicleNumber?: string;
    fleetName?: string;
    deviceNo?: string;
    userName?: string;
    includeSubFleet: number;
}

export interface TabItemProps {
    configureNameRender?: (
        text: string,
        record: any,
        prefix: PrefixType,
        key: string,
        route: string,
        param?: number,
    ) => React.ReactNode;
    strategyTypeToName?: (params: any) => React.ReactNode;
    appList?: any[];
    inSaaS?: boolean;
    tabKey: string;
}
const { PICTURE_CAPTURE, FACE_CONTRAST, USER } = STRATEGY_TYPE;
const ValidatedCode = {
    // 图片抓拍
    [PICTURE_CAPTURE]: {
        pageCode: '@base:@page:setting.default.picture.capture',
        tabCode: '@base:@page:capture.setting.capture@action:tab.picture.capture',
    },
    // 人脸对比
    [FACE_CONTRAST]: {
        pageCode: '@base:@page:setting.default.face.contrast',
        tabCode: '@base:@page:capture.setting.capture@action:tab.face.contrast',
    },
};

export default () => {
    const history: any = useHistory();
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const searchStore = useUrlSearchStore();
    const { activeTab, appId } = searchStore.get();
    const { isAbroadStyle } = useSystemComponentStyle();
    const strategyTypeToName = ({
        limited,
        configureId,
        configureName,
        type,
        prefix,
        path,
        defaultDetailPath,
        authCode,
        moreAuthCode,
    }: {
        limited: number;
        configureId: number;
        configureName: string;
        type: number;
        prefix: string;
        path: string;
        defaultDetailPath: string;
        authCode: string;
        moreAuthCode: string;
    }) => {
        if (!configureId) return '-';
        const strategyName = i18n.t(`@i18n:@${prefix}__${configureId}`, configureName);
        const { UNLIMITED, DEFAULT } = STRATEGY_QUERY_LIMIT;
        let jumpable = false;
        switch (limited) {
            // 不受限，可跳转详情
            case UNLIMITED:
                jumpable = true;
                break;
            // 是默认设置，验证是否有页面权限，有则可跳转
            case DEFAULT:
                // 用户设置不走下面的判断，它没独立的默认设置页
                if (type === USER) {
                    jumpable = true;
                    break;
                }
                // 先判断是否有默认设置跳转资源
                if (Auth.check(authCode)) {
                    const { actions = {}, pages = [] } = getAppGlobalData('APP_RESOURCE');
                    const pageAction = actions[authCode];
                    if (pageAction && pageAction.pageId !== null) {
                        // 该按钮有配置跳转页面,验证设置配置tab页资源code,tabCode属于操作资源
                        jumpable = Auth.check(ValidatedCode[type].tabCode);
                    } else {
                        // 未配置跳转链接，验证对应的默认设置详情页资源code,pageCode属于页面资源
                        const page = pages.find(
                            (p: any) => p.resourceCode === ValidatedCode[type].pageCode,
                        );
                        jumpable = !!page;
                    }
                } else {
                    jumpable = false;
                }
                break;
        }
        const jumpFn = () => {
            if (jumpable) {
                // 默认用户设置也走普通设置的跳转逻辑
                if (limited === STRATEGY_QUERY_LIMIT.DEFAULT && type != USER) {
                    // 默认设置跳转
                    Action.openActionUrl({
                        code: authCode,
                        history,
                        url: defaultDetailPath,
                        params: {
                            // 若是跳往设置配置页的tab需要tabKey来定位
                            tabKey: String(type),
                            appId
                        },
                    });
                } else if (type === USER) {
                    // 用户设置跳转
                    history.push({
                        pathname: path,
                        query: {
                            configureId,
                            appId,
                        },
                    });
                } else {
                    // 自定义设置跳转
                    Action.openActionUrl({
                        code: moreAuthCode,
                        history,
                        url: path,
                        params: {
                            // 若是跳往设置配置页的tab需要tabKey来定位
                            tabKey: String(type),
                            configureId,
                            appId
                        },
                    });
                }
            }
        };
        return (
            <Auth code={limited === UNLIMITED ? moreAuthCode : authCode} fellback={strategyName}>
                <span
                    className={classNames({
                        link: jumpable,
                    })}
                    title={strategyName}
                    onClick={jumpFn}
                >
                    {strategyName}
                </span>
            </Auth>
        );
    };
    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="strategy-query-container">
                    <Tabs activeKey={activeTab || '1'} hiddenLine={isAbroadStyle}>
                        <TabPane key="1" tab={i18n.t('name', '设备查询')}>
                            <DeviceTab
                                inSaaS={inSaaS}
                                appList={appList}
                                tabKey={'1'}
                                strategyTypeToName={strategyTypeToName}
                            />
                        </TabPane>
                    </Tabs>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
