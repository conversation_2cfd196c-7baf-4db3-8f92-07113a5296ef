import {StarryAbroadOverflowEllipsisContainer, useSystemComponentStyle} from "@base-app/runtime-lib";
import {
    StarryBreadcrumb,
    StarryCard,
    StarryTable,
    StarryInfoBlock,
    Action,
    StarryModal,
} from '@base-app/runtime-lib';
import { Form, Button, Space, message, Switch, Tooltip, Table } from '@streamax/poppy';
import React, { useEffect, useState, useRef } from 'react';
import { i18n, Auth, getAppGlobalData, reLoadLanguage } from '@base-app/runtime-lib';
import { Link, useLocation } from '@base-app/runtime-lib/core';
import { utils } from '@base-app/runtime-lib';
import { IconEdit, IconDelete, IconSort, IconRequestFill } from '@streamax/poppy-icons';
import classnames from 'classnames';
import {
    fetchResourcePageList,
    fetchResourceDetail,
    fetchResourceUnLock,
    fetchResourceLockWapper,
    deleteResource,
} from '../../../service/resource';
import './index.less';
import {
    Descriptions,
    ListDataContainer,
    OverflowEllipsisContainer,
    TableColumnSetting,
} from '@streamax/starry-components';

interface GroupRenderType {
    (text?: any, record?: any): string | React.ReactNode | void;
}

interface GroupType {
    label: string;
    field: string;
    render?: GroupRenderType;
}

const ApplicationPageDetail = (props: any) => {
    const { match } = props;
    const pageId = match.params.pageId;
    const appId = match.params.appId || getAppGlobalData('APP_ID');
    const [pageInfo, setPageInfo] = useState<any>({});
    const tableRef = useRef<any>(null);
    const {isAbroadStyle} = useSystemComponentStyle();
    const stateRef = useRef<1 | 2 | null>(null);
    const {
        // @ts-ignore
        query: { fromPage },
    } = useLocation();

    useEffect(() => {
        getPageInfo();
    }, []);

    const InfoGroup: GroupType[] = [
        {
            label: i18n.t('name', '页面名称'),
            field: 'resourceName',
            render: (text, record) => (
                <span title={i18n.t(`@i18n:@page__${record.resourceCode}`, text)}>
                    {i18n.t(`@i18n:@page__${record.resourceCode}`, text)}
                </span>
            ),
        },
        {
            label: i18n.t('name', '页面编码'),
            field: 'resourceCode',
        },
        {
            label: i18n.t('name', '状态'),
            field: 'state',
            render: (text) => (
                <span>
                    {text === 2 && i18n.t('state', '停用')}
                    {text === 1 && i18n.t('state', '启用')}
                </span>
            ),
        },
        {
            label: i18n.t('name', '归属应用'),
            field: 'appId',
            render: (text, record) => (
                <span title={i18n.t(`@i18n:@app__${text}`, record.appName)}>
                    {i18n.t(`@i18n:@app__${text}`, record.appName)}
                </span>
            ),
        },
        {
            label: i18n.t('name', '访问路径'),
            field: 'resourceUrl',
        },
        {
            label: i18n.t('name', '权限管理'),
            field: 'managementSwitch',
            render: (text) => (text === 0 ? i18n.t('state', '是') : i18n.t('state', '否')),
        },
        {
            label: i18n.t('name', '页面级别'),
            field: 'level',
            render: (text) => (text === 1 ? i18n.t('state', '根页面') : i18n.t('state', '子页面')),
        },
        {
            label: i18n.t('name', '上级页面'),
            field: 'parentName',
            render: (text, record) => (
                <span title={i18n.t(`@i18n:@page__${record.parendCode}`, text)}>
                    {record.parentId ? i18n.t(`@i18n:@page__${record.parentCode}`, text) : '-'}
                </span>
            ),
        },
        {
            label: i18n.t('name', '创建时间'),
            field: 'createTime',
            render: (text) => text && utils.formator.zeroTimeStampToFormatTime(text),
        },
    ];

    const columns = [
        {
            title: i18n.t('name', '操作名称'),
            dataIndex: 'resourceName',
            key: 'resourceName',
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => (
                <Tooltip title={i18n.t(`@i18n:@operation__${record.resourceCode}`, text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {i18n.t(`@i18n:@operation__${record.resourceCode}`, text)}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作编码'),
            dataIndex: 'resourceCode',
            key: 'resourceCode',
            ellipsis: { showTitle: false },
            render: (text: string) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'state',
            filterMultiple: false,
            ellipsis: true,
            width: 180,

            filters: [
                {
                    text: i18n.t('state', '启用'),
                    value: 1,
                },
                {
                    text: i18n.t('state', '停用'),
                    value: 2,
                },
            ],
            render: (text: any, record: any) => {
                return (
                    <Space>
                        <div
                            className={classnames(
                                'state-sign',
                                `state-sign-${text === 1 ? 'green' : 'grey'}`,
                            )}
                        ></div>
                        <span>
                            {text === 1 ? i18n.t('state', '启用') : i18n.t('state', '停用')}
                        </span>
                        <Switch
                            size="small"
                            checked={text === 1}
                            onChange={() => {
                                text === 1 ? lock(record) : unlock(record);
                            }}
                        />
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '权限管理'),
            dataIndex: 'managementSwitch',
            ellipsis: { showTitle: false },
            render: (text: any) => (text === 0 ? i18n.t('cstate', '是') : i18n.t('state', '否')),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            width: 120,
            render: (text: any, record: any) => {
                const { resourceId, resourceCode, parentCode } = record;
                return (
                    <Space className="operate-btn">
                        <Auth code="@base:@page:application.manage:page.detail@action:opreation.edit">
                            <Link
                                to={`/applicationManage/edit-application-Operation/${appId}/${resourceId}?resourceCode=${resourceCode}&parentCode=${parentCode}&from=${encodeURIComponent(
                                    location.pathname + location.search,
                                )}&fromPage=${fromPage}`}
                            >
                                <Tooltip title={i18n.t('action', '编辑')}>
                                    <IconEdit />
                                </Tooltip>
                            </Link>
                        </Auth>
                        {record.state === 2 && record.createType !== 1 && (
                            <Tooltip title={i18n.t('action', '删除')}>
                                <span onClick={() => deleteOperation(record)}>
                                    <IconDelete />
                                </span>
                            </Tooltip>
                        )}
                    </Space>
                );
            },
        },
    ];

    // 启用
    function unlock(record: any) {
        const { resourceId, resourceName, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '启用确认'),
            content: i18n.t('message', '确认启用{resourceName}操作吗?', {
                resourceName: i18n.t(`@i18n:@operation__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: () => {
                fetchResourceUnLock({
                    resourceIds: resourceId.toString(),
                    appId,
                })
                    .then(() => {
                        message.success(i18n.t('message', '操作成功'));
                        tableRef.current.loadDataSource({
                            page: 1,
                        });
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    // 停用
    function lock(record: any) {
        const { resourceId, resourceName, resourceInitType, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '停用确认'),
            content: i18n.t('message', '确认停用{resourceName}操作吗?', {
                resourceName: i18n.t(`@i18n:@operation__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: () => {
                fetchResourceLockWapper(
                    resourceInitType,
                    {
                        resourceIds: resourceId.toString(),
                        appId,
                    },
                    false,
                )
                    .then(async (success: any) => {
                        if (!success) {
                            return;
                        }
                        message.success(i18n.t('message', '操作成功'));
                        await reLoadLanguage(true, true);
                        tableRef.current.loadDataSource({
                            page: 1,
                        });
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    // 删除操作
    function deleteOperation(record: any) {
        const { resourceId, resourceName, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确认删除{resourceName}操作吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: () => {
                deleteResource({
                    resourceIds: resourceId,
                    appId,
                })
                    .then(() => {
                        message.success(i18n.t('message', '操作成功'));
                        tableRef.current.loadDataSource({
                            page: 1,
                        });
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    // 获取操作列表
    async function getTableList(params: any) {
        const data = await fetchResourcePageList({
            ...params,
            resourceType: 3,
            parentId: pageId,
            appId,
            complexSort: 'orderBy sortValue asc',
        });
        return Promise.resolve({
            list: data.list || [],
            total: data.total || 0,
        });
    }

    function getPageInfo() {
        fetchResourceDetail({
            resourceId: pageId,
            appId,
        }).then((res: any) => {
            setPageInfo(res || {});
        });
    }

    function renderInfoGroup(group: GroupType[]) {
        return(
        <Descriptions>
            {group.map(({ label, field, render }) => {
                return (
                    <Descriptions.Item key={field} label={label} ellipsis={true}>
                        {render ? render(pageInfo[field], pageInfo) : pageInfo[field]}
                    </Descriptions.Item>
                );
            })}
        </Descriptions>
        );

    }

    // 启用
    function unlockPage() {
        const { resourceId, resourceName, resourceCode } = pageInfo;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '启用确认'),
            content: i18n.t('message', '确认启用{resourceName}页面吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: () => {
                fetchResourceUnLock({
                    resourceIds: resourceId.toString(),
                    appId,
                })
                    .then(() => {
                        message.success(i18n.t('message', '操作成功'));
                        getPageInfo();
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }
    const onTableChange = (page: any, filter: any, sort: any) => {
        const state = filter.state ? filter.state[0] : '';
        tableRef.current.loadDataSource({
            page: 1,
            state,
        });
    };
    // 停用
    function lockPage() {
        const { resourceId, resourceName, resourceInitType, resourceCode } = pageInfo;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '停用确认'),
            content: i18n.t('message', '确认停用{resourceName}页面吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: () => {
                fetchResourceLockWapper(
                    resourceInitType,
                    {
                        resourceIds: resourceId.toString(),
                        appId,
                    },
                    true,
                )
                    .then(async (success) => {
                        if (!success) {
                            return;
                        }
                        message.success(i18n.t('message', '操作成功'));
                        await reLoadLanguage(true, true);
                        getPageInfo();
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(columns, {
        disabledKeys: ['operate'],
        storageKey: '@base:@page:application.page.detail',
    });
    return (
        <StarryBreadcrumb className="app-page-detail">
            <StarryCard
                title={
                    <div>
                        {i18n.t(`@i18n:@page__${pageInfo.resourceCode}`, pageInfo.resourceName)}
                        {pageInfo.state && (
                            <span
                                className={classnames(
                                    'app-details-title-sign',
                                    pageInfo.state === 1 ? 'success' : 'normal',
                                )}
                            >
                                {pageInfo.state === 2 && i18n.t('state', '停用')}
                                {pageInfo.state === 1 && i18n.t('state', '启用')}
                            </span>
                        )}
                    </div>
                }
                operation={
                    <>
                        {pageInfo.state === 2 && (
                            <Button type="primary" onClick={() => unlockPage()}>
                                {i18n.t('state', '启用')}
                            </Button>
                        )}
                        {pageInfo.state === 1 && (
                            <Button onClick={() => lockPage()}>{i18n.t('state', '停用')}</Button>
                        )}
                    </>
                }
            >
                <StarryInfoBlock
                    title={i18n.t('name', '基本信息')}
                    operation={
                        <Auth code="@base:@page:application.manage:page.detail@action:page.edit">
                            <Link
                                to={`/applicationManage/edit-application-page/${appId}/${
                                    match.params.pageId
                                }${location.search}&resourceCode=${encodeURIComponent(
                                    pageInfo.resourceCode,
                                )}`}
                            >
                                {i18n.t('action', '编辑')}
                            </Link>
                        </Auth>
                    }
                    border={isAbroadStyle ? undefined: "bottom" }
                >
                    {renderInfoGroup(InfoGroup)}
                </StarryInfoBlock>
                <StarryInfoBlock title={i18n.t('name', '页面操作')}>
                    <div className="app-manage-operation">
                        <ListDataContainer
                            ref={tableRef}
                            getDataSource={getTableList}
                            toolbar={{
                                extraLeft: (
                                    <Action
                                        code="@base:@page:application.manage:page.detail@action:opreation.sort"
                                        url="/applicationManage/applicationActionSort/:appId"
                                        params={{
                                            pageId: pageId,
                                            appId: appId,
                                        }}
                                    >
                                        <Button icon={<IconSort />}>
                                            {i18n.t('name', '排序')}
                                        </Button>
                                    </Action>
                                ),
                                extraIconBtns: [
                                    <TableColumnSetting
                                        key="setting"
                                        {...tableColumnSettingProps}
                                    />,
                                ],
                            }}
                            listRender={(data) => {
                                return (
                                    <Table
                                        dataSource={data}
                                        columns={tableColumns}
                                        rowKey="applicationId"
                                        onChange={onTableChange}
                                    />
                                );
                            }}
                        />
                    </div>
                </StarryInfoBlock>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default ApplicationPageDetail;
