import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useSubmitFn } from '@streamax/hooks';
import {
    Button,
    Form,
    Input,
    message,
    Radio,
    Select,
    Space,
    Upload,
    Tooltip,
    Spin,
    Image,
    Container,
} from '@streamax/poppy';
import { IconDeleteFill } from '@streamax/poppy-icons';
import {
    getAppGlobalData,
    i18n,
    reLoadLanguage,
    RouterPrompt,
    StarryAbroadIcon,
    StarryAbroadLRLayout,
    useSystemComponentStyle,
    utils,
} from '@base-app/runtime-lib';
import {
    StarryBreadcrumb,
    StarryCard,
    StarryAbroadFormItem,
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { useEffect, useRef, useState } from 'react';
import InternationalInput from '../../../components/InternationalInput';
import { fetchFileDownloadUrl, fileUploadStream } from '../../../service/gss';
import {
    addResource,
    editResource,
    fetchResourceDetail,
    fetchResourcePageList,
} from '../../../service/resource';
import {
    checkFieldSpace,
    validatorResourceCode,
} from '../../../utils/commonFun';
import './index.scoped.less';
import { MenuClickResponseTypeEnum, OpenNewTabEnum } from '../type';
import { openNewTabOptions } from '../constants';
import { getUserAppEntrys } from '@/service/entry';
import { CacheType } from '@/types';
import { updateSystemInitConfig } from '@/runtime-lib/utils/general';
import { RspFormLayout } from '@streamax/responsive-layout';

//@ts-ignore
const { encryptPassword: encrypToken } = utils.general;
const { Option } = Select;
const FILE_MAX_SIZE = 20;

interface PageType {
    resourceId: string | number;
    resourceName: string;
    [name: string]: any;
}

function getBase64(img: any, callback: any) {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
}

const AddApplicationMenu = (props: any) => {
    const history = useHistory();
    const { match, location } = props;
    const { appId } = match.params;
    const { from, resourceCode } = location.query;
    const menuId = match.params.menuId || null;
    const [dataSource, setDataSource] = useState<any>({});
    const [urlList, setUrlList] = useState<PageType[]>([]);
    const [menuList, setMenuList] = useState<PageType[]>([]);
    const [selectMenuLevel, setSelectMenuLevel] = useState<any>(2); // 所选择的菜单级别
    const [clickType, setClickType] = useState<any>(1);
    const [showType, setShowType] = useState<OpenNewTabEnum>(
        OpenNewTabEnum.SELF,
    );
    const [internationalInfo, setInternationalInfo] = useState<any>({});
    const [imageUrl, setImageUrl] = useState('');
    const [loading, setLoading] = useState(false);
    const [initLoading, setInitLoading] = useState(menuId ? true : false);
    const [when, setWhen] = useState(true);
    const [form] = Form.useForm();
    const formRef = useRef<any>(null);
    const { isAbroadStyle } = useSystemComponentStyle();
    // 缓存服务入口访问路径和跳转链接数据
    const cacheUrlInfo = useRef<{
        skipUrl?: string;
        accessPath?: string;
    }>();

    const resourceInitTypeRef = useRef(0);

    const IconId = useRef<any>(null);

    useEffect(() => {
        if (menuId) {
            getMenuInfo();
        }
        getUrlList();
        getMenuList();
    }, []);

    function getMenuInfo() {
        fetchResourceDetail({
            resourceId: menuId,
            appId,
        }).then((res: any) => {
            const {
                clickResponseType,
                level,
                icon,
                iconUrl,
                resourceInitType,
                resourceCode,
                resourceName,
                openNewTab,
            } = res;
            resourceInitTypeRef.current = resourceInitType;
            IconId.current = icon;
            setImageUrl(iconUrl);
            setSelectMenuLevel(level);
            setClickType(clickResponseType);
            setShowType(openNewTab);
            setDataSource(res || {});
            form.setFieldsValue({
                ...res,
                resourceName: i18n.t(
                    `@i18n:@menu__${resourceCode}`,
                    resourceName,
                ),
            });
        });
    }

    // 获取所有页面资源链接
    function getUrlList() {
        fetchResourcePageList({
            resourceType: 2,
            page: 1,
            state: 1,
            level: 1,
            pageSize: 10000,
            appId,
        }).then((res: any) => {
            setUrlList(res.list || []);
        });
    }

    // 获取菜单资源
    function getMenuList() {
        fetchResourcePageList({
            resourceType: 1,
            page: 1,
            pageSize: 10000,
            state: 1,
            // createType: createType || 2,
            appId,
        }).then((res: any) => {
            setMenuList(res.list || []);
        });
    }

    async function onFinish(values: any) {
        const params = values;
        const { level, pageCode, resourceName } = values;
        // 服务入口处理
        if (level === 1) {
            delete params.parentCode;
        }
        if (pageCode) {
            const page: any = urlList.find(
                (item: any) => item.resourceId === pageCode,
            );
            page && (values.resourceUrl = page?.resourceUrl || '');
        }
        const { translationList, objectName, langId } = internationalInfo;
        // if (selectMenuLevel === 2 && !imageUrl) {
        //     message.error(
        //         selectMenuLevel === 2
        //             ? i18n.t('message', '图标不能为空')
        //             : i18n.t('message', '应用Logo不能为空'),
        //     );
        //     return;
        // }
        if (menuId) {
            const resourceId = await editResource({
                ...params,
                icon: IconId.current,
                resourceType: 1,
                resourceId: menuId,
                entryId: langId,
                appId,
                resourceName,
                translationList:
                    translationList ||
                    (i18n.exists(`@i18n:@menu__${values.resourceCode}`)
                        ? [
                              {
                                  langType: getAppGlobalData('APP_LANG'),
                                  translationValue: values.resourceName,
                              },
                          ]
                        : []),
            });
            setWhen(false);
            if (resourceInitTypeRef.current === 0) {
                const url = from.split('?')[0] + '?tab=menu&keys=' + resourceId;
                setWhen(false);
                message.success(i18n.t('message', '操作成功'));
                window.location.replace(url);
                return;
            }
            message.success(i18n.t('message', '操作成功'));
            reLoadLanguage && (await reLoadLanguage(true, true));
            // history.goBack();
        } else {
            await addResource({
                ...params,
                icon: IconId.current,
                resourceType: 1,
                createType: 2,
                appId,
                translationList: translationList || [],
            });

            setWhen(false);
            reLoadLanguage && (await reLoadLanguage(true, true));
            message.success(i18n.t('message', '操作成功'));
            // history.goBack();
        }
        // 刷新拿最新的资源和数据
        setTimeout(() => {
            updateSystemInitConfig(from);
        }, 200);
    }
    const [onFinishLock, submitLoading] = useSubmitFn(onFinish);

    // 菜单编码失焦时，将内容转为大写
    function resourceCodeInputBlur() {
        const resourceCodeValue = form.getFieldValue('resourceCode');
        form.setFieldsValue({
            resourceCode: (resourceCodeValue || '').toUpperCase(),
        });
    }
    function beforeUploadLogo(file: any) {
        if (file.type !== 'image/png') {
            message.error(
                i18n.t('message','请上传小于{size}KB，且背景透明、内部图案颜色为白色的PNG格式图片', {
                    size: FILE_MAX_SIZE,
                }),
            );
            return Upload.LIST_IGNORE;
        }
        if (file.size > 20 * 1024) {
            message.error(
                i18n.t('message', '请上传小于{size}KB，且背景透明、内部图案颜色为白色的PNG格式图片', {
                    size: FILE_MAX_SIZE,
                }),
            );
            return Upload.LIST_IGNORE;
        }
        return true;
    }

    function handleChange(info: any) {
        if (info.file.status === 'uploading') {
            setLoading(true);
            return;
        }
        if (info.file.status === 'done') {
            getBase64(info.file.originFileObj, () => {
                setLoading(false);
                form.setFieldsValue({
                    icon: '1',
                });
            });
        }
    }

    function customRequest() {
        return function ({ file, onSuccess, onError }: any) {
            const formData = new FormData();
            formData.append('file', file);
            fileUploadStream(formData, {
                // @ts-ignore
                _abs: encrypToken(window.localStorage.getItem('AUTH_TOKEN')),
            })
                .then(async (res: any) => {
                    IconId.current = res;
                    getImgUrl(res, onSuccess, onError);
                })
                .catch(() => {
                    onError();
                });
        };
    }

    function getImgUrl(fileId: string | number, onSuccess: any, onError: any) {
        fetchFileDownloadUrl(
            {
                fileIdList: fileId.toString(),
                validTime: 365,
            },
            { _tenantId: 0, _appId: 0 },
        )
            .then((data: any) => {
                if (data[0]) {
                    const fileUrl = data[0].fileUrl;
                    setImageUrl(fileUrl);
                    onSuccess();
                }
            })
            .catch(() => {
                onError();
            });
    }
    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '菜单名称不能为空'));
    };
    const checkSpaceAndLength = (rule: any, value: any) => {
        if (value) {
            if (/[\\/*?"<>|]/g.test(value)) {
                return Promise.reject(i18n.t('name', '非法字符'));
            } else if (!value.trim()) {
                return checkFieldSpace(
                    value,
                    i18n.t('message', '菜单编码不能为空'),
                );
            } else if (value.trim().length < 2 || value.trim().length > 100) {
                return Promise.reject(
                    i18n.t('message', '至少填写2个字符,最多填写100个字符'),
                );
            } else {
                return Promise.resolve();
            }
        } else {
            return checkFieldSpace(
                value,
                i18n.t('message', '菜单编码不能为空'),
            );
        }
    };

    const handleLevelChange = (value: any) => {
        setSelectMenuLevel(value);
        form.setFieldsValue({
            resourceUrl: '',
            parentCode: null,
            openNewTab: undefined,
            clickResponseType: 1,
        });
        setClickType(1);
        setShowType(OpenNewTabEnum.SELF);
        cacheUrlInfo.current = undefined;
    };

    const getClickTypeShowFormItem = () => {
        const currentPageOpen =
            showType === OpenNewTabEnum.SELF && selectMenuLevel === 1;
        const options = [
            {
                value: 1,
                label: i18n.t('action', '页面'),
                show: true,
            },
            {
                value: 2,
                label: i18n.t('action', '跳转链接'),
                show: !currentPageOpen,
            },
        ].filter((item) => item.show);

        return (
            <StarryAbroadFormItem
                label={i18n.t('name', '点击展示')}
                name="clickResponseType"
                rules={[
                    {
                        required: [1, 3].includes(selectMenuLevel),
                    },
                ]}
                initialValue={1}
            >
                <Select
                    placeholder={i18n.t('message', '请选择')}
                    disabled={menuId && dataSource.createType === 1}
                    onChange={(v) => {
                        setClickType(v);
                        // 服务入口切换缓存数据
                        if (selectMenuLevel === 1) {
                            const addUrl =
                                v === 1
                                    ? cacheUrlInfo.current?.accessPath
                                    : cacheUrlInfo.current?.skipUrl;
                            const editUrl =
                                dataSource.clickResponseType === v &&
                                dataSource?.openNewTab === showType
                                    ? dataSource?.resourceUrl
                                    : undefined;
                            const resourceUrl = addUrl ?? editUrl;
                            form.setFieldsValue({
                                resourceUrl,
                            });
                        }
                    }}
                >
                    {options.map((item) => (
                        <Option value={item.value} key={item.value}>
                            {item.label}
                        </Option>
                    ))}
                </Select>
            </StarryAbroadFormItem>
        );
    };
    const openNewTabFormItem = (
        <StarryAbroadFormItem
            name="openNewTab"
            label={i18n.t('name', '展示方式')}
            required
            initialValue={OpenNewTabEnum.SELF}
        >
            <Select
                placeholder={i18n.t('message', '请选择展示方式')}
                disabled={menuId && dataSource.createType === 1}
                onChange={(v) => {
                    setShowType(v as OpenNewTabEnum);
                    cacheUrlInfo.current = undefined;
                    if (v === OpenNewTabEnum.SELF) {
                        form.setFieldsValue({
                              clickResponseType: 1,
                        });
                    }
                }}
            >
                {openNewTabOptions.map((item) => (
                    <Option value={item.value} key={item.value}>
                        {item.label}
                    </Option>
                ))}
            </Select>
        </StarryAbroadFormItem>
    );

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <Spin spinning={initLoading}>
                <StarryCard
                    title={i18n.t('name', '基本信息')}
                    className="add-application"
                >
                    <Form
                        form={form}
                        ref={formRef}
                        labelWidth={80}
                        layout="vertical"
                        onFinish={onFinishLock}
                    >
                        <Container style={{ paddingBottom: 0 }}>
                                <RspFormLayout layoutType="auto">
                                <StarryAbroadFormItem
                                    className={'international-input-form-item'}
                                    style={{paddingRight: 28}}
                                    label={i18n.t('name', '菜单名称')}
                                    name="resourceName"
                                    validateFirst
                                    rules={[
                                        {
                                            required: true,
                                            validator: checkSpaceName,
                                        },
                                        {
                                            min: 1,
                                            type: 'string',
                                        },
                                        {
                                            max: 50,
                                            type: 'string',
                                        },
                                        {
                                            validator:
                                                utils.validator
                                                    .illegalCharacter,
                                        },
                                    ]}
                                >
                                    <InternationalInput
                                        allowClear
                                        maxLength={50}
                                        placeholder={i18n.t(
                                            'message',
                                            '请输入菜单名称',
                                        )}
                                        modalType={menuId ? 'edit' : 'add'}
                                        internationalType="menu"
                                        // menu、page、operation的langKey是后面拼接code，则应该传resourceCode
                                        entryIdOrCode={resourceCode}
                                        entryKey={
                                            (dataSource || {}).resourceName
                                        }
                                        onSave={(values) =>
                                            setInternationalInfo(values)
                                        }
                                        onInit={() => setInitLoading(false)}
                                    />
                                    {/* <Input
                                    maxLength={50}
                                    placeholder={i18n.t('message', '请输入')}
                                /> */}
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '菜单编码')}
                                    name="resourceCode"
                                    rules={[
                                        {
                                            required: true,
                                            validator: checkSpaceAndLength,
                                        },
                                    ]}
                                >
                                    <Input
                                        allowClear
                                        onBlur={resourceCodeInputBlur}
                                        disabled={menuId}
                                        maxLength={100}
                                        placeholder={i18n.t(
                                            'message',
                                            '请输入菜单编码',
                                        )}
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '权限管理')}
                                    name="managementSwitch"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    initialValue={0}
                                >
                                    <Radio.Group>
                                        <Radio value={0}>
                                            {i18n.t('action', '是')}
                                        </Radio>
                                        <Radio value={1}>
                                            {i18n.t('action', '否')}
                                        </Radio>
                                    </Radio.Group>
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '菜单级别')}
                                    name="level"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    initialValue={2}
                                >
                                    <Select
                                        disabled={menuId}
                                        placeholder={i18n.t(
                                            'message',
                                            '请选择菜单级别',
                                        )}
                                        onChange={(v) => handleLevelChange(v)}
                                    >
                                        <Option value={1}>
                                            {i18n.t('action', '服务入口')}
                                        </Option>
                                        <Option value={2}>
                                            {i18n.t('action', '一级')}
                                        </Option>
                                        <Option value={3}>
                                            {i18n.t('action', '二级')}
                                        </Option>
                                    </Select>
                                </StarryAbroadFormItem>
                                {selectMenuLevel === 1 && openNewTabFormItem}

                                {selectMenuLevel === 2 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '服务入口')}
                                        name="parentCode"
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            disabled={
                                                menuId &&
                                                dataSource.createType === 1
                                            }
                                            placeholder={i18n.t(
                                                'message',
                                                '请选择服务入口',
                                            )}
                                            filterOption={(
                                                input,
                                                option: any,
                                            ) => {
                                                return (
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(
                                                            input.toLowerCase(),
                                                        ) >= 0
                                                );
                                            }}
                                        >
                                            {menuList.map(
                                                (item) =>
                                                    item.level === 1 && (
                                                        <Option
                                                            value={
                                                                item.resourceCode
                                                            }
                                                            key={
                                                                item.resourceCode
                                                            }
                                                        >
                                                            {i18n.t(
                                                                `@i18n:@menu__${item.resourceCode}`,
                                                                item.resourceName,
                                                            )}
                                                        </Option>
                                                    ),
                                            )}
                                        </Select>
                                    </StarryAbroadFormItem>
                                )}
                                {selectMenuLevel === 3 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '一级菜单')}
                                        name="parentCode"
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder={i18n.t(
                                                'message',
                                                '请选择一级菜单',
                                            )}
                                            disabled={
                                                menuId &&
                                                dataSource.createType === 1
                                            }
                                            filterOption={(
                                                input,
                                                option: any,
                                            ) => {
                                                return (
                                                    option.children
                                                        .toLowerCase()
                                                        .indexOf(
                                                            input.toLowerCase(),
                                                        ) >= 0
                                                );
                                            }}
                                        >
                                            {menuList.map(
                                                (item) =>
                                                    item.level === 2 && (
                                                        <Option
                                                            value={
                                                                item.resourceCode
                                                            }
                                                            key={
                                                                item.resourceCode
                                                            }
                                                        >
                                                            {i18n.t(
                                                                `@i18n:@menu__${item.resourceCode}`,
                                                                item.resourceName,
                                                            )}
                                                        </Option>
                                                    ),
                                            )}
                                        </Select>
                                    </StarryAbroadFormItem>
                                )}
                                {getClickTypeShowFormItem()}
                                {selectMenuLevel !== 1 && clickType === 1 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '跳转页面')}
                                        name="pageCode"
                                        rules={[
                                            {
                                                required: selectMenuLevel === 3,
                                            },
                                        ]}
                                    >
                                        <Select
                                            allowClear
                                            showSearch
                                            placeholder={i18n.t(
                                                'message',
                                                '请选择跳转页面',
                                            )}
                                            disabled={
                                                menuId &&
                                                dataSource.createType === 1
                                            }
                                            filterOption={(
                                                input,
                                                option: any,
                                            ) => {
                                                return (
                                                    option.children.props.children
                                                        .toLowerCase()
                                                        .indexOf(
                                                            input.toLowerCase(),
                                                        ) >= 0
                                                );
                                            }}
                                        >
                                            {urlList.map((item) => (
                                                <Option
                                                    value={item.resourceCode}
                                                    key={item.resourceCode}
                                                >
                                                    <Tooltip
                                                        overlay={
                                                            item.resourceUrl
                                                        }
                                                        placement="right"
                                                    >
                                                        {i18n.t(
                                                            `@i18n:@page__${item.resourceCode}`,
                                                            item.resourceName,
                                                        )}
                                                    </Tooltip>
                                                </Option>
                                            ))}
                                        </Select>
                                    </StarryAbroadFormItem>
                                )}
                                {selectMenuLevel === 1 && clickType === 1 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '访问路径')}
                                        name="resourceUrl"
                                        rules={[
                                            {
                                                required: true,
                                            },
                                            {
                                                min: 1,
                                                type: 'string',
                                            },
                                            {
                                                max: 500,
                                                type: 'string',
                                            },
                                            {
                                                pattern: /^\/.*/,
                                                message: i18n.t(
                                                    'message',
                                                    '访问路径必须以/开头',
                                                ),
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            maxLength={500}
                                            onChange={(e) =>
                                                (cacheUrlInfo.current = {
                                                    ...(cacheUrlInfo.current ||
                                                        {}),
                                                    accessPath: e.target.value,
                                                })
                                            }
                                            placeholder={i18n.t(
                                                'message',
                                                '请输入',
                                            )}
                                            disabled={
                                                menuId &&
                                                dataSource.createType === 1
                                            }
                                        />
                                    </StarryAbroadFormItem>
                                )}
                                {clickType === 2 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '跳转链接')}
                                        name="resourceUrl"
                                        rules={[
                                            {
                                                required: [1, 3].includes(
                                                    selectMenuLevel,
                                                ),
                                            },
                                            {
                                                min: 1,
                                                type: 'string',
                                            },
                                            {
                                                max: 500,
                                                type: 'string',
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            maxLength={500}
                                            onChange={(e) =>
                                                (cacheUrlInfo.current = {
                                                    ...(cacheUrlInfo.current ||
                                                        {}),
                                                    skipUrl: e.target.value,
                                                })
                                            }
                                            placeholder={i18n.t(
                                                'message',
                                                '请输入',
                                            )}
                                            disabled={
                                                menuId &&
                                                dataSource.createType === 1
                                            }
                                        />
                                    </StarryAbroadFormItem>
                                )}
                                {selectMenuLevel !== 1 &&
                                    form.getFieldValue('clickResponseType') ===
                                        MenuClickResponseTypeEnum.jump &&
                                    openNewTabFormItem}
                                {(
                                <RspFormLayout.SingleRow>
                                    <Form.Item
                                        validateFirst
                                        label={i18n.t('name', '图标')}
                                        name="icon"
                                    >
                                        <StarryAbroadFormItem
                                            noStyle
                                            style={{ width: 340 }}
                                        >
                                            <Upload
                                                name="icon"
                                                withCredentials
                                                listType="picture-card"
                                                className="avatar-uploader"
                                                showUploadList={false}
                                                accept="image/png"
                                                beforeUpload={beforeUploadLogo}
                                                customRequest={customRequest()}
                                                onChange={handleChange}
                                            >
                                                {imageUrl ? (
                                                    <Image
                                                        fit
                                                        width="100%"
                                                        height="100%"
                                                        alt="logo"
                                                        src={imageUrl}
                                                        className={
                                                            'avatar-uploader-image'
                                                        }
                                                        preview={{
                                                            mask: (
                                                                <StarryAbroadIcon>
                                                                    <IconDeleteFill
                                                                        className="delete-icon"
                                                                        onClick={(
                                                                            e: React.MouseEvent,
                                                                        ) => {
                                                                            e.stopPropagation();
                                                                            setImageUrl(
                                                                                '',
                                                                            );
                                                                            IconId.current =
                                                                                null;
                                                                        }}
                                                                    />
                                                                </StarryAbroadIcon>
                                                            ),
                                                            visible: false,
                                                        }}
                                                    />
                                                ) : (
                                                    <div>
                                                        {loading ? (
                                                            <StarryAbroadIcon>
                                                                <LoadingOutlined />
                                                            </StarryAbroadIcon>
                                                        ) : (
                                                            <StarryAbroadIcon>
                                                                <PlusOutlined />
                                                            </StarryAbroadIcon>
                                                        )}
                                                        <div
                                                            style={{
                                                                marginTop: 8,
                                                            }}
                                                        >
                                                            {i18n.t(
                                                                'action',
                                                                '上传图片',
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </Upload>
                                        </StarryAbroadFormItem>
                                        <span className="info-text">
                                            {i18n.t(
                                                'message',
                                                '请上传小于{size}KB，且背景透明、内部图案颜色为白色的PNG格式图片',
                                                {
                                                    size: FILE_MAX_SIZE,
                                                },
                                            )}
                                        </span>
                                    </Form.Item>
                                </RspFormLayout.SingleRow>
                                )}
                                </RspFormLayout>
                        </Container>

                        <StarryAbroadLRLayout>
                            <Button
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={submitLoading}
                            >
                                {i18n.t('action', '保存')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </Form>
                </StarryCard>
            </Spin>
        </StarryBreadcrumb>
    );
};

export default AddApplicationMenu;
