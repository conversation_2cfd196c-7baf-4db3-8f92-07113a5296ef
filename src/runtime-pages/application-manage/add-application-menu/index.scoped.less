@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
::global {
    .add-application {
    height: inherit;

        .avatar-uploader-image{
            transform: translate(-50%, -50%) scale(1);
            &::abroad{
                border-radius: @border-radius-8;
            }
            .delete-icon {
                color: @starry-text-color-inverse;
            }
        }


    .info-text {
        color: @starry-text-color-secondary;
        font-size: 12px;
    }
    .international-input-form-item::abroad{
        padding-right: 40px;
    }
}
    .poppy-spin-nested-loading {
        height: 100% !important;
        .poppy-spin-container {
            height: 100% !important;
        }
    }
    .component-breadcrumb-layout-body {
        height: calc(100% - 48px);
    }
}

