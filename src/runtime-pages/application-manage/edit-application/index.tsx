import { useState, useRef, useEffect } from 'react';
import { useSubmitFn } from '@streamax/hooks';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useParams, useHistory } from '@base-app/runtime-lib/core';
import {
    StarryCard,
    StarryBreadcrumb,
    StarryAbroadFormItem,
    useSystemComponentStyle,
    StarryAbroadIcon
} from '@base-app/runtime-lib'; // 使用公共组件
import {Form, Input, Button, Row, Col, Upload, Space, message, Radio, Container, Image} from '@streamax/poppy';
import InternationalInput from '../../../components/InternationalInput';
import {
    i18n,
    utils,
    getAppGlobalData,
    reLoadLanguage,
    RouterPrompt,
    StarryAbroadLRLayout,
} from '@base-app/runtime-lib';
import { editApplication, fetchApplicationDetail } from '../../../service/application';
import { fileUploadStream, fetchFileDownloadUrl } from '../../../service/gss';
import './index.less';
import { checkFieldSpace } from '@/utils/commonFun';
import {IconDelete, IconDeleteFill} from '@streamax/poppy-icons';
import { RspFormLayout, useBreakpoint } from '@streamax/responsive-layout';

// @ts-ignore
const { encryptPassword: encrypToken } = utils.general;

function getBase64(img: any, callback: any) {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
}

const EditApplication = () => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const params: any = useParams();
    const history: any = useHistory();
    const { appId, parentId } = params;

    const [imageUrl, setImageUrl] = useState('');
    const [faviconUrl, setFaviconUrl] = useState('');
    const [imageUUID, setImageUUID] = useState<number | string>(0);
    const [faviconUUID, setFaviconUUID] = useState<number | string>(0);

    const [loading, setLoading] = useState(false);
    const [loading2, setLoading2] = useState(false);
    const [appInfo, setAppInfo] = useState<any>({});
    const [internationalInfo, setInternationalInfo] = useState<any>({});
    const [when, setWhen] = useState(true);
    const formRef = useRef<any>(null);
    const breakpoint = useBreakpoint();
    console.log(breakpoint,'breakpoint');

    useEffect(() => {
        appId && getAppInfo();
        return () => {
            setImageUrl('');
            setFaviconUrl('');
        };
    }, []);

    // 获取信息
    function getAppInfo() {
        fetchApplicationDetail({
            appId,
        }).then((res: any) => {
            setImageUrl(res.appLogo);
            setFaviconUrl(res.appFavicon);
            setImageUUID(res.appLogFileId);
            setFaviconUUID(res.appFaviconFileId);
            setAppInfo(res || {});
            formRef.current.setFieldsValue({
                ...res,
                applicationName: i18n.t(`@i18n:@app__${res.applicationId}`, res.applicationName),
            });
        });
    }

    function beforeUploadLogo(file: any) {
        if (file.type !== 'image/png') {
            message.error(i18n.t('message', '仅支持png格式，且小于200k'));
            return Upload.LIST_IGNORE;
        }
        if (file.size > 200 * 1024) {
            message.error(i18n.t('message', '仅支持png格式，且小于200k'));
            return Upload.LIST_IGNORE;
        }
        return true;
    }

    function beforeUploadFavicon(file: any) {
        if (!(file.type === 'image/x-icon' || file.type === 'image/vnd.microsoft.icon')) {
            // Mac上识别.ico的MIME类型为 image/vnd.microsoft.icon
            message.error(i18n.t('message', '仅支持ico格式，且小于200k'));
            return Upload.LIST_IGNORE;
        }
        if (file.size > 200 * 1024) {
            message.error(i18n.t('message', '仅支持ico格式，且小于200k'));
            return Upload.LIST_IGNORE;
        }
        return true;
    }

    function handleChange(info: any, type: string) {
        if (info.file.status === 'uploading') {
            type === 'logo' ? setLoading(true) : setLoading2(true);
            return;
        }
        if (info.file.status === 'done') {
            getBase64(info.file.originFileObj, () => {
                type === 'logo' ? setLoading(false) : setLoading2(false);
            });
        }
    }

    const [onFinish, onRequest] = useSubmitFn(async (values: any) => {
        const { translationList, objectName, langId } = internationalInfo;
        if (appId !== undefined && appId !== null) {
            await editApplication({
                ...values,
                applicationId: appId,
                appLogo: imageUUID,
                parentId,
                entryId: langId,
                appFavicon: faviconUUID,
                applicationName: objectName || appInfo.applicationName,
                translationList:
                    translationList ||
                    (i18n.exists(`@i18n:@app__${appId}`)
                        ? [
                              {
                                  langType: getAppGlobalData('APP_LANG'),
                                  translationValue: values.applicationName,
                              },
                          ]
                        : []),
            });
            setWhen(false);
            message.success(i18n.t('message', '操作成功'));
            await reLoadLanguage(true, true);
            history.goBack();
        }
    });

    // function validatorLogo(_: any, value: any) {
    //     if (imageUrl) {
    //         return Promise.resolve();
    //     }
    //     if (!value || !value.file) {
    //         return Promise.reject(new Error(i18n.t('message', '请上传图片')));
    //     }
    //     const { file } = value;
    //     if (file.size > 200 * 1024 || file.type !== 'image/png') {
    //         return Promise.reject(new Error(i18n.t('message', '仅支持png格式，且小于200k')));
    //     }
    //     return Promise.resolve();
    // }

    function customRequest(name: any) {
        return function ({ file, onSuccess, onError }: any) {
            const formData = new FormData();
            formData.append('file', file);
            fileUploadStream(formData, {
                // @ts-ignore
                _abs: encrypToken(window.localStorage.getItem('AUTH_TOKEN')),
            })
                .then(async (res: any) => {
                    getImgUrl(res, onSuccess, onError, name);
                })
                .catch(() => {
                    onError();
                });
        };
    }

    function getImgUrl(fileId: string | number, onSuccess: any, onError: any, name: any) {
        fetchFileDownloadUrl(
            {
                fileIdList: fileId.toString(),
                validTime: 365,
            },
            { _tenantId: 0, _appId: 0 },
        )
            .then((res: any) => {
                if (res && res[0]) {
                    const fileUrl = res[0].fileUrl;
                    if (name === 'appLogo') {
                        setImageUUID(fileId);
                        setImageUrl(fileUrl);
                    } else {
                        setFaviconUUID(fileId);
                        setFaviconUrl(fileUrl);
                    }
                    onSuccess();
                }
            })
            .catch(() => {
                onError();
            });
    }
    const checkSpace = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '应用名称不能为空'));
    };

    const appLogoFormItem = <Form.Item>
        <StarryAbroadFormItem
            validateFirst
            label={i18n.t('name', '应用Logo')}
            name="appLogo"
            className={'upload-form-item'}
            // rules={[
            //     {
            //         validator: validatorLogo
            //     }
            // ]}
        >
            <Upload
                name="appLogo"
                withCredentials
                listType="picture-card"
                className="avatar-uploader"
                showUploadList={false}
                accept="image/png"
                beforeUpload={beforeUploadLogo}
                customRequest={customRequest('appLogo')}
                onChange={(info: any) => handleChange(info, 'logo')}
            >
                {imageUrl ? (
                        <Image
                            alt="logo"
                            src={imageUrl}
                            className={'avatar-uploader-image'}
                            preview={
                                {mask: <StarryAbroadIcon>
                                        <IconDeleteFill
                                            className="delete-icon"
                                            onClick={
                                                (e: React.MouseEvent) => {
                                                    e.stopPropagation();
                                                    setImageUrl('');
                                                    setImageUUID("");
                                                }
                                            }/>
                                    </StarryAbroadIcon>,
                                    visible: false
                                }
                            }
                        />
                ) : (
                    <div>
                        {loading ? (
                            <StarryAbroadIcon>
                                <LoadingOutlined />
                            </StarryAbroadIcon>
                        ) :(
                            <StarryAbroadIcon>
                                <PlusOutlined />
                            </StarryAbroadIcon>
                        )}
                        <div style={{ marginTop: 8 }}>
                            {i18n.t('message', '上传图片')}
                        </div>
                    </div>
                )}
            </Upload>
        </StarryAbroadFormItem>
        <span className="info-text">
            {i18n.t('message', '仅支持png格式，小于200k')}
        </span>
    </Form.Item>;


    const appFaviconFormItem = <Form.Item>
        <StarryAbroadFormItem
            validateFirst
            label={`${i18n.t('name', '网站图标')}`}
            name="appFavicon"
            className={'upload-form-item'}
            // rules={[
            //     {
            //         required: true
            //     }
            // ]}
        >
            <Upload
                name="appFavicon"
                withCredentials
                listType="picture-card"
                className="avatar-uploader"
                showUploadList={false}
                accept=".ico"
                beforeUpload={beforeUploadFavicon}
                customRequest={customRequest('appFavicon')}
                onChange={(info: any) => handleChange(info, 'favicon')}
            >
                {faviconUrl ? (
                    <Image
                        alt="favicon"
                        src={faviconUrl}
                        className={'avatar-uploader-image'}
                        preview={
                            {mask: <StarryAbroadIcon>
                                    <IconDeleteFill
                                        className="delete-icon"
                                        onClick={
                                            (e: React.MouseEvent) => {
                                                e.stopPropagation();
                                                setFaviconUrl('');
                                                setFaviconUUID("");
                                            }
                                        }/>
                                </StarryAbroadIcon>,
                                visible: false
                            }
                        }
                    />
                ) : (
                    <div>
                        {loading2 ?  <StarryAbroadIcon>
                            <LoadingOutlined />
                        </StarryAbroadIcon> : <StarryAbroadIcon><PlusOutlined /></StarryAbroadIcon>}
                        <div style={{ marginTop: 8 }}>
                            {i18n.t('message', '上传图片')}
                        </div>
                    </div>
                )}
            </Upload>
        </StarryAbroadFormItem>
        <span className="info-text">
            {i18n.t('message', '仅支持ico格式，小于200k')}
        </span>
    </Form.Item>;

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard title={i18n.t('name', '基本信息')} className="add-application">
                <Form
                    ref={formRef}
                    labelWidth={100}
                    // style={{ maxWidth: 550 }}
                    layout="vertical"
                    onFinish={onFinish}
                >

                    <Container>
                        <div >
                        <RspFormLayout layoutType="fixed">
                        <RspFormLayout.Col>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '应用名称')}
                                name="applicationName"
                                validateFirst
                                style={{paddingRight: '28px'}}
                                rules={[
                                    {
                                        required: true,
                                        validator: checkSpace,
                                    },
                                    {
                                        min: 1,
                                        type: 'string',
                                    },
                                    {
                                        max: 50,
                                        type: 'string',
                                    },
                                    {
                                        validator: utils.validator.illegalCharacter,
                                    },
                                ]}
                                className="form-item international-input-form-item"
                            >
                                <InternationalInput
                                    allowClear
                                    maxLength={50}
                                    placeholder={i18n.t('message', '请输入')}
                                    modalType={appId ? 'edit' : 'add'}
                                    internationalType="app"
                                    entryKey={(appInfo || {}).applicationName}
                                    entryIdOrCode={appId}
                                    onSave={(values) => setInternationalInfo(values)}
                                />
                            </StarryAbroadFormItem>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '隐藏应用名称')}
                                name="hideAppName"
                                rules={[
                                    {
                                        required: true,
                                    },
                                ]}
                            >
                                <Radio.Group disabled={!imageUrl}>
                                    <Radio value={1}>{i18n.t('state', '是')}</Radio>
                                    <Radio value={0}>{i18n.t('state', '否')}</Radio>
                                </Radio.Group>
                            </StarryAbroadFormItem>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '应用编码')}
                                name="applicationId"
                                validateFirst
                                rules={[
                                    {
                                        required: true,
                                    },
                                ]}
                                className="form-item"
                            >
                                <Input
                                    allowClear
                                    maxLength={50}
                                    disabled={!!appId}
                                    placeholder={i18n.t('message', '请输入')}
                                />
                            </StarryAbroadFormItem>
                            {
                                (isAbroadStyle||(breakpoint==='lg'||breakpoint==='md'||breakpoint==='sm'||breakpoint==='xs')) &&
                                <>
                                    {appLogoFormItem}
                                    {appFaviconFormItem}
                                </>
                            }
                            <StarryAbroadFormItem
                                label={i18n.t('name', '描述')}
                                name="description"
                                rules={[
                                    {
                                        max: 500,
                                    },
                                ]}
                                className="form-item"
                            >
                                <Input.TextArea
                                    allowClear
                                    maxLength={500}
                                    showCount
                                    autoSize={{
                                        minRows: 4,
                                        maxRows: 6,
                                    }}
                                    placeholder={i18n.t('message', '请输入')}
                                />
                            </StarryAbroadFormItem>
                            </RspFormLayout.Col>
                            <RspFormLayout.Col>
                        {
                            (!(isAbroadStyle)&&(breakpoint==='xl'||breakpoint==='xxl')) &&
                            <>
                                    <div style={{display: 'flex', gap: 80}}>
                                    {appLogoFormItem}
                                    {appFaviconFormItem}
                                    </div>
                            </>
                        }
                        </RspFormLayout.Col>
                        </RspFormLayout>
                        </div>
                </Container>
                    <StarryAbroadLRLayout>
                        <Button
                            onClick={() => {
                                history.goBack();
                            }}
                        >
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            type="primary"
                            disabled={appId && !internationalInfo.langId}
                            htmlType="submit"
                            loading={onRequest}
                        >
                            {i18n.t('action', '保存')}
                        </Button>
                    </StarryAbroadLRLayout>
                </Form>

            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default EditApplication;
