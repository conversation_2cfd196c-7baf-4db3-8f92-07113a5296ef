import {StarryAbroadFormItem, StarryBread<PERSON>rumb, StarryCard, StarryAbroadLRLayout, useSystemComponentStyle} from '@base-app/runtime-lib';
import {Form, Input, Button, Space, Select, Radio, message, Spin, Container} from '@streamax/poppy';
import { useState, useRef, useEffect } from 'react';
import { i18n, utils, getAppGlobalData, reLoadLanguage } from '@base-app/runtime-lib';
import { useHistory, useParams, useLocation } from '@base-app/runtime-lib/core';
import InternationalInput from '../../../components/InternationalInput';
import { validatorResourceCode } from '../../../utils/commonFun';
import {
    fetchResourceDetail,
    editResource,
    addResource,
    fetchResourcePageList,
} from '../../../service/resource';
import './index.scoped.less';
import { useSubmitFn } from '@streamax/hooks';
import { RspFormLayout } from '@streamax/responsive-layout';

const { Option } = Select;
interface PageType {
    resourceId: string | number;
    resourceName: string;
    [name: string]: any;
}

const AddApplicationPageOperation = () => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const history = useHistory();
    const params: any = useParams();
    const { appId, operationId } = params;
    const [urlList, setUrlList] = useState<PageType[]>([]);
    const [dataSource, setDataSource] = useState<any>({});
    const [internationalInfo, setInternationalInfo] = useState<any>({});
    const [initLoading, setInitLoading] = useState(operationId ? true : false);
    const formRef = useRef<any>(null);
    const resourceInitTypeRef = useRef(0);

    const {
        // @ts-ignore
        query: { resourceCode, parentCode, from },
    } = useLocation();

    useEffect(() => {
        getUrlList();
    }, []);

    useEffect(() => {
        operationId && getOperationInfo();
    }, [operationId]);

    // 获取所有页面资源链接
    function getUrlList() {
        fetchResourcePageList({
            resourceType: 2,
            page: 1,
            pageSize: 10000,
            appId,
        }).then((res: any) => {
            setUrlList(res.list || []);
        });
    }

    function getOperationInfo() {
        fetchResourceDetail({
            resourceId: operationId,
            appId,
        }).then((res: any) => {
            resourceInitTypeRef.current = res.resourceInitType;
            setDataSource(res || {});
            formRef.current.setFieldsValue({
                ...res,
                operationWay: res.operationWay || 1,
                resourceName: i18n.t(`@i18n:@operation__${res.resourceCode}`, res.resourceName),
            });
        });
    }

    async function onFinish(values: any) {
        const { translationList, objectName, langId } = internationalInfo;
        if (operationId) {
            await editResource({
                ...values,
                resourceId: operationId.toString(),
                entryId: langId,
                appId,
                parentCode,
                resourceName:
                    objectName ||
                    (i18n.exists(`@i18n:@operation__${resourceCode}`)
                        ? dataSource.resourceName
                        : values.resourceName),
                translationList:
                    translationList ||
                    (i18n.exists(`@i18n:@operation__${resourceCode}`)
                        ? [
                              {
                                  langType: getAppGlobalData('APP_LANG'),
                                  translationValue: values.resourceName,
                              },
                          ]
                        : []),
            });
            message.success(i18n.t('message', '操作成功'));
            // resourceInitType判断一下资源的初始化类型，0-初始化，1-新增，2-修改，3-删除。 0的就刷新页面
            if (resourceInitTypeRef.current === 0) {
                // @ts-ignore
                // history.replace(-1);
                window.location.replace(decodeURIComponent(from));
                return;
            }
            await reLoadLanguage(true, true);
            history.goBack();
        } else {
            await addResource({
                ...values,
                resourceType: 3,
                appId,
                translationList: translationList || [],
            });
            await reLoadLanguage(true, true);
            history.goBack();
        }
    }

    const [onFinishLock, submitLoading] = useSubmitFn(onFinish);

    return (
        <StarryBreadcrumb>
            <Spin spinning={initLoading}>
                <StarryCard title={i18n.t('name', '基本信息')} className="add-application">
                    <Form
                        ref={formRef}
                        labelWidth={100}
                        // style={{ maxWidth: isAbroadStyle ? '100%' : 760 }}
                        layout="vertical"
                        onFinish={onFinishLock}
                    >
                        <Container style={{paddingBottom: 0}}>
                            {/* <Space
                                style={{width: '100%'}}
                                wrap={!isAbroadStyle && true}
                                direction={isAbroadStyle ? 'vertical' : 'horizontal'}
                                align={isAbroadStyle ? undefined : 'baseline'}
                                size={isAbroadStyle ? 0 : [80, 0]}
                            > */}
                            <RspFormLayout layoutType="auto">
                            <StarryAbroadFormItem
                                style={{paddingRight: 28}}
                                className={'international-input-form-item'}
                                label={i18n.t('name', '操作名称')}
                                name="resourceName"
                                validateFirst
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        min: 2,
                                        type: 'string',
                                    },
                                    {
                                        max: 50,
                                        type: 'string',
                                    },
                                    {
                                        validator: utils.validator.illegalCharacter,
                                    },
                                ]}
                            >
                                <InternationalInput
                                    allowClear
                                    maxLength={50}
                                    placeholder={i18n.t('message', '请输入')}
                                    modalType={operationId ? 'edit' : 'add'}
                                    internationalType="operation"
                                    // menu、page、operation的langKey是后面拼接code，则应该传resourceCode
                                    entryIdOrCode={resourceCode}
                                    entryKey={(dataSource || {}).resourceName}
                                    onSave={(values) => setInternationalInfo(values)}
                                    onInit={() => setInitLoading(false)}
                                />
                            </StarryAbroadFormItem>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '操作编码')}
                                name="resourceCode"
                                validateFirst
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        min: 2,
                                        type: 'string',
                                    },
                                    {
                                        validator: validatorResourceCode,
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    maxLength={50}
                                    disabled={operationId}
                                    placeholder={i18n.t('message', '请输入')}
                                />
                            </StarryAbroadFormItem>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '权限管理')}
                                name="managementSwitch"
                                rules={[
                                    {
                                        required: true,
                                    },
                                ]}
                                initialValue={0}
                            >
                                <Radio.Group>
                                    <Radio value={0}>{i18n.t('state', '是')}</Radio>
                                    <Radio value={1}>{i18n.t('state', '否')}</Radio>
                                </Radio.Group>
                            </StarryAbroadFormItem>
                            <StarryAbroadFormItem
                                label={i18n.t('name', '跳转页面')} name="pageCode">
                                <Select
                                    allowClear
                                    showSearch
                                    placeholder={i18n.t('message', '请选择跳转页面')}
                                    filterOption={(input, option: any) => {
                                        return (
                                            option.children
                                                .toLowerCase()
                                                .indexOf(input.toLowerCase()) >= 0
                                        );
                                    }}
                                >
                                    {urlList.map((item) => (
                                        <Option value={item.resourceCode}>
                                            {i18n.t(
                                                `@i18n:@page__${item.resourceCode}`,
                                                item.resourceName,
                                            )}
                                        </Option>
                                    ))}
                                </Select>
                            </StarryAbroadFormItem>
                            </RspFormLayout>
                        {/* </Space> */}
                        </Container>

                        <StarryAbroadLRLayout>
                            <Button
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                {i18n.t('action', '保存')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </Form>
                </StarryCard>
            </Spin>
        </StarryBreadcrumb>
    );
};

export default AddApplicationPageOperation;
