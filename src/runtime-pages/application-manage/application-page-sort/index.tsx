import { useEffect, useState } from 'react';
import { useSubmitFn } from '@streamax/hooks';
import {Table, Button, Space, message, Container} from '@streamax/poppy';
import {i18n, RouterPrompt, StarryAbroadLRLayout} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import arrayMove from 'array-move';
import { IconDrag } from '@streamax/poppy-icons';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
//@ts-ignore
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc';
import { fetchResourcePageList, sortResource } from '../../../service/resource';
import './index.less';

const SortableItem = sortableElement((props: any) => <tr {...props} />);
const SortableContainer = sortableContainer((props: any) => <tbody {...props} />);
const DragHandle = sortableHandle(() => <IconDrag className={'icon-drag'} style={{ cursor: 'grab' }} />);

const ApplicationPageSort = (props: any) => {
    const history = useHistory();
    const { match } = props;
    const {
        // @ts-ignore
        query: { parentId },
    } = useLocation<any>();

    const [dataSource, setDataSource] = useState<any[]>([]);
    const [when, setWhen] = useState(true);

    useEffect(() => {
        getDataList();
    }, []);

    function getDataList() {
        const { appId } = match.params;
        fetchResourcePageList({
            page: 1,
            pageSize: 10000,
            resourceType: 2,
            appId,
            level: Number(parentId) !== 0 ? undefined : 1, // 没有父级，排序跟页面
            parentId: Number(parentId) !== 0 ? parentId : undefined,
        }).then((res: any) => {
            setDataSource(
                (res.list || [])
                    .sort((a: any, b: any) => {
                        return a.sortValue - b.sortValue;
                    })
                    .map((item: any, index: number) => {
                        item.index = index;
                        return item;
                    }),
            );
        });
    }

    const columns = [
        {
            title: i18n.t('name', '页面编码'),
            dataIndex: 'resourceCode',
            className: 'drag-visible',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '页面名称'),
            dataIndex: 'resourceName',
            ellipsis: true,
            render: (text: any, record: any) => i18n.t(`@i18n:@page__${record.resourceCode}`, text),
        },
        {
            title: i18n.t('name', '操作'),
            ellipsis: true,
            iconResize: true,
            dataIndex: 'sortValue',
            className: 'drag-visible',
            render: () => <DragHandle />,
        },
    ];

    const onSortEnd = ({ oldIndex, newIndex }: any) => {
        if (oldIndex !== newIndex) {
            const newData = arrayMove([].concat(dataSource as any), oldIndex, newIndex).filter(
                (el) => !!el,
            );
            setDataSource(newData);
        }
    };

    const DraggableContainer = (tableProps: any) => (
        <SortableContainer
            useDragHandle
            useWindowAsScrollContainer
            helperClass="row-dragging"
            onSortEnd={onSortEnd}
            {...tableProps}
        />
    );

    const DraggableBodyRow = ({ className, style, ...restProps }: any) => {
        const index = dataSource.findIndex((x) => x.index === restProps['data-row-key']);
        return <SortableItem index={index} {...restProps} />;
    };

    // 保存排序
    const [saveSort, submitLoading] = useSubmitFn(async () => {
        const resourceIds = dataSource.map((item) => {
            return item.resourceId;
        });
        if (!resourceIds.length) {
            setWhen(false);
            history.goBack();
            return;
        }
        await sortResource({
            resourceIds: resourceIds.join(','),
        });
        message.success(i18n.t('message', '操作成功'));
        setWhen(false);
        history.goBack();
    });

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <div className="app-page-sort">
                    <Container>
                    <div className="poppy-table-around-bordered">
                        <Table
                            bordered="around"
                            pagination={false}
                            dataSource={dataSource}
                            columns={columns}
                            rowKey="index"
                            components={{
                                body: {
                                    wrapper: DraggableContainer,
                                    row: DraggableBodyRow,
                                },
                            }}
                        />
                    </div>
                </Container>
                    <StarryAbroadLRLayout className='edit-submit-btn-container'>
                            <Button
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button onClick={saveSort} type="primary" loading={submitLoading}>
                                {i18n.t('action', '保存')}
                            </Button>
                    </StarryAbroadLRLayout>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default ApplicationPageSort;
