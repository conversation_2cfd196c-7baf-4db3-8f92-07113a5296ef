import {<PERSON>y<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>y<PERSON>ard, StarryAbroadLRLayout, useSystemComponentStyle, StarryAbroadFormItem} from '@base-app/runtime-lib';
import { Form, Input, Button, Space, Select, Radio, message, Spin, Container } from '@streamax/poppy';
import InternationalInput from '../../../components/InternationalInput';
import { useState, useRef, useEffect } from 'react';
import {
    i18n,
    utils,
    getAppGlobalData,
    reLoadLanguage,
    RouterPrompt,
} from '@base-app/runtime-lib';
import { useHistory, useParams, useLocation } from '@base-app/runtime-lib/core';
import {
    fetchResourcePageList,
    fetchResourceDetail,
    editResource,
    addResource,
} from '../../../service/resource';
import { useSubmitFn } from '@streamax/hooks';
import { checkFieldSpace, validatorResourceCode } from '../../../utils/commonFun';
import { RspFormLayout } from '@streamax/responsive-layout';
import './index.scoped.less';
const { Option } = Select;
interface PageType {
    resourceId: string | number;
    resourceName: string;
    [name: string]: any;
}

const AddApplicationPage = () => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const history = useHistory();
    const params: any = useParams();
    const { appPageId, appId } = params;
    const [dataSource, setDataSource] = useState<any>({});
    const [selectLevel, setSelectLevel] = useState<any>('');
    const [allLevelPage, setAllLevelPage] = useState<PageType[]>([]);
    const [internationalInfo, setInternationalInfo] = useState<any>({});
    const [initLoading, setInitLoading] = useState(appPageId ? true : false);
    const formRef = useRef<any>(null);
    const resourceInitTypeRef = useRef(0);
    const [when, setWhen] = useState(true);

    const {
        // @ts-ignore
        query: { from, fromPage, resourceCode },
    } = useLocation();

    useEffect(() => {
        if (appPageId) {
            getAppPageInfo(appPageId);
        }
        getAllLevelPage();
    }, []);

    // 获取上级
    function getAppPageInfo(pageId: string) {
        fetchResourceDetail({
            resourceType: 1,
            resourceId: pageId,
            appId,
        }).then((res: any) => {
            resourceInitTypeRef.current = res.resourceInitType;
            setSelectLevel(res.level || 1);
            setDataSource(res || {});
            formRef.current &&
                formRef.current.setFieldsValue({
                    ...res,
                    resourceName: i18n.t(`@i18n:@page__${res.resourceCode}`, res.resourceName),
                });
        });
    }

    // 获取一级页面
    function getAllLevelPage() {
        fetchResourcePageList({
            resourceType: 2,
            page: 1,
            pageSize: 10000,
            appId,
        }).then((res: any) => {
            setAllLevelPage(res.list || []);
        });
    }

    async function onFinish(values: any) {
        const { level } = values;
        // 如果是根页面，则删掉上级页面
        level === 1 && delete values.parentCode;
        const { translationList, objectName, langId } = internationalInfo;
        if (appPageId) {
            await editResource({
                ...values,
                resourceType: 2,
                resourceId: appPageId,
                entryId: langId,
                appId,
                resourceName:
                    objectName ||
                    (i18n.exists(`@i18n:@menu__${dataSource.resourceCode}`)
                        ? dataSource.resourceName
                        : values.resourceName),
                // resourceName:,
                translationList:
                    translationList ||
                    (i18n.exists(`@i18n:@menu__${dataSource.resourceCode}`)
                        ? [
                              {
                                  langType: getAppGlobalData('APP_LANG'),
                                  translationValue: values.resourceName,
                              },
                          ]
                        : []),
            });
            message.success(i18n.t('message', '操作成功'));
            setWhen(false);
            if (resourceInitTypeRef.current === 0) {
                // @ts-ignore
                // window.location.replace(-1);
                if (fromPage) {
                    setWhen(false);
                    // 从页面详情跳转到的编辑，直接回退到列表页面
                    window.location.replace(fromPage.replaceAll('||', '?').replaceAll('**', '&'));
                } else {
                    setWhen(false);
                    window.location.replace(decodeURIComponent(from));
                }
                return;
            }
            await reLoadLanguage(true, true);
            setWhen(false);
            history.goBack();
        } else {
            const res = await addResource({
                ...values,
                resourceType: 2,
                appId,
                translationList: translationList || [],
            });
            if (res.data.code === 1000) {
                await reLoadLanguage(true, true);
                setWhen(false);
                history.goBack();
            } else {
                message.warn(res.data.message);
            }
        }
    }
    const [onFinishLock, submitLoading] = useSubmitFn(onFinish);
    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '页面名称不能为空'));
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <Spin spinning={initLoading}>
                <StarryCard title={i18n.t('name', '基本信息')} className="add-application">
                    <Form
                        ref={formRef}
                        labelWidth={100}
                        // style={{ maxWidth: isAbroadStyle ? '100%' : 760 }}
                        layout="vertical"
                        onFinish={onFinishLock}
                    >
                        <Container style={{paddingBottom: 0}}>
                        {/* <Space
                            style={{width: '100%'}}
                            wrap={!isAbroadStyle && true}
                            direction={isAbroadStyle ? 'vertical' : 'horizontal'}
                            align={isAbroadStyle ? undefined : 'baseline'}
                            size={isAbroadStyle ? 0 : [80, 0]}
                        > */}
                            <RspFormLayout layoutType="auto">
                                <StarryAbroadFormItem
                                    style={{width: 340}}
                                    className={'international-input-form-item'}
                                    label={i18n.t('name', '页面名称')}
                                    name="resourceName"
                                    validateFirst
                                    style={{paddingRight: 40}}
                                    rules={[
                                        {
                                            required: true,
                                            validator: checkSpaceName,
                                        },
                                        {
                                            min: 1,
                                            type: 'string',
                                        },
                                        {
                                            max: 50,
                                            type: 'string',
                                        },
                                        {
                                            validator: utils.validator.illegalCharacter,
                                        },
                                    ]}
                                >
                                    <InternationalInput
                                        allowClear
                                        maxLength={50}
                                        placeholder={i18n.t('message', '请输入')}
                                        modalType={appPageId ? 'edit' : 'add'}
                                        internationalType="page"
                                        // menu、page、operation的langKey是后面拼接code，则应该传resourceCode
                                        entryIdOrCode={resourceCode}
                                        entryKey={(dataSource || {}).resourceName}
                                        onSave={(values) => setInternationalInfo(values)}
                                        onInit={() => setInitLoading(false)}
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '页面编码')}
                                    name="resourceCode"
                                    validateFirst
                                    rules={[
                                        {
                                            required: true,
                                        },
                                        // 此处特殊处理（同后台管理）限制1-100
                                        {
                                            min: 1,
                                            type: 'string',
                                        },
                                        {
                                            max: 100,
                                            type: 'string',
                                        },
                                        {
                                            validator: validatorResourceCode,
                                        },
                                    ]}
                                >
                                    <Input
                                        allowClear
                                        disabled={appPageId}
                                        placeholder={i18n.t('message', '请输入')}
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '权限管理')}
                                    name="managementSwitch"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    initialValue={0}
                                >
                                    <Radio.Group>
                                        <Radio value={0}>{i18n.t('state', '是')}</Radio>
                                        <Radio value={1}>{i18n.t('state', '否')}</Radio>
                                    </Radio.Group>
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '页面级别')}
                                    name="level"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                    ]}
                                    initialValue={1}
                                >
                                    <Select
                                        disabled={appPageId}
                                        placeholder={i18n.t('message', '请选择')}
                                        onChange={(v) => setSelectLevel(v)}
                                    >
                                        <Option value={1}>{i18n.t('state', '根页面')}</Option>
                                        <Option value={2}>{i18n.t('state', '子页面')}</Option>
                                    </Select>
                                </StarryAbroadFormItem>
                                {selectLevel === 2 && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '上级页面')}
                                        name="parentCode"
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <Select
                                            disabled={appPageId}
                                            placeholder={i18n.t('message', '请选择')}
                                        >
                                            {allLevelPage.map((item) => (
                                                <Option value={item.resourceCode}>
                                                    {i18n.t(
                                                        `@i18n:@page__${item.resourceCode}`,
                                                        item.resourceName,
                                                    )}
                                                </Option>
                                            ))}
                                        </Select>
                                    </StarryAbroadFormItem>
                                )}
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '访问路径')}
                                    name="resourceUrl"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                        {
                                            min: 1,
                                            type: 'string',
                                        },
                                        {
                                            max: 500,
                                            type: 'string',
                                        },
                                    ]}
                                >
                                    <Input
                                        allowClear
                                        maxLength={500}
                                        disabled={appPageId}
                                        placeholder={i18n.t('message', '请输入')}
                                    />
                                </StarryAbroadFormItem>
                            </RspFormLayout>
                        {/* </Space> */}
                    </Container>
                        <StarryAbroadLRLayout>
                            <Button
                                onClick={() => {
                                    setWhen(false);
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button
                                type="primary"
                                disabled={appPageId && !internationalInfo.langId}
                                htmlType="submit"
                                loading={submitLoading}
                            >
                                {i18n.t('action', '保存')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </Form>
                </StarryCard>
            </Spin>
        </StarryBreadcrumb>
    );
};

export default AddApplicationPage;
