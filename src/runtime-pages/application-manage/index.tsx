import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
import { useRef, useEffect } from 'react';
import {
    Input,
    Select,
    Form,
    Space,
    Switch,
    Badge,
    message,
    Tooltip,
} from '@streamax/poppy';
import { Link } from '@base-app/runtime-lib/core';
import { IconEdit, IconRequestFill } from '@streamax/poppy-icons';
import {
    StarryTable,
    StarryCard,
    StarryBreadcrumb,
    Action,
    StarryModal,
} from '@base-app/runtime-lib'; // 使用公共组件
import { i18n, Auth, utils, useUrlSearchStore } from '@base-app/runtime-lib';
import {
    fetchApplicationPageList,
    unlockApplication,
    lockApplication,
} from '../../service/application';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { PageBase } from '@/types/pageReuse/pageReuseBase';
import './index.less';
/****定制项***/
export type ApplicationManageShareProps = PageBase;
const BASE_PLATFORM_APP_ID = 0;

const ApplicationManage = (props: ApplicationManageShareProps) => {
    /**定制项 */
    const { children } = props;
    /**end */
    const tableRef = useRef<any>(null);
    const searchStore = useUrlSearchStore();
    const [form] = Form.useForm();

    const items: QueryFormProps['items'] = [
        {
            label: i18n.t('name', '状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择应用状态'),
                options: [
                    {
                        label: i18n.t('state', '启用'),
                        value: 1,
                    },
                    {
                        label: i18n.t('state', '停用'),
                        value: 2,
                    },
                ],
            },
        },
        {
            label: i18n.t('name', '应用'),
            name: 'search',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入应用编码或应用名称'),
                maxLength: 50,
            },
        },
    ];

    const toolbar = {
        // leftRender: () => (
        //     <Button
        //         type="primary"
        //         onClick={() => message.info(i18n.t('message ', '暂不支持'))}
        //     >
        //         {i18n.t(
        //             'action',
        //             '开通申请',
        //         )}
        //     </Button>
        // ),
        columnSetting: {
            disabledKeys: ['operate', 'applicationName'],
            storageKey: '@base:@page:application.manage.table',
        },
    };
    const { page, pageSize, search, state } = searchStore.get();
    useEffect(() => {
        if (page || pageSize || search || state) {
            form.setFieldsValue({
                ...searchStore.get(),
                state: state ? Number(state) : undefined,
            });
            // StarryTable 暂时不支持固定page pageSize
        }
    }, []);

    const getList = async (data: any) => {
        const params = JSON.parse(JSON.stringify(data));
        const search = params.search && params.search.trim();
        params.search = search ? encodeURI(`${search} in:applicationName[app],id[app]`) : '';
        const states = params.state || '1,2,3';
        delete params.state;
        params.states = states;
        let list: any = [];
        let total = 0;
        searchStore.set({
            ...params,
            state: params.states === '1,2,3' ? '' : params.states,
            search,
        });

        return fetchApplicationPageList(params)
            .then((res: any) => {
                list = res.list;
                total = res.total;
                return { list, total };
            })
            .catch(() => {
                return { list, total };
            });
    };

    const columns: ColumnsType<any> = [
        {
            title: i18n.t('name', '应用编码'),
            dataIndex: 'applicationId',
            ellipsis: { showTitle: false },
            render: (text) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '应用名称'),
            dataIndex: 'applicationName',
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => {
                const tooltip = i18n.t(`@i18n:@app__${record.applicationId}`, record.applicationName);
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Action
                            code="@base:@page:application.manage@action:detail"
                            url="/applicationManage/applicationDetail/:appId"
                            fellback={tooltip}
                            params={{
                                appId: record.applicationId,
                            }}
                        >
                            {tooltip}
                        </Action>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '应用状态'),
            dataIndex: 'state',
            ellipsis: true,
            render: (text: any, record: any) => {
                const { applicationId } = record;
                let status: 'success' | 'error' | undefined = undefined;
                let showText = '-';
                switch (text) {
                    case 1:
                        status = 'success';
                        showText = i18n.t('state', '启用');
                        break;
                    case 2:
                        status = 'error';
                        showText = i18n.t('state', '停用');
                        break;
                    default:
                        break;
                }
                return (
                    <Space>
                        {status ? <Badge status={status} text={showText} /> : '-'}
                        <Auth code="@base:@page:application.manage@action:enable.disenable">
                            {(text === 1 || text === 2) &&
                                applicationId !== BASE_PLATFORM_APP_ID && (
                                    <Switch
                                        size="small"
                                        checked={text === 1}
                                        onChange={() => {
                                            text === 1 ? lock(record) : unlock(record);
                                        }}
                                    />
                                )}
                        </Auth>
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '开通时间'),
            dataIndex: 'beginTime',
            ellipsis: { showTitle: false },
            render: (text: any) => (
                <Tooltip title={utils.formator.zeroTimeStampToFormatTime(text)}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            width: 120,
            render: (text: any, record: any) => (
                <Space className="operate-btn">
                    <Action
                        code="@base:@page:application.manage@action:edit"
                        url="/applicationManage/edit-application/:appId"
                        fellback={
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <IconEdit />
                            </Tooltip>
                        }
                        params={{
                            appId: record.applicationId,
                        }}
                    >
                        <Tooltip title={i18n.t('action', '编辑')}>
                            <IconEdit />
                        </Tooltip>
                    </Action>

                    {/* {record.state !== 1 && ( 去掉删除功能
                        <span
                            title={i18n.t(
                                'action',
                                '删除'
                            )}
                            onClick={() => deleteApp(record)}
                        >
                            <IconDelete />
                        </span>
                    )} */}
                </Space>
            ),
        },
    ];

    // 启用
    function unlock(record: any) {
        const { applicationId, applicationName } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '启用确认'),
            content: i18n.t('message', '确认要启用“{applicationName}”吗？', {
                applicationName: i18n.t(`@i18n:@app__${applicationId}`, applicationName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: async () => {
                try {
                    await unlockApplication({
                        appIds: applicationId.toString(),
                    });
                    message.success(i18n.t('message', '操作成功'));
                    tableRef.current.reload();
                    // eslint-disable-next-line no-empty
                } catch (error) {}
            },
        });
    }

    // 停用
    function lock(record: any) {
        const { applicationId, applicationName } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '停用确认'),
            content: i18n.t('message', '确认要停用“{applicationName}”吗？', {
                applicationName: i18n.t(`@i18n:@app__${applicationId}`, applicationName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: async () => {
                try {
                    await lockApplication({
                        appIds: applicationId.toString(),
                    });
                    message.success(i18n.t('message', '操作成功'));
                    tableRef.current.reload();
                    // eslint-disable-next-line no-empty
                } catch (error) {}
            },
        });
    }

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="application-manage">
                    <StarryTable
                        aroundBordered
                        fetchDataAfterMount
                        fetchDataFunc={getList}
                        ref={tableRef}
                        queryProps={{
                            items,
                            form,
                        }}
                        pagination={{
                            defaultCurrent: Number(page) || 1,
                            defaultPageSize: Number(pageSize) || 20,
                        }}
                        rowKey="applicationId"
                        toolbar={toolbar}
                        columns={columns}
                    />
                </div>
                {children}
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC<ApplicationManageShareProps, any>(ApplicationManage);
