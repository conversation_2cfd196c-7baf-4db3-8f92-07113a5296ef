import { StarryInfoBlock, Action } from '@base-app/runtime-lib';
import React from 'react';
import { Row, Col } from '@streamax/poppy';
import { i18n, Auth, utils } from '@base-app/runtime-lib';
import './appInfo.less';
import { useHistory } from '@base-app/runtime-lib/core';
import {Descriptions} from "@streamax/starry-components";

interface AppInfoProps {
    appId: string;
    appInfo: any;
}
interface GroupRenderType {
    (text?: any, record?: any): string | React.ReactNode | void;
}
interface GroupType {
    label: string;
    field: string;
    render?: GroupRenderType;
    column?: number;
    ellipsis?: boolean;
}

const AppInfo: React.FC<AppInfoProps> = (props) => {
    const { appId, appInfo: infoData } = props;
    const history = useHistory();
    const { location } = history;
    const { appName } = location?.query;
    const infoGroup: GroupType[] = [
        {
            label: i18n.t('name', '应用名称'),
            field: 'applicationName',
            ellipsis: true,
            render: (text, record) => (
                <span title={i18n.t(`@i18n:@app__${record.applicationId}`, text || appName)}>
                    {i18n.t(`@i18n:@app__${record.applicationId}`, text || appName)}
                </span>
            ),
        },
        {
            label: i18n.t('name', '隐藏应用名称'),
            field: 'hideAppName',
            ellipsis: true,
            render: (text) =>
                text === 1 ? (i18n.t('state', '是') as string) : (i18n.t('state', '否') as string),
        },
        {
            label: i18n.t('name', '应用编码'),
            field: 'applicationId',
            ellipsis: true,
        },
        {
            label: i18n.t('name', '状态'),
            field: 'state',
            ellipsis: true,
            render: (text) => {
                let stateText = '-';
                switch (text) {
                    case 1:
                        stateText = i18n.t('state', '启用');
                        break;
                    case 2:
                        stateText = i18n.t('state', '停用');
                        break;
                    default:
                        break;
                }
                return stateText;
            },
        },
        {
            label: i18n.t('name', '应用Logo'),
            field: 'appLogo',
            render: (text) =>
                text ? (
                    <img style={{ height: '60px', maxWidth: '100%' }} src={text} alt="logo" />
                ) : (
                    '-'
                ),
        },
        {
            label: `${i18n.t('name', '网站图标')}`,
            field: 'appFavicon',
            render: (text) =>
                text ? (
                    <img style={{ height: '60px', maxWidth: '100%' }} src={text} alt="appFavicon" />
                ) : (
                    '-'
                ),
        },
        {
            label: i18n.t('name', '描述'),
            field: 'description',
            ellipsis: false,
            render: (text) => text || '-',
            column: 2,
        },
    ];
    function renderContent(group: GroupType[], data: any) {
        return (
            <Descriptions>
                {group.map(({label, field, render, column,ellipsis}, index) => (
                    <Descriptions.Item label={label} key={index} ellipsis={ellipsis} column={column || 1}>
                        {render ? render(data[field], data) : data[field]}
                    </Descriptions.Item>
                ))}
            </Descriptions>
        );
    }

    return (
        <div className="app-detail-info">
            <StarryInfoBlock
                title={i18n.t('name', '基本信息')}
                operation={
                    <Action
                        code="@base:@page:application.manage@action:edit"
                        url="/applicationManage/edit-application/:appId"
                        fellback={i18n.t('action', '编辑')}
                        params={{
                            appId: appId,
                        }}
                    >
                        <span>{i18n.t('action', '编辑')}</span>
                    </Action>
                }
            >
                {renderContent(infoGroup, infoData)}
            </StarryInfoBlock>
        </div>
    );
};

export default AppInfo;
