@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.message-jump-config-form {
    position: relative;
    &-container {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        .message-jump-config-tab-header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 24px;
            &-title{
                color: var(--poppy-text-color);
                font-weight: 600;
                font-size: 18px;
            }
            .header-right-button{
                flex: 1;
                display: flex;
                justify-content: end;
                span {
                    color: @primary-color;
                    font-weight: normal;
                    font-size: 14px;
                }
                .operate-btn{
                    cursor: pointer;
                }
            }
        }
    }
    &-two {
        // display: flex;
        gap: 80px;
    }
    .message-divider::abroad{
        display: none;
    }
    .sub-title {
        color: @starry-text-color-primary;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        &:before {
            content: "";
            display: inline-block;
            height: 16px;
            line-height: 16px;
            border-left: 4px solid @primary-color;
            margin-right: 8px;
        }
    }
    .poppy-divider-horizontal {
        margin: 32px 0 16px;
    }
}
.app-manage-content-panel-wrapper{
    .poppy-space{
        // display: block;
    }
}
