@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.app-menu {
    .app-menu-tree-wrapper {
        height: 460px;
        margin-right: 4px;
        padding: 0 0 0 24px;
        overflow-y: auto;

        .poppy-tree-switcher {
            line-height: 34px !important;
        }

        .poppy-tree-list {
            padding-right: 14px;
        }
    }

    &-content {
        display: flex;
        gap: 24px;
        width: 100%;
        margin-top: 16px;
        border: 1px solid @starry-border-level-1-color;
        &::abroad {
            border-radius: @border-radius-8;
        }
        &-left {
            position: relative;
            width: 280px;
            border-right: 1px solid @starry-border-level-1-color;
            .header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                height: 46px;
                padding: 0 15px;
                line-height: 46px;
                background: @starry-bg-color-component-degraded;
                .operation-btn {
                    color: @primary-color;
                    cursor: pointer;
                }
            }

            .search-input {
                padding: 20px 24px 4px;

                .poppy-form-item {
                    margin-bottom: 15px;
                }
            }

            .site-tree-search-value {
                color: red !important;
                font-weight: 600;
            }

            .poppy-tree-treenode {
                align-items: center;
                width: 100%;

                .poppy-tree-node-content-wrapper {
                    flex: 1 !important;
                    height: 34px;
                    overflow: hidden;
                    line-height: 32px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    word-break: break-all;
                }
                .poppy-tree-node-selected {
                    // color: #597ef7 !important;
                    background: @menu-item-active-bg !important;
                }
            }
        }

        &-right {
            flex: 1;
            box-sizing: border-box;
            padding-right: 24px;
            padding-left: 24px;

            &::abroad {
                padding: 24px;
            }
            .starry-info-block {
                border-bottom-color: @starry-border-level-1-color!important;
            }
            .poppy-form-item {
                margin-bottom: 12px;
            }

            .operation-text {
                color: @primary-color !important;
            }

            .poppy-form-item {
                display: flex;
                flex-wrap: nowrap;

                .poppy-form-item-label {
                    display: block;
                    overflow: visible;
                }

                .poppy-form-item-control {
                    overflow: hidden;
                }
            }

            .menu-info-icon {
                width: 104px;
                height: 104px;
                background: @primary-1;
                border: 1px solid @primary-5;
                border-radius: 2px;
            }

            .starry-info-block {
                padding: 16px 0 24px 0;
            }
        }
    }
}

.app-menu-content {
    .switch-container {
        margin: 24px 0 0 24px;
        color: @starry-text-color-primary;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;

        .switch-icon {
            margin-left: 8px;
            font-size: 16px;

            &::abroad {
                font-size: 20px;
            }
        }
    }
}

.app-menu-content-drawer {
    .poppy-drawer-body {
        height: 100%;
        padding: 24px 4px 24px 24px;
        overflow-x: hidden;
        .app-menu-content-left {
            width: 100%;
            height: 100%;
            max-height: 100%;
            border-right: none;

            .app-menu-tree-wrapper {
                max-height: calc(100% - 117px);
                padding: 0 0 0 24px;
                overflow-y: auto;
                .poppy-tree-list {
                    padding-right: 20px;
                }
            }
        }
    }
}
