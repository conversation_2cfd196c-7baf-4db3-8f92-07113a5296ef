import React from 'react';
import { InputNumber, Popover, Radio, Space, Tooltip } from '@streamax/poppy';
import { IconInformation } from '@streamax/poppy-icons';
import { i18n, useSystemComponentStyle,StarryAbroadIcon } from '@base-app/runtime-lib';
import type { RadioChangeEvent } from 'antd/lib/radio';
import './VehicleNums.less';
import { RspFormLayout } from '@streamax/responsive-layout';

interface VehicleNumsType {
    value?: number | string;
    onChange?: (value: number | string) => void;
    disabled?: boolean;
    desc?: string;
    radioText?: [string, string];
    radioValue?: [number, number]; // 默认值[-1,1] 要求前者小于后者
    range?: [number, number];
}

export const VehicleNums: React.FunctionComponent<VehicleNumsType> = (props) => {
    const { value, onChange, disabled, desc, radioText, range, radioValue = [-1, 1] } = props;


    const { isAbroadStyle } = useSystemComponentStyle();
    const radioChange = (e: RadioChangeEvent) => {
        onChange?.(e.target.value);
    };


    const innerValue = Number(value) > radioValue[0] ? radioValue[1] : radioValue[0];

    return (
        <>
            <Space className="vehicle-nums-com">
                <Radio.Group value={innerValue} disabled={disabled} onChange={radioChange}>
                    <Radio value={radioValue[0]} className="vehicle-nums-com-radio">
                        <span className="vehicle-nums-text-wraper">
                            {radioText?.[0] || i18n.t('name', '无限制')}
                        </span>
                    </Radio>
                    <Radio value={radioValue[1]}>{radioText?.[1] || i18n.t('name', '限制')}</Radio>
                </Radio.Group>
            </Space>
            <div className="vehicle-nums-sub-content">
                <RspFormLayout layoutType="auto">
                <RspFormLayout.SingleRow>
                {radioValue[1] === innerValue  && (
                    <>
                        <InputNumber
                            size={isAbroadStyle ? 'large' : 'middle'}
                            value={Number(value) < radioValue[1] ? null : value}
                            onChange={(value) => onChange?.(value ?? 1)}
                            precision={0}
                            disabled={disabled || Number(value) < radioValue[1]}
                            min={range?.[0] ?? 1}
                            max={range?.[1] ?? 999}
                        />
                        {desc && (
                            <StarryAbroadIcon>
                                <Tooltip title={desc}>
                                    <IconInformation className="default-icon-information" />
                                </Tooltip>
                            </StarryAbroadIcon>
                        )}
                    </>
                )}
            </RspFormLayout.SingleRow>
            </RspFormLayout>
            </div>
        </>
    );
};
