import React, { useEffect, useRef, useState } from 'react';
import {
    Descriptions,
    OverflowEllipsisContainer,
} from '@streamax/starry-components';
import {
    StarryInfoBlock,
    Action,
    StarryModal,
    StarryAbroadIcon,
} from '@base-app/runtime-lib'; // 使用公共组件
import {
    ProForm,
    Button,
    Space,
    Input,
    Select,
    Form,
    Tree,
    message,
    Spin,
    Container,
} from '@streamax/poppy';
import {
    IconUnfoldFill,
    IconPackupFill,
    IconSortFill,
    IconAddFill,
    IconSearch02,
    IconRequestFill,
    IconSwitch02Fill,
} from '@streamax/poppy-icons';
import { useHistory, Link } from '@base-app/runtime-lib/core';
import {
    i18n,
    Auth,
    utils,
    getAppGlobalData,
    reLoadLanguage,
    useUrlSearchStore,
} from '@base-app/runtime-lib';
import {
    fetchResourcePageList,
    fetchResourceDetail,
    fetchResourceUnLock,
    fetchResourceLockWapper,
    deleteResource,
} from '../../../../service/resource';
import { openNewTabOptions } from '../../constants';
import './appMenu.less';
import { RspDrawerTemplate } from '@streamax/responsive-layout';
import { useSystemComponentStyle } from '@/runtime-lib';
import { updateSystemInitConfig } from '@/runtime-lib/utils/general';

interface appMenuProps {
    appId: string;
}

interface GroupRenderType {
    (text?: any, record?: any): string | React.ReactNode | void;
}

interface GroupShowType {
    (text?: any): boolean;
}

interface GroupType {
    label: string;
    field: string;
    show?: boolean | GroupShowType;
    render?: GroupRenderType;
}

function findIndexCaseInsensitive(str: string, sub: string) {
    return str?.toLocaleLowerCase().indexOf(sub?.toLocaleLowerCase());
}

const AppMenu: React.FC<appMenuProps> = (props) => {
    const history = useHistory();
    const { appId } = props;
    const searchStore = useUrlSearchStore();
    const keys = searchStore.get().keys;
    const [menuDetail, setMenuDetail] = useState<any>({});
    const [menuDataTree, setMenuDataTree] = useState<any>([]);
    const [menuDataList, setMenuDataList] = useState<any>([]);
    const [searchKeys, setSearchKeys] = useState<any[]>([]); // 符合搜索条件的key
    const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
    const [keyword, setKeyWord] = useState<any>(null); // 搜索关键词
    const [selectedKeys, setSelectedKeys] = useState<any[]>([]); // 设置选中的节点
    const [createType, setCreateType] = useState(null); // 菜单类型
    const [loading, setLoading] = useState<boolean>(false); // 菜单类型
    const menuDataTreeSource = useRef<any[]>([]);
    const keywordRef = useRef<any>(null);
    const treeRef = useRef<any>();
    const firstRef = useRef(true);
    const [loadingTree, setLoadingTree] = useState(false);
    const drawerRef = useRef<any>(null);
    const { isAbroadStyle } = useSystemComponentStyle();

    useEffect(() => {
        setMenuDetail({});
        setKeyWord('');
        getMenuList();
    }, [appId, createType]);

    useEffect(() => {
        setMenuDataTree(loop(menuDataTreeSource.current));
        findSearchKeyList();
    }, [keyword]);

    useEffect(() => {
        menuDataList && findSearchKeyList();
    }, [menuDataList]);

    useEffect(() => {
        if (selectedKeys.length > 0 && selectedKeys[0] && menuDataList.length) {
            getMenuDetails(selectedKeys[0]);
            // 展开父节点打开
            const node = menuDataList.find(
                (item: any) => item.key === +selectedKeys[0],
            );
            if (node) {
                setMenuDetail(node);
                setExpandedKeys([...expandedKeys, node.parentId]);
            }
            if (firstRef.current) {
                setTimeout(() => {
                    treeRef.current &&
                        treeRef.current.scrollTo({
                            key: selectedKeys[0],
                            offset: 30,
                        });
                }, 200); // setExpandedKeys设置的100
                firstRef.current = false;
            }
        } else {
            setMenuDetail({});
        }
    }, [selectedKeys, menuDataList]);

    const InfoGroup: GroupType[] = [
        {
            label: i18n.t('name', '菜单名称'),
            field: 'resourceId',
            render: (text: any, record: any) => (
                <>
                    {text || text == 0
                        ? (i18n.t(
                              `@i18n:@menu__${record.resourceCode}`,
                              record.resourceName,
                          ) as string)
                        : '-'}
                </>
            ),
        },
        {
            label: i18n.t('name', '菜单编码'),
            field: 'resourceCode',
            render: (text) => <>{text || '-'}</>,
        },
        {
            label: i18n.t('name', '归属应用'),
            field: 'appId',
            render: (text: any, record: any) => (
                <span>
                    {text || text == 0
                        ? i18n.t(`@i18n:@app__${text}`, record.appName)
                        : '-'}
                </span>
            ),
        },
        {
            label: i18n.t('name', '状态'),
            field: 'state',
            render: (text) => {
                if (text === undefined || text === null) return '-';
                return (
                    <span>
                        {text === 1 && i18n.t('state', '启用')}
                        {text === 2 && i18n.t('state', '停用')}
                    </span>
                );
            },
        },
        {
            label: i18n.t('name', '菜单类型'),
            field: 'createType',
            render: (text) => {
                if (text === undefined || text === null) return '-';
                return (
                    <span>
                        {text === 1 && i18n.t('name', '系统菜单')}
                        {text === 2 && i18n.t('name', '租户菜单')}
                    </span>
                );
            },
        },
        {
            label: i18n.t('name', '菜单级别'),
            field: 'level',
            render: (text) => {
                switch (text) {
                    case 1:
                        return i18n.t('name', '服务入口') as string;
                    case 2:
                        return i18n.t('name', '一级') as string;
                    case 3:
                        return i18n.t('name', '二级') as string;
                    default:
                        return '-';
                }
            },
        },
        {
            label: i18n.t('name', '一级菜单'),
            field: 'parentName',
            show: (record) => {
                return record.level === 3;
            },
            render: (text: any, record: any) => {
                return text
                    ? i18n.t(`@i18n:@menu__${record.parentCode}`, text)
                    : '-';
            },
        },
        {
            label: i18n.t('name', '服务入口'),
            field: 'parentName',
            show: (record) => {
                return record.level === 2;
            },
            render: (text, record) => {
                const name = text
                    ? i18n.t(`@i18n:@menu__${record.parentCode}`, text)
                    : '-';
                return name;
            },
        },
        {
            label: i18n.t('name', '权限管理'),
            field: 'managementSwitch',
            render: (text) => {
                if (text === undefined || text === null) return '-';
                return (
                    <span>
                        {text === 0 && i18n.t('action', '是')}
                        {text === 1 && i18n.t('action', '否')}
                    </span>
                );
            },
        },
        {
            label: i18n.t('name', '展示方式'),
            field: 'openNewTab',
            render: (text) =>
                openNewTabOptions?.find((item) => item.value == text)?.label ||
                '-',
            show: (record) => {
                return true;
            },
        },
        {
            label: i18n.t('name', '点击展示'),
            field: 'clickResponseType',
            render: (text) =>
                text ? (
                    <span>
                        {text === 1 && i18n.t('name', '页面')}
                        {text === 2 && i18n.t('name', '跳转链接')}
                    </span>
                ) : (
                    '-'
                ),
        },
        {
            label: i18n.t('name', '跳转页面'),
            field: 'resourceUrl',
            show: (record) => {
                return (
                    record.clickResponseType === 1 &&
                    (record.level === 2 || record.level === 3)
                );
            },
            render: (text) => {
                return text || '-';
            },
        },
        {
            label: i18n.t('name', '访问路径'),
            field: 'resourceUrl',
            show: (record) => {
                return record.clickResponseType === 1 && record.level === 1;
            },
            render: (text) => {
                return text || '-';
            },
        },
        {
            label: i18n.t('name', '跳转链接'),
            field: 'resourceUrl',
            show: (record) => {
                return record.clickResponseType === 2;
            },
            render: (text) => text || '-',
        },
        {
            label: i18n.t('name', '排序号'),
            field: 'sortValue',
            render: (text) => text || '-',
        },
        {
            label: i18n.t('name', '创建人'),
            field: 'creatorName',
            render: (text) => text || '-',
        },
        {
            label: i18n.t('name', '创建时间'),
            field: 'createTime',
            render: (text) =>
                text ? utils.formator.zeroTimeStampToFormatTime(text) : '-',
        },
        {
            label: i18n.t('name', '图标'),
            field: 'iconUrl',
            render: (text: string) =>
                text ? (
                    <img className="menu-info-icon" src={text} alt="icon" />
                ) : (
                    '-'
                ),
        },
    ];

    // 获取菜单列表
    function getMenuList() {
        setLoadingTree(true);
        fetchResourcePageList({
            appId,
            page: 1,
            pageSize: 1000,
            resourceType: 1,
            createType,
        })
            .then((res: any) => {
                const list = (res.list || []).map((item: any) => {
                    item.title = i18n.t(
                        `@i18n:@menu__${item.resourceCode}`,
                        item.resourceName,
                    );
                    item.key = item.resourceId;
                    return item;
                });
                const treeData = utils.general.arrayToTree(list || [], {
                    idFieldName: 'resourceId',
                    parentIdFieldName: 'parentId',
                    topIdValue: null,
                    order: 'sortValue',
                    sort: 'asc',
                    parentInfo: false,
                });
                menuDataTreeSource.current = treeData;
                setMenuDataTree(loop(treeData));
                setMenuDataList(treeToList(treeData));
            })
            .finally(() => setLoadingTree(false));
    }

    function getMenuDetails(menuId: any) {
        setMenuDetail({});
        if (!menuId) {
            return;
        }
        fetchResourceDetail({
            appId,
            resourceId: menuId,
        }).then((res: any) => {
            setMenuDetail(res || {});
        });
    }

    const items: any = [
        {
            label: i18n.t('name', '菜单类型'),
            name: 'createType',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('message', '请选择菜单类型'),
                options: [
                    {
                        label: i18n.t('name', '系统菜单'),
                        value: 1,
                    },
                    {
                        label: i18n.t('name', '租户菜单'),
                        value: 2,
                    },
                ],
            },
        },
    ];

    function renderInfoGroup(group: GroupType[]) {
        return (
            <Descriptions>
                {group.map(({ label, field, render, show }) => {
                    return (
                        (show === undefined ||
                            (typeof show !== 'boolean' &&
                                show(menuDetail))) && (
                            <Descriptions.Item
                                key={field}
                                label={label}
                                ellipsis={true}
                            >
                                {render
                                    ? render(menuDetail[field], menuDetail)
                                    : menuDetail[field]}
                            </Descriptions.Item>
                        )
                    );
                })}
            </Descriptions>
        );
    }

    // 树相关操作
    function onExpand(keys: any[]) {
        setExpandedKeys(
            Array.from(new Set(keys)).filter((item) => item !== null),
        );
    }
    function findTitleByKey(treeData: any[], targetKey: number): string {
        // 深度优先搜索
        function dfs(nodes: any[]): string | null {
            for (const node of nodes) {
                if (node.key === targetKey) {
                    // 如果找到匹配的key，返回对应的title
                    return node.title.props.title;
                }
                if (node.children && node.children.length) {
                    const result = dfs(node.children);
                    if (result) return result;
                }
            }
            return null;
        }
    
        return dfs(treeData) || '';
    }
    // 启用停用
    function lockOrUnlock() {
        const {
            state,
            resourceName,
            resourceId,
            resourceInitType,
            resourceCode,
        } = menuDetail;
        const isKey = true;
        StarryModal.confirm({
            centered: true,
            title:
                state === 1
                    ? i18n.t('name', '停用确认')
                    : i18n.t('name', '启用确认'),
            content:
                state === 1
                    ? i18n.t('message', '确认要停用“{resourceName}”吗？', {
                          resourceName: i18n.t(
                              `@i18n:@menu__${resourceCode}`,
                              resourceName,
                          ),
                      })
                    : i18n.t('message', '确认要启用“{resourceName}”吗？', {
                          resourceName: i18n.t(
                              `@i18n:@menu__${resourceCode}`,
                              resourceName,
                          ),
                      }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            // icon: <Icon component={DeleteConfirmIcon} />,
            onOk: async () => {
                if (state === 1) {
                    try {
                        const success = await fetchResourceLockWapper(
                            resourceInitType,
                            {
                                resourceIds: resourceId + '',
                                appId,
                            },
                            true,
                            isKey,
                        );
                        if (!success) {
                            return;
                        }
                        message.success(i18n.t('message', '操作成功'));
                    } catch (error) {
                        message.error(i18n.t('message', '操作失败'));
                        await reLoadLanguage(true, true);
                    }
                } else {
                    try {
                        await fetchResourceUnLock({
                            resourceIds: resourceId,
                            appId,
                        });
                    } catch (error) {
                        message.error(i18n.t('message', '操作失败'));
                    }
                }
                // 资源变化，触发页面更新，走网络优先
                setTimeout(() => {
                    updateSystemInitConfig();
                }, 200);
            },
        });
    }

    function deleteMenu() {
        const { resourceName, resourceId, resourceCode } = menuDetail;

        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确认删除{resourceName}页面吗?', {
                resourceName: i18n.t(
                    `@i18n:@menu__${resourceCode}`,
                    resourceName,
                ),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            // icon: <Icon component={DeleteConfirmIcon} />,
            afterClose: () => {
                setLoading(false);
            },
            onOk: async () => {
                if (loading) return Promise.resolve;
                setLoading(true);
                return new Promise(async (resolve, reject) => {
                    try {
                        await deleteResource({
                            resourceIds: resourceId,
                        });
                        resolve(true);
                        message.success(i18n.t('message', '操作成功'));
                        setSelectedKeysAndStore(undefined);
                        setMenuDetail({});
                        setKeyWord('');
                        getMenuList();
                    } catch (error) {
                        message.error(i18n.t('message', '操作失败'));
                        resolve(true);
                    }
                });
            },
        });
    }

    function loop(data: any) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return data.map((item: any) => {
            const index = findIndexCaseInsensitive(item.title, keyword);
            let title = <span title={item.title}>{item.title}</span>;
            if (index > -1 && keyword) {
                const beforeStr = item.title.substr(0, index);
                const midStr = item.title.substr(index, keyword.length);
                const afterStr = item.title.substr(index + keyword.length);
                title = (
                    <span className="site-tree-item" title={item.title}>
                        {beforeStr}
                        <span className="site-tree-search-value">{midStr}</span>
                        {afterStr}
                    </span>
                );
            }
            if (item.children) {
                return { title, key: item.key, children: loop(item.children) };
            }
            return {
                ...item,
                title,
            };
        });
    }

    // 将树转为序列化为数组
    function treeToList(treeData: any) {
        let result: any[] = [];
        treeData.forEach((item: any) => {
            result.push(item);
            if (item.children) {
                result = result.concat(treeToList(item.children));
            }
        });
        return result;
    }

    function setSelectedKeysAndStore(keys: number | undefined) {
        setSelectedKeys(keys ? [keys] : []);
        searchStore.set({
            ...searchStore.get(),
            keys: keys,
        });
    }

    // 查找符合搜索条件的key结合
    function findSearchKeyList() {
        const result: any[] = [];
        const parentIds: any[] = [];
        menuDataList.forEach((item: any) => {
            if (
                findIndexCaseInsensitive(item.title, keyword) > -1 ||
                !keyword
            ) {
                result.push(item.key);
                const loopFindFather = (list: any[], parentId: any) => {
                    const father = list.find(
                        (item) => item.resourceId == parentId,
                    );
                    if (father?.parentId) {
                        parentIds.push(father.parentId);
                        loopFindFather(menuDataList, father.parentId);
                    }
                };
                if (item.parentId) {
                    parentIds.push(item.parentId);
                    loopFindFather(menuDataList, item.parentId);
                }
            }
        });
        if (keyword) {
            setSearchKeys(result);
            setSelectedKeysAndStore(result[0]);
        } else {
            setSearchKeys(result);
            setSelectedKeysAndStore(keys ? +keys : result[0]);
        }
        setTimeout(() => {
            setExpandedKeys(parentIds);
        }, 100);
    }

    // 上下快捷选择
    function handelUpDown(operation: string) {
        if (!menuDetail.resourceId) {
            return;
        }
        const index = searchKeys.indexOf(selectedKeys[0]);
        if (operation === 'down') {
            const nextIndex = index + 1 === searchKeys.length ? 0 : index + 1;
            setSelectedKeysAndStore(searchKeys[nextIndex]);
            treeRef.current &&
                treeRef.current.scrollTo({ key: searchKeys[nextIndex] });
        } else {
            const nextIndex = index === 0 ? searchKeys.length - 1 : index - 1;
            setSelectedKeysAndStore(searchKeys[nextIndex]);
            treeRef.current &&
                treeRef.current.scrollTo({ key: searchKeys[nextIndex] });
        }
    }

    function searchKeyWordFinish(values: any) {
        setKeyWord(values.keywordVal);
    }

    function menuSort() {
        if (!menuDetail.resourceId) {
            message.warn(i18n.t('message', '请选择菜单'));
        } else {
            Action.openActionUrl({
                code: '@base:@page:application.manage:detail@action:menu.sort',
                url: '/applicationManage/applicationMenuSort/:appId/:menuParentId',
                //@ts-ignore
                history,
                params: {
                    appId,
                    menuParentId: menuDetail.parentId || 0,
                },
            });
        }
    }
    const { Left, Right } = RspDrawerTemplate;
    return (
        <div className="app-menu">
            {!getAppGlobalData('APP_ID') && (
                <Container style={{ paddingBottom: 0 }}>
                    <ProForm.QueryForm
                        items={items}
                        layout="vertical"
                        onSearch={(v) => setCreateType(v.createType)}
                        onReset={() => setCreateType(null)}
                    />
                </Container>
            )}
            <Container style={{ marginBottom: 0 }}>
                <div>
                    <Space>
                        <Action
                            code="@base:@page:application.manage:detail@action:menu.add"
                            url="/applicationManage/addApplicationMenu/:appId"
                            fellback={i18n.t('action', '添加')}
                            params={{
                                appId: appId,
                                createType: createType || '',
                                from: window.location.href,
                            }}
                        >
                            <Button type="primary" icon={<IconAddFill />}>
                                {i18n.t('action', '添加')}
                            </Button>
                        </Action>

                        <Auth code="@base:@page:application.manage:detail@action:menu.sort">
                            <Button icon={<IconSortFill />} onClick={menuSort}>
                                {i18n.t('action', '排序')}
                            </Button>
                        </Auth>
                    </Space>
                </div>
                <Spin spinning={loadingTree}>
                    <div className="app-menu-content">
                    <RspDrawerTemplate breakpoint='lg'>
                <Left 
                    drawerTrigger={<div className="switch-container">
                                {
                                    findTitleByKey(menuDataTree, selectedKeys[0])
                                }
                                <IconSwitch02Fill  className='switch-icon' />
                            </div>}
                    drawerProps={{width: 400,className: 'app-menu-content-drawer'}}
                    ref={drawerRef}
                            >
                        <div className="app-menu-content-left">
                            <div className="header">
                                <div>{i18n.t('name', '菜单名称')}</div>
                                <Space>
                                    <StarryAbroadIcon>
                                        <IconPackupFill
                                            className="operation-btn"
                                            title={i18n.t('message', '上一个')}
                                            onClick={() => handelUpDown('up')}
                                        />
                                    </StarryAbroadIcon>
                                    <StarryAbroadIcon>
                                        <IconUnfoldFill
                                            title={i18n.t('message', '下一个')}
                                            className="operation-btn"
                                            onClick={() => handelUpDown('down')}
                                        />
                                    </StarryAbroadIcon>
                                </Space>
                            </div>
                            <div className="search-input">
                                <Form
                                    ref={keywordRef}
                                    onFinish={searchKeyWordFinish}
                                >
                                    <Form.Item
                                        name="keywordVal"
                                        rules={[
                                            {
                                                max: 50,
                                                type: 'string',
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            prefix={
                                                <StarryAbroadIcon>
                                                    <IconSearch02 />
                                                </StarryAbroadIcon>
                                            }
                                            maxLength={50}
                                            placeholder={i18n.t(
                                                'message',
                                                '请输入关键词搜索',
                                            )}
                                        />
                                    </Form.Item>
                                </Form>
                            </div>
                            <div className="app-menu-tree-wrapper">
                                {menuDataTree.length ? (
                                    <Tree
                                        ref={treeRef}
                                        onExpand={onExpand}
                                        expandedKeys={expandedKeys}
                                        defaultExpandAll
                                        treeData={menuDataTree}
                                        selectedKeys={selectedKeys}
                                        onSelect={(keys) => {
                                            drawerRef.current.closeDrawer();
                                            setSelectedKeysAndStore(
                                                keys[0] as number,
                                            );
                                        }}
                                    />
                                ) : null}
                            </div>
                        </div>
                        </Left>
                    <Right>
                        <div className="app-menu-content-right">
                            <StarryInfoBlock
                                title={i18n.t('name', '基本信息')}
                                operation={
                                    menuDetail.resourceId && (
                                        <Space>
                                            <span
                                                onClick={() => lockOrUnlock()}
                                                className="operation-text"
                                            >
                                                {menuDetail.state === 1
                                                    ? i18n.t('state', '停用')
                                                    : i18n.t('state', '启用')}
                                            </span>
                                            {menuDetail.state !== 1 &&
                                                menuDetail.createType !== 1 && (
                                                    <Auth code="@base:@page:application.manage:detail@action:menu.delete">
                                                        <span
                                                            className="operation-text"
                                                            onClick={() => {
                                                                deleteMenu();
                                                            }}
                                                        >
                                                            {i18n.t(
                                                                'action',
                                                                '删除',
                                                            )}
                                                        </span>
                                                    </Auth>
                                                )}
                                            <Action
                                                code="@base:@page:application.manage:detail@action:menu.edit"
                                                url="/applicationManage/edit-applicationMenu/:appId/:menuId"
                                                fellback={i18n.t(
                                                    'action',
                                                    '编辑',
                                                )}
                                                params={{
                                                    appId: appId,
                                                    menuId: menuDetail.resourceId,
                                                    createType:
                                                        menuDetail.createType ||
                                                        '',
                                                    from: window.location.href,
                                                    resourceCode:
                                                        menuDetail.resourceCode,
                                                }}
                                            >
                                                {i18n.t('action', '编辑')}
                                            </Action>
                                        </Space>
                                    )
                                }
                                border={isAbroadStyle ? undefined : 'bottom'}
                            >
                                {renderInfoGroup(InfoGroup)}
                            </StarryInfoBlock>
                        </div>
                        </Right>
                    </RspDrawerTemplate>

                    </div>
                </Spin>
            </Container>
        </div>
    );
};

export default AppMenu;
