@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.driver-discern-wraper-container::abroad {
    margin-bottom: 0 !important;
}
.app-manage-driver-discern-wraper {
    .driver-identify {
        display: flex;
        align-content: center;
        justify-content: space-between;
        height: 32px;
        margin-bottom: 24px;
        .left {
            .title {
                color: @starry-text-color-primary;
                font-weight: 600;
                font-size: 18px;
            }
            .switch {
                margin-left: 16px;
            }
        }
        .right{
            display: flex;
            align-items: center;
            span {
                color: @primary-color;
            }
            .operate-btn{
                cursor: pointer;
            }
        }
    }
    .choose-identify-way {
        .poppy-segmented {
            width: 100%;
        }
        .poppy-segmented-group {
            display: flex;
            .poppy-segmented-item {
                flex: 1;
                max-width: 33%;
            }
        }
    }
    .main-content {
        &-container {
            .tabs-header {
                // display: flex;
                // justify-content: flex-start;
                .rect-tab-item {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 36px;
                    margin-right: 8px;
                    padding: 0 8px;
                    font-weight: 600;
                    line-height: 36px;
                    text-align: center;
                    background: @starry-bg-color-component;
                    border-radius: 2px;
                    cursor: pointer;
                }
                .rect-tab-item::abroad {
                    border-radius: @border-radius-8;
                }

                .active {
                    color: @primary-color;
                    background: @primary-color-light;
                    border: 1px solid @primary-color;
                }
                .disactive {
                    color: @primary-color;
                    background-color: @primary-color-light;
                    border: 1px solid @primary-color;
                }
                .disable {
                    color: @starry-text-color-secondary;
                }
            }

            &-tabs {
                .poppy-tabs-nav-wrap {
                    display: none;
                }
                .poppy-tabs-content-holder {
                    border-right: 0 !important;
                }

                .poppy-tabs-nav {
                    display: none;
                }
                .config-face {
                    margin-top: 12px;
                    padding: 24px;
                    background-color: @starry-bg-color-secondarycontainer;
                    .face-abnormal {
                        margin-bottom: 24px;
                    }
                    .face-abnormal-open {
                        margin-bottom: 0px;
                    }
                    .title {
                        margin-right: 12px;
                        color: @starry-text-color-primary;
                        font-weight: 600;
                        font-size: 14px;
                    }
                    .todo-type-radios > .poppy-radio-wrapper {
                        margin-right: 112px;
                    }
                }
                .config-face::abroad {
                    border-radius: @border-radius-12;
                }
                .advanced-setting {
                    margin-top: 24px;
                    .header {
                        display: inline-block;
                        margin-bottom: 24px;
                        .advance-title {
                            margin-right: 10px;
                            font-weight: 400;
                            font-size: 14px;
                        }
                    }
                    .advance-compare-scope {
                        margin-right: 102px;
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .driver-identify::abroad {
        margin-top: 0;
    }
    .title-driver-popoer {
        max-width: 480px;
    }
    .default-icon-information > svg {
        color: @starry-text-color-secondary;
    }
    .default-icon-information > svg:hover {
        color: @primary-color;
    }
    .edit-button {
        padding: 0;
    }
}
