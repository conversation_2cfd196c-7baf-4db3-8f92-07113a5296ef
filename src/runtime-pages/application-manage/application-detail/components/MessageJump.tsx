import { useState, useEffect } from 'react';
import {Form, Divider, Button, Space, Container} from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import FormConfigItem from './FormConfigItem';
import { fetchResourcePageList } from '@/service/resource';
import { getJumpConfig, updateJumpConfig } from '@/service/application';
import './MessageJump.less';
import { updateSystemInitConfig } from '@/runtime-lib/utils/general';
import { RspFormLayout } from '@streamax/responsive-layout';
import {useSystemComponentStyle} from "@/runtime-lib";

interface PropsType {
    appId: number;
    openIsWhen?: (flag: boolean) => void;
    activeKey: string;
    when: boolean
}
interface Option {
    label: string;
    value: string;
    code: string;
    key: string;
    freeType: string;
    disabled?: boolean;
}
const PAGE_TYPE = 2; // 页面类型
const FREE_TYPE = 1; // 基础资源类型
const STATE_ONLINE = 1; // 在线状态类型

export default (props: PropsType) => {
    const {isAbroadStyle} = useSystemComponentStyle();
    const { appId, activeKey, openIsWhen, when } = props;
    const [isEdit, setIsEdit] = useState(false);
    const [options, setOptions] = useState<Option[]>([]);
    const [licenseOptions, setLicenseOptions] = useState<Option[]>([]);
    const [form] = Form.useForm();
    const [initialValues, setInitialValues] = useState<Record<string, string | number>>({});
    useEffect(() => {
        setIsEdit(false);
        openIsWhen && openIsWhen(false);
        // tab切换要重新请求数据
        requestFn();
    }, [activeKey]);

    useEffect(() => {
        !when && handleCancel();
    }, [when]);

    useEffect(() => {
        requestFn();
    }, [appId]);

    const requestFn = async () => {
        const rs = await fetchResourcePageList({
            page: 1,
            pageSize: 1e8,
            resourceType: PAGE_TYPE,
            appId,
            // state: STATE_ONLINE,
        });
        const list = (rs?.list || []).map((item: Record<string, any>) => {
            const option: Option = {
                label: i18n.t(
                    `@i18n:@page__${item.resourceCode}`,
                    item.resourceName,
                ),
                value: item.resourceUrl,
                key: item.resourceCode,
                code: item.resourceCode,
                freeType: item.freeType,
            };
            if (item.state != STATE_ONLINE) {
                option.disabled = true;
            }
            return option;
        });
        setLicenseOptions(list);
        setOptions(list.filter((item) => item.freeType === FREE_TYPE));
        
        getJumpConfig({
            appIds: appId,
        }).then((rs: Record<string, any>) => {
            const {
                alarmJumpPage,
                evidenceDownloadSuccessJumpPage,
                evidenceDownloadUnSuccessJumpPage,
                videoDownloadSuccessJumpPage,
                videoDownloadUnSuccessJumpPage,
                vehicleLicenseExpireJumpPage,
                tenantCreateSuccessJumpPage,
                tenantCreateUnSuccessJumpPage
            } = rs[0] || {};
            let resultItem = false;
            if(vehicleLicenseExpireJumpPage?.resourceUrl) {
                resultItem = list.find(item => item.value === vehicleLicenseExpireJumpPage?.resourceUrl);
            }
            const values = {
                alarm: alarmJumpPage?.resourceUrl,
                evidenceSucceeded: evidenceDownloadSuccessJumpPage?.resourceUrl,
                evidenceDownloadFailed: evidenceDownloadUnSuccessJumpPage?.resourceUrl,
                videoSucceeded: videoDownloadSuccessJumpPage?.resourceUrl,
                videoDownloadFailed: videoDownloadUnSuccessJumpPage?.resourceUrl,
                licenseExpired: resultItem ? vehicleLicenseExpireJumpPage?.resourceUrl : undefined,
                tenantCreateSuccess: tenantCreateSuccessJumpPage?.resourceUrl,
                tenantCreateFailed: tenantCreateUnSuccessJumpPage?.resourceUrl,
            };
            form.setFieldsValue(values);
            setInitialValues(values);
        });
    };

    const handleEdit = () => {
        setIsEdit(true);
        openIsWhen && openIsWhen(true);
    };

    const handleCancel = () => {
        setIsEdit(false);
        openIsWhen && openIsWhen(false);
        form.resetFields();
        form.setFieldsValue(initialValues);
    };

    const handleSave = () => {
        form.submit();
    };

    const getResourceInfoByUrl = (url: string) => {
        const { value, code } = licenseOptions.find((item) => item.value === url) || {};
        return {
            resourceUrl: value,
            resourceCode: code,
        };
    };

    const onFinish = (values: Record<string, any>) => {
        const {
            alarm,
            evidenceSucceeded,
            evidenceDownloadFailed,
            videoSucceeded,
            videoDownloadFailed,
            licenseExpired,
            tenantCreateSuccess,
            tenantCreateFailed,
        } = values;
        updateJumpConfig({
            appId,
            alarmJumpPage: getResourceInfoByUrl(alarm),
            evidenceDownloadSuccessJumpPage: getResourceInfoByUrl(evidenceSucceeded),
            evidenceDownloadUnSuccessJumpPage: getResourceInfoByUrl(evidenceDownloadFailed),
            videoDownloadSuccessJumpPage: getResourceInfoByUrl(videoSucceeded),
            videoDownloadUnSuccessJumpPage: getResourceInfoByUrl(videoDownloadFailed),
            vehicleLicenseExpireJumpPage: getResourceInfoByUrl(licenseExpired),
            tenantCreateSuccessJumpPage: getResourceInfoByUrl(tenantCreateSuccess),
            tenantCreateUnSuccessJumpPage: getResourceInfoByUrl(tenantCreateFailed),
        }).then(() => {
            openIsWhen && openIsWhen(false);
            setIsEdit(false);
            // 保存后刷新页面
            updateSystemInitConfig();
        });
    };

    return (
        <Form
            className="message-jump-config-form"
            layout="vertical"
            form={form}
            onFinish={onFinish}
        >
            <Container className="message-jump-config-form-container">
                <div className={'message-jump-config-tab-header'}>
                    <div className={'message-jump-config-tab-header-title'}>
                        { i18n.t('name', '消息跳转')}
                    </div>
                    <div className="header-right-button">
                        {isEdit ? (
                            <Space size={isAbroadStyle? 24: 12} className="space-group">
                                <span className={'operate-btn'} onClick={handleCancel}>
                                    {i18n.t('action', '取消')}
                                </span>
                                        <span className={'operate-btn'} onClick={handleSave}>
                                    {i18n.t('action', '保存')}
                                </span>
                            </Space>
                        ) : (
                            <span className={'operate-btn'} onClick={handleEdit}>
                        {i18n.t('action', '编辑')}
                    </span>
                        )}
                    </div>
                </div>
            <div className="sub-title"><span>{i18n.t('name', '报警弹窗页配置')}</span></div>
            <RspFormLayout layoutType="auto">
            <RspFormLayout.SingleRow>
            <FormConfigItem
                label={i18n.t('name', '报警详情跳转')}
                name="alarm"
                options={options}
                disabled={!isEdit}
            />
            </RspFormLayout.SingleRow>
            </RspFormLayout>
            <Divider className="message-divider"  />
            <div className="sub-title">{i18n.t('name', '证据下载弹窗跳转页配置')}</div>
                <div className="message-jump-config-form-two">
                <RspFormLayout layoutType="auto">
                <FormConfigItem
                    label={i18n.t('name', '成功详情跳转')}
                    name="evidenceSucceeded"
                    options={options}
                    disabled={!isEdit}
                />
                <FormConfigItem
                    label={i18n.t('name', '失败详情跳转')}
                    name="evidenceDownloadFailed"
                    options={options}
                    disabled={!isEdit}
                />
                </RspFormLayout>
                </div>
                <Divider className="message-divider" />
                <div className="sub-title">{i18n.t('name', '视频下载弹窗跳转页配置')}</div>
                <div className="message-jump-config-form-two">
                <RspFormLayout layoutType="auto">
                <FormConfigItem
                    label={i18n.t('name', '成功详情跳转')}
                    name="videoSucceeded"
                    options={options}
                    disabled={!isEdit}
                />

                <FormConfigItem
                    label={i18n.t('name', '失败详情跳转')}
                    name="videoDownloadFailed"
                    options={options}
                    disabled={!isEdit}
                />
                </RspFormLayout>
                </div>
                <Divider className="message-divider" />
                <div className="sub-title">{i18n.t('name', '车辆License到期弹窗跳转页配置')}</div>
                <RspFormLayout layoutType="auto">
                <FormConfigItem
                label={i18n.t('name', 'License详情跳转')}
                name="licenseExpired"
                options={licenseOptions}
                disabled={!isEdit}
                />
                </RspFormLayout>
                <Divider className="message-divider" />
                <div className="sub-title">{i18n.t('name', '租户创建弹窗跳转页配置')}</div>
                <div className="message-jump-config-form-two">
                <RspFormLayout layoutType="auto">
                <FormConfigItem
                    label={i18n.t('name', '成功详情跳转')}
                    name="tenantCreateSuccess"
                    options={options}
                    disabled={!isEdit}
                />
                <FormConfigItem
                    label={i18n.t('name', '失败详情跳转')}
                    name="tenantCreateFailed"
                    options={options}
                    disabled={!isEdit}
                />
                </RspFormLayout>
                </div>
            </Container>
        </Form>
    );
};
