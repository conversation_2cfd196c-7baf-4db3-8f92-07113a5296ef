/*
 * @LastEditTime: 2025-01-20 11:18:27
 */
import { Form, Select } from '@streamax/poppy';
import {i18n, StarryAbroadFormItem} from '@base-app/runtime-lib';

interface PropsType {
    name: string;
    label: string;
    options: any[];
    disabled: boolean;
    marginBottom?:number
}

export default (props: PropsType) => {
    const { name, label, options, disabled, marginBottom } = props;
    return (
        <StarryAbroadFormItem label={label} name={name} style={{marginBottom }}>
            <Select
                placeholder={i18n.t('message', '请选择跳转页面')}
                disabled={disabled}
                options={options}
                showSearch
                filterOption={(inputValue, option) =>
                    !!(
                        inputValue &&
                        ((option?.label as string) || '')
                            .toLowerCase()
                            .includes(inputValue?.toLowerCase())
                    )
                }
                allowClear
            />
        </StarryAbroadFormItem>
    );
};
