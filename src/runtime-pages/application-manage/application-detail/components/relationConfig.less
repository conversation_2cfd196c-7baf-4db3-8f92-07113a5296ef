@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.relation-config {
    .starry-info-block-wrapper-container:last-child::abroad {
        margin-bottom: 0;
    }
    .col-container:last-child {
        margin-bottom: -24px;
    }
    .starry-info-block {
        padding-top: 0 !important;
    }
    .starry-info-block {
        margin-bottom: 24px;
        border-bottom-color: @starry-border-level-1-color!important;

    }
    .starry-info-block::abroad {
        margin-bottom: 0;
    }
    .config-item {
        display: flex;
        flex-wrap: wrap;
    }
    .default-icon-information > svg {
        color: @starry-text-color-secondary;
    }
    .default-icon-information > svg:hover {
        color: @primary-color;
    }
    .poppy-input-number-input-wrap {
        width: 100px;
    }
    .operation-text{
        color: @primary-color;
    }
}
