import {StarryTable, Action, StarryModal, StarryAbroadIcon} from '@base-app/runtime-lib';
import {
    Input,
    Select,
    Space,
    Switch,
    message,
    Button,
    Form,
    Tooltip,
} from '@streamax/poppy';
import { ColumnsType } from '@streamax/poppy/lib/table';
import React, { useRef, useEffect, useState } from 'react';
import {IconEdit, IconDeleteFill, IconSortFill, IconRequestFill} from '@streamax/poppy-icons';
import Icon from '@streamax/poppy-icons/lib/Icon';
import {
    Auth,
    i18n,
    utils,
    reLoadLanguage,
    useUrlSearchStore,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import classnames from 'classnames';
import {
    fetchResourcePageList,
    fetchResourceUnLock,
    fetchResourceLockWapper,
    deleteResource,
} from '../../../../service/resource';
import './appPage.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

interface appPageProps {
    appId: string;
}
interface PageItem {
    resourceCode: string; // 资源编码
    freeType: number; // 资源类型 1，基础资源；2，增值资源
    resourceName: string; // 资源名称
    resourceUrl: string; // 资源路径
    parentName: string; // 上级资源名称
    parentId: number; // 上级资源Id
    creatorName: string; // 创建人
    createTime: string; // 创建时间
    resourceId: number; // 资源id
}

const AppPage: React.FC<appPageProps> = (props) => {
    const { appId } = props;
    const tableRef = useRef<any>(null);
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const { state, search, page, pageSize } = searchStore.get();
    const [selectKey, setSelectKey] = useState<PageItem | null>(null);

    useEffect(() => {
        if (state || search) {
            form.setFieldsValue({
                search,
                state: state ? Number(state) : null,
            });
        }
        //  StarryTable 暂不支持page pageSize
    }, []);

    const columns: ColumnsType<any> = [
        {
            title: i18n.t('name', '页面编码'),
            dataIndex: 'resourceCode',
            ellipsis: { showTitle: false },
            render: (text) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '页面名称'),
            dataIndex: 'resourceId',
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => (
                <Auth
                    code="@base:@page:application.manage:detail@action:page.edit"
                    fellback={
                        <span
                            title={i18n.t(
                                `@i18n:@page__${record.resourceCode}`,
                                record.resourceName,
                            )}
                        >
                            {i18n.t(`@i18n:@page__${record.resourceCode}`, record.resourceName)}
                        </span>
                    }
                >
                    <Tooltip
                        title={i18n.t(`@i18n:@page__${record.resourceCode}`, record.resourceName)}
                    >
                        <StarryAbroadOverflowEllipsisContainer>
                            <Link
                                to={`/applicationManage/applicationPageDetail/${appId}/${
                                    record.resourceId
                                }?fromPage=${location.pathname}||${location.search
                                    .replaceAll('?', '')
                                    .replaceAll('&', '**')}`}
                            >
                                {i18n.t(`@i18n:@page__${record.resourceCode}`, record.resourceName)}
                            </Link>
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                </Auth>
            ),
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'state',
            filterMultiple: false,
            ellipsis: true,
            width: 180,
            render: (text: any, record: any) => {
                return (
                    <Space>
                        <div
                            className={classnames(
                                'state-sign',
                                `state-sign-${text === 1 ? 'green' : 'grey'}`,
                            )}
                        ></div>
                        <span>
                            {text === 2 ? i18n.t('state', '停用') : i18n.t('state', '启用')}
                        </span>
                        <Switch
                            size="small"
                            checked={text === 1}
                            onChange={() => {
                                text === 1 ? lock(record) : unlock(record);
                            }}
                        />
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '权限管理'),
            dataIndex: 'managementSwitch',
            ellipsis: { showTitle: false },
            render: (text) => (text === 0 ? i18n.t('state', '是') : i18n.t('state', '否')),
        },
        {
            title: i18n.t('name', '访问路径'),
            dataIndex: 'resourceUrl',
            ellipsis: { showTitle: false },
            render: (text) => (
                <Tooltip title={text}>
                    <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '上级页面'),
            dataIndex: 'parentName',
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => {
                const name = text ? i18n.t(`@i18n:@page__${record.parentCode}`, text) : '-';
                return (
                    <Tooltip title={record.parentId ? name : '-'}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {record.parentId ? name : '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '创建人'),
            dataIndex: 'creatorName',
            ellipsis: { showTitle: false },
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>{text}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            sorter: true,
            ellipsis: true,
            render: (text) => (
                <Tooltip title={text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}>
                    <StarryAbroadOverflowEllipsisContainer>
                        {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '操作'),
            key: 'operate',
            width: 120,
            render: (text: any, record: any) => (
                <Space size={24} className="operate-btn">
                    <Auth code="@base:@page:application.manage:detail@action:page.edit">
                        <Link
                            to={`/applicationManage/edit-application-page/${appId}/${
                                record.resourceId
                            }?from=${encodeURIComponent(
                                location.pathname + location.search,
                            )}&resourceCode=${encodeURIComponent(record.resourceCode)}`}
                        >
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <Icon
                                    className="operate-btn"
                                    //@ts-ignore
                                    component={IconEdit}
                                />
                            </Tooltip>
                        </Link>
                    </Auth>
                    {record.state === 2 && record.createType !== 1 && (
                        <Tooltip title={i18n.t('action', '删除')}>
                            <Icon
                                //@ts-ignore
                                component={IconDeleteFill}
                                title={i18n.t('action')}
                                onClick={() => deletePage(record)}
                            />
                        </Tooltip>
                    )}
                </Space>
            ),
        },
    ];

    // 删除页面
    function deletePage(record: any) {
        const { resourceId, resourceName, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确认删除{resourceName}页面吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () => {
                deleteResource({
                    resourceIds: resourceId.toString(),
                })
                    .then((res: any) => {
                        const { data } = res;
                        if (data.code === 1000) {
                            message.success(i18n.t('message', '操作成功'));
                            tableRef.current.reload();
                        } else {
                            message.warn(data.message);
                        }
                    })
                    .catch(() => {
                        message.success(i18n.t('message', '操作失败'));
                    });
            },
        });
    }
    // 启用
    function unlock(record: any) {
        const { resourceId, resourceName, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '启用确认'),
            content: i18n.t('message', '确认启用“ {resourceName} ”页面吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                fetchResourceUnLock({
                    resourceIds: resourceId.toString(),
                    appId,
                })
                    .then(() => {
                        message.success(i18n.t('message', '操作成功'));
                        tableRef.current.reload();
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    // 停用
    function lock(record: any) {
        const { resourceId, resourceName, resourceInitType, resourceCode } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '停用确认'),
            content: i18n.t('message', '确认停用“{resourceName}”页面吗?', {
                resourceName: i18n.t(`@i18n:@page__${resourceCode}`, resourceName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                fetchResourceLockWapper(
                    resourceInitType,
                    {
                        resourceIds: resourceId.toString(),
                        appId,
                    },
                    false,
                )
                    .then(async (success) => {
                        if (!success) {
                            return;
                        }
                        message.success(i18n.t('message', '操作成功'));
                        await reLoadLanguage(true, true);
                        tableRef.current.reload();
                    })
                    .catch(() => {
                        message.error(i18n.t('message', '操作失败'));
                    });
            },
        });
    }

    const items: any = [
        {
            label: i18n.t('name', '页面状态'),
            name: 'state',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('messaget', '请选择页面状态'),
                options: [
                    {
                        label: i18n.t('name', '启用'),
                        value: 1,
                    },
                    {
                        label: i18n.t('name', '停用'),
                        value: 2,
                    },
                ],
            },
        },
        {
            label: i18n.t('name', '页面'),
            name: 'search',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入页面编码或页面名称'),
                maxLength: 50,
            },
            itemProps: {
                rules: [{ max: 50, type: 'string' }],
            },
        },
    ];

    const toolbar = {
        leftRender: () => (
            <Space>
                {/* <Link
                    to={`/applicationManage/edit-application-page/${appId}`}
                    title={i18n.t('action', '新增')}
                >
                    <Button
                        type="primary"
                        icon={<IconAdd />}
                    >
                        {i18n.t(
                            'name',
                            '新增',
                        )}
                    </Button>
                </Link> */}
                <Action
                    code="@base:@page:application.manage:detail@action:page.sort"
                    url="/applicationManage/applicationPageSort/:appId"
                    params={{
                        appId: appId,
                        parentId: selectKey?.resourceId || 0,
                    }}
                >
                    <Button icon={<IconSortFill />}>{i18n.t('name', '排序')}</Button>
                </Action>
            </Space>
        ),
        columnSetting: {
            storageKey: '@base:@page:application.manage.detail.app.page',
            disabledKeys: ['resourceCode', 'resourceName', 'operate'],
        },
    };

    const fetchFunc = async (params: any) => {
        const search = params.search && params.search.trim();
        // params.search = search
        //     ? encodeURI(`${search} in:resourceName[tr],resourceCode[tr]`)
        //     : '';
        const data = await fetchResourcePageList({
            ...params,
            appId,
            resourceType: 2,
            search: search
                ? encodeURIComponent(`${search} in:resourceName[tr],resourceCode[tr]`)
                : '',
        });
        searchStore.set({
            ...params,
        });
        return Promise.resolve({
            list: data.list || [],
            total: data.total || 0,
        });
    };
    const setRowClassName = (record: PageItem) => {
        return record.resourceCode === selectKey?.resourceCode ? 'select-row-style' : '';
    };
    const onSelect = (record: PageItem) => {
        return {
            onClick: () => {
                if (record.resourceCode === selectKey?.resourceCode) {
                    setSelectKey(null);
                } else {
                    setSelectKey(record);
                }
            },
        };
    };

    return (
        <div className="app-page">
            <StarryTable
                aroundBordered
                fetchDataAfterMount
                fetchDataFunc={fetchFunc}
                ref={tableRef}
                queryProps={{
                    items,
                    form,
                }}
                pagination={{
                    defaultCurrent: Number(page) || 1,
                    defaultPageSize: Number(pageSize) || 20,
                }}
                rowKey="applicationId"
                toolbar={toolbar}
                columns={columns}
                onRow={onSelect}
                rowClassName={setRowClassName}
            />
        </div>
    );
};

export default AppPage;
