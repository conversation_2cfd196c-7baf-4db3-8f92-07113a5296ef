import { useState, useEffect } from 'react';
import { Space, Radio, message } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { StarryInfoBlock } from '@base-app/runtime-lib';
import { fetchApplicationDetail, editAppDataManageConfig } from '../../../../service/application';
import { useSubmitFn } from '@streamax/hooks';
import './dataManage.less';

interface DataManageProps {
    appId: number | string;
    ConfigData: [];
    openIsWhen?: (flag: boolean) => void;
    activeKey: string;
    when: boolean;
}
interface ConfigItem {
    name: string;
    state: number;
    custom: number;
}

const DataManage = (props: DataManageProps) => {
    const { appId, ConfigData: data, openIsWhen, activeKey, when } = props;
    const FieldNameMap: any = {
        fleet: i18n.t('name', '车组'),
        vehicle: i18n.t('name', '车辆'),
        device: i18n.t('name', '设备'),
        driver: i18n.t('name', '司机'),
        application: i18n.t('name', '应用'),
        user: i18n.t('name', '用户'),
        channel: i18n.t('name', '通道'),
        line: i18n.t('name', '线路'),
        station: i18n.t('name', '场站'),
    };

    const [editing, setEditing] = useState<boolean>(false);

    const [configData, setConfigData] = useState<ConfigItem[]>(data);
    useEffect(() => {
        setConfigData(data?.filter((item: any) => item.name !== 'application'));
    }, [data]);
    useEffect(() => {
        setEditing(false);
    }, [activeKey]);
    useEffect(() => {
        !when && setEditing(false);
    }, [when]);
    // 取消编辑
    function cancel() {
        fetchApplicationDetail({ appId }).then((res: any) => {
            setConfigData(res.dataManagement?.filter((item: any) => item.name !== 'application'));
            setEditing(false);
            openIsWhen && openIsWhen(false);
        });
    }

    // 保存
    const [save] = useSubmitFn(async () => {
        const dataManagement = configData.map((item: ConfigItem) => ({
            name: item.name,
            state: item.state,
            custom: item.custom,
        }));
        await editAppDataManageConfig({
            appId,
            dataManagement: JSON.stringify(dataManagement),
        });
        message.success(i18n.t('message', '操作成功'));
        setEditing(false);
        openIsWhen && openIsWhen(false);
    });

    // 修改配置
    function editConfig(e: any, index: number) {
        const nextConfig = JSON.parse(JSON.stringify(configData));
        nextConfig[index].state = e.target.value;
        setConfigData(nextConfig?.filter((item: any) => item.name !== 'application'));
    }

    return (
        <div className="appSetting-dataManage">
            <StarryInfoBlock
                title={i18n.t('name', '管理数据对象配置')}
                operation={
                    !editing ? (
                        <span
                            onClick={() => {
                                openIsWhen && openIsWhen(true);
                                setEditing(true);
                            }}
                        >
                            {i18n.t('name', '编辑')}
                        </span>
                    ) : (
                        <Space>
                            <span onClick={cancel}>{i18n.t('name', '取消')}</span>
                            <span onClick={save}>{i18n.t('name', '保存')}</span>
                        </Space>
                    )
                }
            >
                {configData &&
                    configData.map((item: ConfigItem, index: number) => (
                        <div key={item.name} className="config-item">
                            <span className="label-name">{FieldNameMap[item.name]}：</span>
                            <Radio.Group
                                disabled={!editing || item.custom === 2}
                                value={item.state}
                                onChange={(e) => editConfig(e, index)}
                            >
                                <Radio value={1} style={{ marginRight: '50px' }}>
                                    {i18n.t('name', '开启')}
                                </Radio>
                                <Radio value={2}>{i18n.t('name', '关闭')}</Radio>
                            </Radio.Group>
                        </div>
                    ))}
            </StarryInfoBlock>
        </div>
    );
};

export default DataManage;
