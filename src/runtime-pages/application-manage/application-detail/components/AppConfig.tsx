import React, { memo, useEffect, useRef, useState } from 'react';
import type { ReactNode } from 'react';
import { Container, Tabs } from '@streamax/poppy';
import { i18n, Auth } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import DataManage from './DataManage';
import MessageJump from './MessageJump';
import RelationConfig from './RelationConfig';
import { DriverDiscern } from './DriverDiscern';
import './AppConfig.less';
import { RspDrawerTemplate } from '@streamax/responsive-layout';
import { IconSwitch02Fill } from '@streamax/poppy-icons';

const { TabPane } = Tabs;
type WhenConfig = Record<'company' | 'vehicle' | 'driver' | 'driverIdentify', boolean>;
interface Props {
    children?: ReactNode;
    appId: string;
    appDetails: any;
    openIsWhen: (flag: boolean) => void;
    openIsWhenKey: (key: string, flag: boolean) => void;
    when: boolean;
    whenConfig: WhenConfig
}
const AppConfig = ({ appId, appDetails, openIsWhen, openIsWhenKey,when,whenConfig }: Props) => {
    const drawerRef = useRef<any>(null);
    const handleTabClick = (key: string) => {
        drawerRef.current.closeDrawer();
        const active = key === activeKey;
        !active && setActiveKey(key);
    };


    const panels = [
        {
            key: 'data',
            title: i18n.t('name', '数据管理'),
            content: Auth.check(
                '@base:@page:application.manage:detail@action:tab.application.data',
            ) ? (
                <DataManage
                    ConfigData={appDetails.dataManagement}
                    appId={appId}
                    openIsWhen={openIsWhen}
                    activeKey={activeKey}
                    when={when}
                />
            ) : null,
            rendered: false,
        },
        {
            key: 'messageJump',
            title: i18n.t('name', '消息跳转'),
            content: Auth.check(
                '@base:@page:application.manage:detail@action:tab.message.jump',
            ) ? (
                <MessageJump
                    appId={appId}
                    openIsWhen={openIsWhen}
                    activeKey={activeKey}
                    when={when}
                />
            ) : null,
            rendered: false,
        },
        {
            key: 'config',
            title: i18n.t('name', '关联配置'),
            content: Auth.check(
                '@base:@page:application.manage:detail@action:tab.application.config',
            ) ? (
                <RelationConfig
                    appId={appId}
                    openIsWhen={openIsWhenKey}
                    activeKey={activeKey}
                    whenConfig={whenConfig}
                />
            ) : null,
            rendered: false,
        },
        {
            key: 'driver',
            title: i18n.t('name', '司机识别'),
            content: Auth.check(
                '@base:@page:application.manage:detail@action:tab.application.driver',
            ) ? (
                <DriverDiscern
                    appId={appId}
                    openIsWhen={openIsWhenKey}
                    activeKey={activeKey}
                    whenConfig={whenConfig}
                />
            ) : null,
            rendered: false,
        },
    ];
    const [activeKey, setActiveKey] = useState(
        panels.filter((item) => item.content)[0].key,
    );


    const tabs =
        panels.filter((item) => item.content).length > 0 ? (
            <Container className={'app-manage-wrapper-tab-container'}>
                <Tabs
                    onTabClick={(key: string) => handleTabClick(key)}
                    tabPosition={'right'}
                    style={{ height: '100%' }}
                    activeKey={activeKey}
                >
                    {panels
                        .filter((item) => item.content)
                        .map(({ key, title }) => {
                            return (
                                <TabPane
                                    tab={
                                        <div className="tab-pane-custom">
                                            <OverflowEllipsisContainer>
                                                {title}
                                            </OverflowEllipsisContainer>
                                        </div>
                                    }
                                    key={key}
                                />
                            );
                        })}
                </Tabs>
            </Container>
        ) : null;
    const { Left, Right } = RspDrawerTemplate;
    return (
        <div className="app-manage-wrapper">
            <RspDrawerTemplate breakpoint="lg">
                <Left
                    drawerTrigger={
                        <div className="switch-container">
                            {
                                panels.find((item) => item.key === activeKey)
                                    ?.title
                            }
                            <IconSwitch02Fill className="switch-icon" />
                        </div>
                    }
                    drawerProps={{
                        width: 400,
                        className: 'app-manage-wrapper-drawer',
                    }}
                    ref={drawerRef}
                >
                    <div className="app-manage-vertical-tab-wrapper">
                        {tabs}
                    </div>
                </Left>
                <Right>
                    <div className="app-manage-content-panel-wrapper">
                        {
                            panels.filter((item) => item.key === activeKey)[0]
                                .content
                        }
                    </div>
                </Right>
            </RspDrawerTemplate>
        </div>
    );
};

export default memo(AppConfig);
