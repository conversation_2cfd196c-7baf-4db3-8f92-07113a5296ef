import React, { useState, useRef, useEffect } from 'react';
import { useSubmitFn } from '@streamax/hooks';
import {StarryAbroadFormItem, StarryAbroadIcon, StarryInfoBlock, useSystemComponentStyle} from '@base-app/runtime-lib';
import { IconInformation } from '@streamax/poppy-icons';
import {Form, Radio, InputNumber, Space, Popover, Row, Col, message, Button, Tooltip} from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { fetchApplicationConfig, editApplicationConfig } from '../../../../service/application';
import './relationConfig.less';
import { RspFormLayout } from '@streamax/responsive-layout';

type WhenConfig = Record<'company' | 'vehicle' | 'driver' | 'driverIdentify', boolean>;
interface RelationConfigProps {
    appId: string;
    openIsWhen?: (key: string, flag: boolean) => void;
    activeKey: string;
    whenConfig: WhenConfig
}

interface GroupRenderType {
    (record?: any): string | React.ReactNode | void;
}

interface GroupType {
    label: string;
    field: string;
    render?: GroupRenderType;
}

const dataField: any = {
    company: {
        field1: 'fleet.vehicle.max',
        field2: 'fleet.driver.max',
    },
    vehicle: {
        field1: 'vehicle.fleet.max',
        field2: 'vehicle.device.max',
    },
    driver: {
        field1: 'driver.fleet.max',
    },
};

const DEFAULT_VALUE = {
    'fleet.vehicle.max': 0,
    'fleet.driver.max': 0,
    'vehicle.fleet.max': 0,
    'vehicle.device.max': 0,
    'driver.fleet.max': 0,
};

const RelationConfig: React.FC<RelationConfigProps> = (props) => {
    const cardList = [
        {
            title: i18n.t('name', '车组关联配置'),
            groupName: 'company',
        },
        {
            title: i18n.t('name', '车辆关联配置'),
            groupName: 'vehicle',
        },
        {
            title: i18n.t('name', '司机关联配置'),
            groupName: 'driver',
        },
    ];

    const { appId, openIsWhen, activeKey, whenConfig } = props;
    const [data, setData] = useState<any>(DEFAULT_VALUE);
    const dataSource = useRef<any>(DEFAULT_VALUE);
    const [editGroup, setEditGroup] = useState<string[]>(['none']);
    const companyFormRef = useRef<any>(null);
    const vehicleFormRef = useRef<any>(null);
    const driverFormRef = useRef<any>(null);
    const {isAbroadStyle} = useSystemComponentStyle();

    useEffect(() => {
        getData();
    }, []);
    useEffect(() => {
        setEditGroup(['none']);
        getData();
    }, [activeKey]);

    useEffect(() => {
        const allFalse = Object.values(whenConfig).every(value => !value);
        if(allFalse) {
            setEditGroup(['none']);
            getData();
        }
    }, [whenConfig]);

    function getData() {
        fetchApplicationConfig({
            appId,
        }).then((res: any) => {
            (res || []).forEach((item: any) => {
                dataSource.current[item.configKey] = item.value;
            });
            setEditGroup([]);
            setData(dataSource.current);
            const setFormData: any = {};
            for (const key in dataSource.current) {
                if (Object.prototype.hasOwnProperty.call(dataSource.current, key)) {
                    setFormData[key] = dataSource.current[key] > 0 ? dataSource.current[key] : null;
                }
            }
            companyFormRef.current.setFieldsValue(setFormData);
            vehicleFormRef.current.setFieldsValue(setFormData);
            driverFormRef.current.setFieldsValue(setFormData);
        });
    }

    function getGroupList(groupName: string) {
        const obj: any = {};
        switch (groupName) {
            case 'company':
                obj.label1 = i18n.t('name', '最大车辆数');
                obj.label2 = i18n.t('name', '最大司机数');
                break;
            case 'vehicle':
                obj.label1 = i18n.t('name', '关联多车组');
                obj.label2 = i18n.t('name', '关联多设备');
                obj.desc1 = i18n.t(
                    'message',
                    '开启关联多车组，车辆可归属于多个车组。同时对车辆最多归属车组数可限制，不填写为不限制。开启和关闭功能不会影响已录入数据',
                );
                obj.desc2 = i18n.t(
                    'message',
                    '开启关联多设备，车辆可添加多个设备。同时对车辆最多关联设备数可限制，不填写为不限制。开启和关闭功能不会影响已录入数据',
                );
                break;
            case 'driver':
                obj.label1 = i18n.t('name', '关联多车组');
                obj.desc1 = i18n.t(
                    'message',
                    '开启关联多车组，司机可归属于多个车组。同时对司机最多归属车组数可限制，不填写为不限制。开启和关闭功能不会影响已录入数据',
                );
                break;
            default:
                break;
        }

        const { label1, label2, desc1, desc2 } = obj;

        // 校验数值
        function validatorNumber(rule: any, value: any) {
            if (!value || parseInt(value, 10) < 1 || parseInt(value, 10) > 999) {
                return Promise.reject(i18n.t('message', '请输入1-999之间的正整数'));
            }
            return Promise.resolve();
        }

        const groupItems: GroupType[] = [
            {
                label: label1,
                field: dataField[groupName].field1,
                render: (text: any) => (
                    <Form.Item
                        className='form-item'
                        label={
                            <Space size={8}>
                                <div>{label1}</div>
                                <div>
                                    {desc1 && (
                                        <StarryAbroadIcon>
                                            <Tooltip title={desc1}>
                                                <IconInformation className="default-icon-information" />
                                            </Tooltip>
                                        </StarryAbroadIcon>
                                    )}
                                </div>
                            </Space>
                        }
                    >
                        <RspFormLayout layoutType="fixed">
                        <RspFormLayout.Col>
                        <Form.Item>
                            <Radio.Group
                                value={text > 0 ? 1 : 0}
                                onChange={(e: any) => {
                                    const fieldName = dataField[groupName].field1;
                                    const value = e.target.value === 0 ? null : 1;
                                    setData({
                                        ...data,
                                        [fieldName]: value,
                                    });
                                    companyFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                    vehicleFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                    driverFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                }}
                                disabled={!editGroup.includes(groupName)}
                            >
                                <Radio value={0}>
                                    {groupName === 'company'
                                        ? i18n.t('state', '无限制')
                                        : i18n.t('state', '关闭')}
                                </Radio>
                                <Radio value={1}>
                                    {groupName === 'company'
                                        ? i18n.t('state', '限制')
                                        : i18n.t('state', '开启')}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                        <StarryAbroadFormItem
                            name={dataField[groupName].field1}
                            rules={text > 0 ? [{ validator: validatorNumber }] : []}
                            style={{
                                marginBottom: 0,
                            }}
                        >
                            <InputNumber
                                min={1}
                                max={999}
                                //@ts-ignore
                                formatter={(value) => Math.round(value)}
                                disabled={!editGroup.includes(groupName) || text < 1}
                            />
                        </StarryAbroadFormItem>
                        </RspFormLayout.Col>
                </RspFormLayout>
                    </Form.Item>
                ),
            },
            label2 && {
                label: label2,
                field: dataField[groupName].field2,
                render: (text: any) => (
                    <Form.Item
                        label={
                            <Space size={8}>
                                <div>{label2}</div>
                                <StarryAbroadIcon>
                                    {desc2 && (
                                        <Tooltip title={desc2}>
                                            <IconInformation className="default-icon-information" />
                                        </Tooltip>
                                    )}
                                </StarryAbroadIcon>
                            </Space>
                        }
                    >
                        <RspFormLayout layoutType="fixed">
                        <RspFormLayout.Col>
                        <Form.Item>
                            <Radio.Group
                                value={text > 0 ? 1 : 0}
                                onChange={(e: any) => {
                                    const fieldName = dataField[groupName].field2;
                                    const value = e.target.value === 0 ? null : 1;
                                    setData({
                                        ...data,
                                        [fieldName]: value,
                                    });
                                    companyFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                    vehicleFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                    driverFormRef.current.setFieldsValue({
                                        [fieldName]: value,
                                    });
                                }}
                                disabled={!editGroup.includes(groupName)}
                            >
                                <Radio value={0}>
                                    {groupName === 'company'
                                        ? i18n.t('state', '无限制')
                                        : i18n.t('state', '关闭')}
                                </Radio>
                                <Radio value={1}>
                                    {groupName === 'company'
                                        ? i18n.t('state', '限制')
                                        : i18n.t('state', '开启')}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                        <StarryAbroadFormItem
                            name={dataField[groupName].field2}
                            rules={text > 0 ? [{ validator: validatorNumber }] : []}
                            style={{
                                marginBottom: 0,
                            }}
                        >
                            <InputNumber
                                min={1}
                                max={999}
                                //@ts-ignore
                                formatter={(value) => Math.round(value)}
                                disabled={!editGroup.includes(groupName) || text < 1}
                            />
                        </StarryAbroadFormItem>
                        </RspFormLayout.Col>
                </RspFormLayout>
                    </Form.Item>
                ),
            },
        ].filter((p: any) => p);
        return groupItems;
    }

    function renderContent(group: GroupType[], groupName: string) {
        return (
            <Form
                layout='vertical'
                ref={(function () {
                    if (groupName === 'company') {
                        return companyFormRef;
                    }
                    if (groupName === 'vehicle') {
                        return vehicleFormRef;
                    }
                    if (groupName === 'driver') {
                        return driverFormRef;
                    }
                    return undefined;
                })()}
                onFinish={(values) => saveEdit(values, groupName)}
            >
                <Row>
                    {group.map(({ field, render }) => (
                        <Col span={24} key={field} className='col-container'>
                            {render ? render(data[field]) : data[field]}
                        </Col>
                    ))}
                </Row>
            </Form>
        );
    }

    function changeFormState(groupName: string) {
        if (editGroup.includes(groupName)) {
            setEditGroup(
                editGroup.filter((item) => {
                    return item !== groupName;
                }),
            );
        } else {
            setEditGroup(editGroup.concat([groupName]));
        }
    }

    // 取消编辑
    function cancelEdit(groupName: string) {
        openIsWhen && openIsWhen(groupName, false);
        const field1Name = dataField[groupName].field1;
        const field2Name = dataField[groupName].field2;
        const setFormData = {
            [field1Name]:
                dataSource.current[field1Name] < 1 ? null : dataSource.current[field1Name],
            [field2Name]:
                dataSource.current[field2Name] < 1 ? null : dataSource.current[field2Name],
        };
        companyFormRef.current.setFieldsValue(setFormData);
        vehicleFormRef.current.setFieldsValue(setFormData);
        driverFormRef.current.setFieldsValue(setFormData);
        setData({
            ...data,
            [field1Name]: dataSource.current[field1Name],
            [field2Name]: dataSource.current[field2Name],
        });

        changeFormState(groupName);
    }

    // 保存
    const [saveEdit] = useSubmitFn(async (values: any, groupName: string) => {
        const configList = [];
        for (const key in values) {
            if (Object.prototype.hasOwnProperty.call(values, key)) {
                configList.push({
                    key,
                    value: values[key] || -1,
                });
            }
        }
        await editApplicationConfig({
            appId,
            configList,
        });
        dataSource.current = {
            ...dataSource.current,
            ...values,
        };
        message.success(i18n.t('message', '操作成功'));
        // 重新获取数据， 部分更新
        setEditGroup(editGroup.filter(item => item !== groupName));
        setData(dataSource.current);
        if (groupName === 'company') {
            companyFormRef.current.setFieldsValue(dataSource.current);
        }
        else if (groupName === 'vehicle') {
            vehicleFormRef.current.setFieldsValue(dataSource.current);
        }
        else if (groupName === 'driver') {
            driverFormRef.current.setFieldsValue(dataSource.current);
        }
    });


    const isShowBorder = (list: any[], index: number) => {
        if (isAbroadStyle) return;
        // eslint-disable-next-line consistent-return
        return index === list.length - 1 ? undefined : 'bottom';
    };

    return (
        <div className="relation-config">
            {cardList.map((card, index) => (
                <StarryInfoBlock
                    title={card.title}
                    key={card.groupName}
                    operation={
                        <div>
                            {editGroup.includes(card.groupName) ? (
                                <Space>
                                    <span
                                        className="operation-text"
                                        onClick={() => cancelEdit(card.groupName)}
                                    >
                                        {i18n.t('action', '取消')}
                                    </span>
                                    <span
                                        className="operation-text"
                                        onClick={() => {
                                            if (card.groupName === 'company') {
                                                companyFormRef.current.submit();
                                                openIsWhen && openIsWhen(card.groupName, false);
                                            }
                                            if (card.groupName === 'vehicle') {
                                                vehicleFormRef.current.submit();
                                                openIsWhen && openIsWhen(card.groupName, false);
                                            }
                                            if (card.groupName === 'driver') {
                                                driverFormRef.current.submit();
                                                openIsWhen && openIsWhen(card.groupName, false);
                                            }
                                        }}
                                    >
                                        {i18n.t('action', '保存')}
                                    </span>
                                </Space>
                            ) : (
                                <span
                                    className="edit-btn operation-text"
                                    onClick={() => {
                                        openIsWhen && openIsWhen(card.groupName, true);
                                        changeFormState(card.groupName);
                                    }}
                                >
                                    {i18n.t('action', '编辑')}
                                </span>
                            )}
                        </div>
                    }
                    border={isShowBorder(cardList, index)}
                >
                    {renderContent(getGroupList(card.groupName), card.groupName)}
                </StarryInfoBlock>
            ))}
        </div>
    );
};

export default RelationConfig;
