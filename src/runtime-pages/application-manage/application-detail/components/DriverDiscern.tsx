import { monthList, weekList } from '@/utils/commonFun';
import timeUtils from '@/utils/time';
import { i18n, StarryAbroadFormItem, StarryAbroadIcon } from '@base-app/runtime-lib';
import {
    Checkbox,
    Col,
    Container,
    Form,
    message,
    Radio,
    Row,
    Segmented,
    Space,
    Switch,
    Tabs,
    TimePicker,
    Tooltip
} from '@streamax/poppy';
import { IconArrow02Down, IconArrow02Up, IconInformation } from '@streamax/poppy-icons';
import type { RadioGroupProps } from '@streamax/poppy/lib/radio';
import { RspFormLayout, useBreakpoint } from '@streamax/responsive-layout';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { editApplicationConfig, fetchApplicationConfig } from '../../../../service/application';
import './DriverDiscern.less';
import { VehicleNums } from './VehicleNums';

const { TabPane } = Tabs;
type WhenConfig = Record<'company' | 'vehicle' | 'driver' | 'driverIdentify', boolean>;
interface DriverDiscernProps {
    openIsWhen?: (key: string, flag: boolean) => void;
    appId: string;
    activeKey: string;
    whenConfig: WhenConfig
}
const { formatTime } = timeUtils;
const DAY = 1;
const WEEK = 2;
const MONTH = 3;

const dynamicWays = [
    { type: 'plat', key: 'driver.face.compare.plateform', value: { isOpen: 0 } },
    { type: 'device', key: 'driver.face.compare.device', value: { isOpen: 0 } },
    { type: 'button', key: 'driver.dynamic.compare.ibutton', value: { isOpen: 0 } },
    { type: 'card', key: 'driver.dynamic.compare.swipe.card', value: { isOpen: 0 } },
];

const KeyFormItemMap = {
    'driver.vehicle.max': 'driverRelatedVehicle', // 司机绑定车辆
    'vehicle.driver.max': 'vehicleRelatedDriver', // 车辆绑定司机
    'face.compare': 'faceAbnormal', // 人脸异常代办
    'driver.device.offline': 'driverDeviceOffline', // 司机下线
    'driver.dynamic.binding.scope': 'compareScope', // 异常司机驾驶关联范围
    'driver.binding.dynamic': 'dynamicBind', // 动态绑定
    'driver.face.compare.device': 'device', // 设备
    'driver.face.compare.plateform': 'plat', // 平台
    'driver.dynamic.compare.swipe.card': 'card', // 刷卡
    'driver.dynamic.compare.ibutton': 'button', // iButton
};
const OFF = 1;
const NO = 0;

const { Item } = Form;

interface ItemConfig {
    configKey: string;
    value: any;
}
type DriverIdentifyWayType = 'singleDriver' | 'dynamicDriver' | 'rangeDriver';
interface Config {
    serverData: ItemConfig[];
    formValue: any;
    configData: any;
}
const dynamicKeys = [
    'face.compare',
    'driver.device.offline',
    'driver.face.compare.plateform',
    'driver.face.compare.device',
    'driver.binding.dynamic',
    'driver.dynamic.binding.scope', // 异常司机驾驶关联范围
    'driver.dynamic.compare.ibutton', // iButton
    'driver.dynamic.compare.swipe.card', // 刷卡
];
const rangeKeys = ['driver.vehicle.max', 'vehicle.driver.max', ...dynamicKeys];
const CONFIGKEY = rangeKeys;

type VerificationWay = 'plat' | 'device' | 'button' | 'card';
export const DriverDiscern: React.FunctionComponent<DriverDiscernProps> = (props) => {
    const initTabs: { text: string; value: VerificationWay; isActive: boolean }[] = [
        { text: i18n.t('name', '平台人脸识别'), value: 'plat', isActive: true },
        { text: i18n.t('name', '设备人脸识别'), value: 'device', isActive: false },
        { text: i18n.t('name', 'iButton识别'), value: 'button', isActive: false },
        { text: i18n.t('name', '刷卡识别'), value: 'card', isActive: false },
    ];
    const { appId, openIsWhen, activeKey, whenConfig } = props;
    const [form] = Form.useForm();
    const [initData, setInitData] = useState<Config>({} as Config); // 远程数据源 包含组装好的form表单数据
    const [todoCycle, setTodoCycle] = useState(DAY);
    const [platOrDevice, setPlatOrDevice] = useState<VerificationWay | undefined>(); // 动态验证4种方式之一
    const initTime = [moment('08:30', 'HH:mm'), moment('09:30', 'HH:mm')];

    const [openIdentify, setOpenIdentify] = useState<boolean>(false); // 司机识别
    const [driverIdentifyWay, setDriverIdentifyWay] = useState<DriverIdentifyWayType>(); // 身份识别方式
    const [faceAbnormalHandleIsOpen, setFaceAbnormalHandleIsOpen] = useState(true); // 人脸异常代办
    const [tabs, setTabs] = useState(initTabs); // 4个身份识别tabs
    const [showAdvanced, setShowAdvanced] = useState<boolean>();
    const [isEdit, setIsEdit] = useState<boolean>();

    // 使用useBreakpoint hook获取当前断点
    const breakpoint = useBreakpoint();

    // 根据断点计算列数
    // x ＜576(xs),576 ≤ x＜768(sm),768≤ x＜992(md),992≤ x＜1200(lg),1200 ≤ x＜1600(xl),x ≥1600(xxl)
    const getColSpan = () => {
        if (breakpoint === 'xl' || breakpoint === 'xxl') {
            // 分辨率 > 1200px，按钮四列展示
            return 6; // 24/4=6
        } else if (breakpoint === 'lg' || breakpoint === 'md') {
            // 768px ≤ X ＜ 1200px，按钮双列展示
            return 12; // 24/2=12
        } else {
            // 576px ≤ X ＜ 768px 或更小，按钮单列展示
            return 24; // 单列铺满
        }
    };

    useEffect(() => {
        initPageContent();
        setIsEdit(false);
    }, [activeKey]);

    useEffect(()=>{
        if(!whenConfig.driverIdentify) {
            initPageContent();
            setIsEdit(false);
        }
    },[whenConfig]);

    const editItem = () => {
        setIsEdit(true);
        openIsWhen && openIsWhen('driverIdentify', true);
    };
    async function getData() {
        return fetchApplicationConfig({
            appId,
        }).then((res) => {
            const originData = res?.filter((item: any) => CONFIGKEY.includes(item.configKey));
            const specialData: {
                faceAbnormalHandleIsOpen: boolean;
                todoCycle: number;
            } = {
                todoCycle: DAY,
                faceAbnormalHandleIsOpen: true,
            };
            const serverDataMapObj: Record<string, any> = {};
            const serverData = originData.map((item: any) => {
                let value;
                try {
                    value = JSON.parse(item.value);
                } catch (error) {
                    message.error(i18n.t('message', '解析字符串错误'));
                }
                serverDataMapObj[item.configKey.replace(/\./g, '_')] = value;
                item.value = value;
                return item;
            });

            const configData: any = {};
            let formValue: any = {};
            const advanceData: any = {};
            let time1: Moment[] = [];
            serverData.map((item: { configKey: string; value: any }) => {
                if (rangeKeys.includes(item.configKey)) {
                    formValue[KeyFormItemMap[item.configKey]] = item.value;
                }
            });
            // 设置是否开启司机识别 是否开启人脸异常代办 动态识别方式 高级设置数据 人脸异常代办数据处理
            serverData.forEach(
                (item: {
                    configKey: string;
                    value: {
                        faceAbnormalHandleIsOpen: boolean;
                        isOpen: number;
                        compareScope: string;
                        value: number;
                        faceAbnormalHandletype: number;
                        faceAbnormalHandleType: number;
                        faceAbnormalHandleValue: string;
                        faceAbnormalHandleStartTime: number;
                        faceAbnormalHandleEndTime: number;
                    };
                }) => {
                    if (
                        item.configKey === 'driver.vehicle.max' ||
                        item.configKey === 'driver.binding.dynamic'
                    ) {
                        if (item.value.isOpen) {
                            setOpenIdentify(true);
                            configData.openIdentify = true;
                        }
                    }
                    if (item.configKey === 'driver.dynamic.binding.scope') {
                        if (typeof item.value.compareScope === 'number') {
                            item.value.compareScope = String(item.value.compareScope);
                        }
                        const targetScope = item.value.compareScope
                            .split(',')
                            .map((item) => Number(item));

                        // 设置高级设置里的关联范围
                        advanceData.compareScope = targetScope || [0, 1, 2]; // 关联范围默认值三个全选
                    }
                    if (item.configKey === 'driver.device.offline') {
                        // 设置高级设置里的司机下线
                        advanceData.driverDeviceOffline = item.value.value || -1; // 默认值无限制（-1）
                    }
                    if (item.configKey === 'face.compare') {
                        if (item.value.faceAbnormalHandleIsOpen) {
                            // 设置是否开启人脸代办
                            specialData.faceAbnormalHandleIsOpen = true;
                        } else {
                            specialData.faceAbnormalHandleIsOpen = false;
                        }
                        const { faceAbnormalHandleStartTime, faceAbnormalHandleEndTime } =
                            item.value;
                        const days = item.value.faceAbnormalHandleValue;
                        const todoType = Number(
                            item.value.faceAbnormalHandletype ||
                            item.value.faceAbnormalHandleType ||
                            DAY,
                        );
                        specialData.todoCycle = todoType;
                        formValue = {
                            ...formValue,
                            todoCycle: todoType,
                            todoTime2: todoType === WEEK ? days : [1],
                            todoTime3: todoType === MONTH ? days : [1],
                        };
                        time1 = [
                            faceAbnormalHandleStartTime + ''
                                ? moment(
                                    formatTime(Number(faceAbnormalHandleStartTime) * 1000, true),
                                    'HH:mm',
                                )
                                : initTime[0],
                            faceAbnormalHandleEndTime + ''
                                ? moment(
                                    formatTime(Number(faceAbnormalHandleEndTime) * 1000, true),
                                    'HH:mm',
                                )
                                : initTime[1],
                        ];
                    }
                    if (item.configKey === 'driver.vehicle.max') {
                        formValue[KeyFormItemMap[item.configKey]] = item.value.value;
                    }
                    if (item.configKey === 'vehicle.driver.max') {
                        formValue[KeyFormItemMap[item.configKey]] = item.value;
                    }
                },
            );
            // 设置身份识别方式（三选一）driver.binding.dynamic driver.vehicle.max
            const staticIdentify = serverData.find(
                (item: { configKey: string }) => item.configKey === 'driver.vehicle.max',
            );
            const dynamicIdnetify = serverData.find(
                (item: { configKey: string }) => item.configKey === 'driver.binding.dynamic',
            );
            if (staticIdentify?.value.isOpen && dynamicIdnetify?.value.isOpen) {
                setDriverIdentifyWay('rangeDriver');
                configData.driverIdentifyWay = 'rangeDriver';
            } else if (staticIdentify?.value.isOpen) {
                setDriverIdentifyWay('singleDriver');
                configData.driverIdentifyWay = 'singleDriver';
            } else if (dynamicIdnetify?.value.isOpen) {
                setDriverIdentifyWay('dynamicDriver');
                configData.driverIdentifyWay = 'dynamicDriver';
            }

            let platOrDevice: VerificationWay | undefined = 'plat';
            const { isOpen: platIsOpen } = serverDataMapObj['driver_face_compare_plateform'] || {}; // 平台人脸验证开关
            const { isOpen: deviceIsOpen } = serverDataMapObj['driver_face_compare_device'] || {}; // 设备人脸验证开关
            const { isOpen: ibuttonIsOpen } =
                serverDataMapObj['driver_dynamic_compare_ibutton'] || {}; // iButton验证开关
            const { isOpen: cardIsOpen } =
                serverDataMapObj['driver_dynamic_compare_swipe_card'] || {}; // card验证开关

            if (!!deviceIsOpen) {
                platOrDevice = 'device';
            } else if (!!platIsOpen) {
                platOrDevice = 'plat';
            } else if (!!ibuttonIsOpen) {
                platOrDevice = 'button';
            } else if (!!cardIsOpen) {
                platOrDevice = 'card';
            }
            setPlatOrDevice(platOrDevice);
            configData.dynamicWay = platOrDevice;
            configData.faceAbnormalIsOpen = specialData.faceAbnormalHandleIsOpen;
            formValue = {
                ...formValue,
                ...advanceData,
                todoTime1: time1,
            };

            setInitData({
                formValue,
                serverData,
                configData,
            });
            return {
                serverData,
                formValue,
                configData,
                ...specialData,
            };
        });
    }
    // 初始化页面
    const initPageContent = async () => {
        // configData,formValue是在初始化页面时获取到数据后自己组装的数据
        form.resetFields();
        const { todoCycle, faceAbnormalHandleIsOpen, configData, formValue } = await getData();
        setPlatOrDevice(configData.dynamicWay);
        setFaceAbnormalHandleIsOpen(faceAbnormalHandleIsOpen);
        setDriverIdentifyWay(configData.driverIdentifyWay);
        setTodoCycle(todoCycle);
        form.setFieldsValue({
            ...formValue,
        });
    };

    useEffect(() => {
        initPageContent();
    }, []);

    // 取消
    const cancle = () => {
        openIsWhen && openIsWhen('driverIdentify', false);
        setTodoCycle(initData.formValue.todoCycle);
        setIsEdit(false);
        setDriverIdentifyWay(initData.configData.driverIdentifyWay);
        setPlatOrDevice(initData.configData.dynamicWay);
        setOpenIdentify(initData.configData.openIdentify);
        setFaceAbnormalHandleIsOpen(initData.configData.faceAbnormalIsOpen);
        setTimeout(() => {
            form.setFieldsValue({
                ...initData.formValue,
            });
        });
    };

    const postParams = async (params: any, operationName: string) => {
        editApplicationConfig(params)
            .then(() => {
                message.success(i18n.t('message', '操作成功'));
                setIsEdit(false);
                openIsWhen && openIsWhen(operationName, false);
            })
            .then(() => getData());
    };

    const save = async () => {
        const param: any = { appId };
        if (openIdentify) {
            //开启司机识别
            const {
                compareScope,
                driverDeviceOffline, // 高级设置
                todoCycle,
                todoTime1,
                todoTime2,
                todoTime3,
            } = form.getFieldsValue();
            const baseConfigList = [
                { key: 'driver.binding.dynamic', value: { isOpen: OFF } },
                {
                    key: 'driver.dynamic.binding.scope',
                    value: { compareScope: compareScope?.join() || '0' },
                },
                {
                    key: 'driver.device.offline',
                    value: {
                        isOpen: driverDeviceOffline === -1 ? NO : OFF,
                        value: driverDeviceOffline || -1,
                    },
                },
                ...dynamicWays.map((item) => {
                    if (item.type === platOrDevice) {
                        return { key: item.key, value: { isOpen: OFF } };
                    } else {
                        return { key: item.key, value: { isOpen: NO } };
                    }
                }),
            ];
            let faceConfig: any;
            if (driverIdentifyWay !== 'singleDriver') {
                const timeRange = todoTime1?.map(
                    (item: Moment) => item.hour() * 60 * 60 + item.minute() * 60,
                );

                if (faceAbnormalHandleIsOpen) {
                    // 开启人脸验证
                    faceConfig = {
                        // face.compare
                        faceAbnormalHandleEndTime: timeRange && timeRange[1],
                        faceAbnormalHandleStartTime: timeRange && timeRange[0],
                        faceAbnormalHandleIsOpen: 1,
                        faceAbnormalHandleValue: todoCycle === WEEK ? todoTime2 : todoTime3,
                        faceAbnormalHandleType: todoCycle,
                    };
                } else {
                    // 关闭人脸验证
                    faceConfig = {
                        faceAbnormalHandleIsOpen: 0,
                    };
                }
            }
            const driverBindVehicle = form.getFieldValue('driverRelatedVehicle');
            const vehicleBindDriver = form.getFieldValue('vehicleRelatedDriver');
            if (driverIdentifyWay === 'singleDriver') {
                // 绑定单个司机
                param.configList = [
                    { key: 'driver.vehicle.max', value: { isOpen: OFF, value: driverBindVehicle } },
                    { key: 'driver.binding.dynamic', value: { isOpen: NO } },
                ];
            } else if (driverIdentifyWay === 'dynamicDriver') {
                // 动态绑定
                param.configList = [
                    ...baseConfigList,
                    { key: 'face.compare', value: faceConfig },
                    { key: 'driver.vehicle.max', value: { isOpen: NO } }, // 关闭静态绑定
                ];
            } else if (driverIdentifyWay === 'rangeDriver') {
                // 范围
                param.configList = [
                    ...baseConfigList,
                    { key: 'face.compare', value: faceConfig },
                    { key: 'driver.vehicle.max', value: { isOpen: OFF, value: driverBindVehicle } }, // 开启静态绑定
                    { key: 'vehicle.driver.max', value: vehicleBindDriver },
                ];
            }
        } else {
            param.configList = [
                { key: 'driver.binding.dynamic', value: { isOpen: NO } },
                { key: 'driver.vehicle.max', value: { isOpen: NO } },
                { key: 'vehicle.driver.max', value: -1 },
                { key: 'driver.face.compare.device', value: { isOpen: 0 } },
                { key: 'driver.face.compare.plateform', value: { isOpen: 0 } },
                { key: 'driver.dynamic.compare.swipe.card', value: { isOpen: 0 } },
                { key: 'driver.dynamic.compare.ibutton', value: { isOpen: 0 } },
                { key: 'driver.dynamic.binding.scope', value: { compareScope: '0,1,2' } },
                { key: 'driver.device.offline', value: { isOpen: 0, value: -1 } },
                {
                    key: 'face.compare',
                    value: {
                        faceAbnormalHandleType: DAY,
                        faceAbnormalHandleIsOpen: 1, // 默认开启
                        faceAbnormalHandleValue: [1], // 默认值
                    },
                },
            ];
        }
        postParams(param, 'driverIdentify');
    };

    const operationDom = () => {
        if (isEdit) {
            return (
                <Space >
                    <span className={'operate-btn'} onClick={cancle}>
                        {i18n.t('action', '取消')}
                    </span>
                    <span className={'operate-btn'} onClick={save}>
                        {i18n.t('action', '保存')}
                    </span>
                </Space>
            );
        } else {
            return (
                <span className={'operate-btn'} onClick={editItem}>
                    {i18n.t('action', '编辑')}
                </span>
            );
        }
    };
    //
    const cycleChange: RadioGroupProps['onChange'] = (e) => {
        setTodoCycle(e.target.value);
    };

    // 开关状态改变
    const switchChange = (checked: boolean, item: string) => {
        if (item === 'driverIdentify' && checked) {
            // 开启司机识别
            setOpenIdentify(true);
            setDriverIdentifyWay('singleDriver');
        } else if (item === 'driverIdentify' && !checked) {
            // 关闭司机识别
            setOpenIdentify(false);
        }
        if (item === 'faceAbnormal' && checked) {
            setFaceAbnormalHandleIsOpen(true);
        } else if (item === 'faceAbnormal' && !checked) {
            setFaceAbnormalHandleIsOpen(false);
        }
    };
    // 司机识别方式改变
    const driverIdentifyChange = (value: string) => {
        setDriverIdentifyWay(value);
        setTodoCycle(DAY);
        form.setFieldsValue({
            driverDeviceOffline: -1, // -1为无限制默认值
            driverRelatedVehicle: -1,
            vehicleRelatedDriver: -1,
            compareScope: initData?.formValue?.compareScope, // 关联范围全选
        });
    };

    // 4种动态识别方式改变
    const activeTabChange = (text: string, value: VerificationWay) => {
        if (!isEdit) {
            return;
        }
        if (value === 'plat') {
            form.setFieldsValue({
                todoTime1: initTime,
            });
        }
        // setTabValue(value);
        setPlatOrDevice(value);
        const newTabs = initTabs.map((item) => {
            if (item.text === text) {
                return { text: item.text, value: item.value, isActive: true };
            } else {
                return { text: item.text, value: item.value, isActive: false };
            }
        });
        setTabs(newTabs);
    };
    const getActiveClassName = (isActive: boolean) => {
        if (isActive) {
            return isEdit ? 'active' : 'disactive';
        } else {
            return isEdit ? '' : 'disable';
        }
    };
    const rectTab = (text: string, value: VerificationWay, isActive: boolean) => (
        <div
            className={`rect-tab-item ${getActiveClassName(isActive)}`}
            onClick={() => activeTabChange(text, value)}
        >
            <OverflowEllipsisContainer>{text}</OverflowEllipsisContainer>
        </div>
    );

    // 展示人脸代办异常内容
    const faceAbnormal = (
        <div className="config-face">
            <div className={`face-abnormal ${faceAbnormalHandleIsOpen ? '' : 'face-abnormal-open'}`}>
                <span className="title">{i18n.t('name', '人脸异常代办')}</span>
                <Switch
                    disabled={!isEdit}
                    onChange={(checked) => switchChange(checked, 'faceAbnormal')}
                    checked={faceAbnormalHandleIsOpen}
                />
            </div>
            {faceAbnormalHandleIsOpen && (
                <>
                    <Item
                        name="todoCycle"
                        label={
                            <OverflowEllipsisContainer>
                                {i18n.t('name', '待办周期:')}
                            </OverflowEllipsisContainer>
                        }
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        initialValue={DAY}
                    >
                        <Radio.Group
                            onChange={cycleChange}
                            disabled={!isEdit}
                            className="todo-type-radios"
                        >
                            <Radio value={DAY}>
                                <span className="text-wraper"> {i18n.t('name', '每日')}</span>
                            </Radio>
                            <Radio value={WEEK}>
                                <span className="text-wraper">{i18n.t('name', '每周')}</span>
                            </Radio>
                            <Radio value={MONTH}>
                                <span className="text-wraper">{i18n.t('name', '每月')}</span>
                            </Radio>
                        </Radio.Group>
                    </Item>
                    <Item
                        hidden={todoCycle != WEEK}
                        colon={false}
                        label=""
                        name="todoTime2"
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        initialValue={[1]} //默认周一
                    >
                        <Checkbox.Group disabled={!isEdit}>
                            {weekList.map((item, index) => {
                                return (
                                    <Checkbox key={index} value={index + 1}>
                                        <span className="text-wraper">{item}</span>
                                    </Checkbox>
                                );
                            })}
                        </Checkbox.Group>
                    </Item>
                    <Item
                        colon={false}
                        hidden={todoCycle != MONTH}
                        name="todoTime3"
                        label=""
                        rules={[
                            {
                                required: true,
                            },
                        ]}
                        initialValue={[1]} // 默认一号
                    >
                        <Checkbox.Group disabled={!isEdit}>
                            <Row style={{ width: "100%" }} gutter={[10, 10]}>
                                {monthList().map((item, index) => {
                                    return (
                                        <Col key={index} span={3}>
                                            <Checkbox value={index + 1}>
                                                <span className="text-wraper">{item}</span>
                                            </Checkbox>
                                        </Col>
                                    );
                                })}
                            </Row>
                        </Checkbox.Group>
                    </Item>
                    <RspFormLayout layoutType="auto">
                        <RspFormLayout.SingleRow>
                            <StarryAbroadFormItem
                                name="todoTime1"
                                label={
                                    <OverflowEllipsisContainer>
                                        {i18n.t('name', '待办时间:')}
                                    </OverflowEllipsisContainer>
                                }
                                rules={[
                                    {
                                        required: true,
                                    },
                                ]}
                                initialValue={initTime}
                                style={{ marginBottom: 0 }}
                            >
                                <TimePicker.RangePicker
                                    style={{ width: '100%' }}
                                    format={'HH:mm'}
                                    disabled={!isEdit}
                                />
                            </StarryAbroadFormItem>
                        </RspFormLayout.SingleRow>
                    </RspFormLayout>
                </>
            )}
        </div>
    );
    const showAdvancedChange = () => {
        setShowAdvanced(!showAdvanced);
    };
    // 高级设置
    const advancedSetting = (
        <div className="advanced-setting">
            <Space size={8} onClick={showAdvancedChange}>
                <a className="advance-title">{i18n.t('name', '高级设置')}</a>
                <a>{showAdvanced ? <IconArrow02Up /> : <IconArrow02Down />}</a>
            </Space>
            {showAdvanced && (
                <div style={{ marginTop: 24 }}>
                    {driverIdentifyWay !== 'dynamicDriver' && (
                        <Item
                            name="compareScope"
                            label={
                                <>
                                    <OverflowEllipsisContainer>
                                        {i18n.t('name', '司机驾驶关联范围')}
                                    </OverflowEllipsisContainer>
                                    <StarryAbroadIcon>
                                        <Tooltip
                                            title={() => (
                                                <div>
                                                    {i18n.t(
                                                        'message',
                                                        '如果用户驾驶了关联范围以外的车辆，平台会产生异常驾驶报警',
                                                    )}
                                                </div>
                                            )}
                                            className="question-icon"
                                            placement="right"
                                        >
                                            <IconInformation
                                                style={{ marginLeft: '8px' }}
                                                className="default-icon-information"
                                            />
                                        </Tooltip>
                                    </StarryAbroadIcon>
                                </>
                            }
                            rules={[{ required: true }]}
                            initialValue={[0, 1, 2]}
                        >
                            <Checkbox.Group disabled={!isEdit}>
                                <Checkbox
                                    disabled={true}
                                    value={0}
                                    className="advance-compare-scope"
                                >
                                    <span className="text-wraper">
                                        {' '}
                                        {i18n.t('name', '关联车辆')}{' '}
                                    </span>
                                </Checkbox>
                                <Checkbox value={1} className="advance-compare-scope">
                                    <span className="text-wraper">
                                        {i18n.t('name', '关联车组')}
                                    </span>
                                </Checkbox>
                                <Checkbox value={2} className="advance-compare-scope">
                                    {i18n.t('name', '关联车组(包含子车组)')}
                                </Checkbox>
                            </Checkbox.Group>
                        </Item>
                    )}
                    <Item
                        name="driverDeviceOffline"
                        label={
                            <>
                                <OverflowEllipsisContainer>
                                    {i18n.t('name', '车辆下线时,自动将司机下线')}
                                </OverflowEllipsisContainer>
                                <StarryAbroadIcon>
                                    <Tooltip
                                        title={() => (
                                            <div>
                                                {i18n.t(
                                                    'message',
                                                    '车辆下线持续时间达到设置时长后，司机自动下线',
                                                )}
                                            </div>
                                        )}
                                        className="question-icon"
                                        placement="right"
                                    >
                                        <IconInformation
                                            style={{ marginLeft: '8px' }}
                                            className="default-icon-information"
                                        />
                                    </Tooltip>
                                </StarryAbroadIcon>
                            </>
                        }
                        rules={[{ required: true }]}
                        initialValue={-1} // 默认值无限制
                        style={{
                            marginBottom: 0
                        }}
                    >
                        <VehicleNums
                            range={[0, 3600]}
                            radioText={[
                                i18n.t('name', '关闭'),
                                i18n.t('name', '开启'),
                            ]}
                            radioValue={[-1, 0]}
                            disabled={!isEdit}
                        />
                    </Item>
                </div>
            )}
        </div>
    );
    // 指定单个司机驾驶main-content展示的内容
    const singleIdentifyContent = () => {
        return (
            <div className="main-content-container">
                <div className="single-driver">
                    <Item
                        style={{
                            marginBottom:
                                driverIdentifyWay === 'singleDriver' ? 0 : '',
                        }}
                        name="driverRelatedVehicle"
                        label={
                            <Space size={0}>
                                {i18n.t('name', '司机关联车辆数')}
                                <StarryAbroadIcon>
                                    <Tooltip
                                        title={() => (
                                            <div>
                                                {i18n.t(
                                                    'message',
                                                    '单司机最多关联车辆数可限制，开启和关闭功能不会影响已录入数据',
                                                )}
                                            </div>
                                        )}
                                        className="question-icon"
                                        placement="right"
                                    >
                                        <IconInformation
                                            style={{ marginLeft: '8px' }}
                                            className="default-icon-information"
                                        />
                                    </Tooltip>
                                </StarryAbroadIcon>
                            </Space>
                        }
                        rules={[{ required: true }]}
                        initialValue={-1}
                        className="single-content-item"
                    >
                        <VehicleNums disabled={!isEdit} />
                    </Item>
                    {driverIdentifyWay === 'rangeDriver' && (
                        <Item
                            name="vehicleRelatedDriver"
                            label={
                                <Space size={8}>
                                    {i18n.t('name', '车辆关联司机数')}
                                    <StarryAbroadIcon>
                                        <Tooltip
                                            title={() => (
                                                <div>
                                                    {i18n.t(
                                                        'message',
                                                        '单车辆最多关联司机数可限制，开启和关闭功能不会影响已录入数据',
                                                    )}
                                                </div>
                                            )}
                                            className="question-icon"
                                            placement="right"
                                        >
                                            <IconInformation className="default-icon-information" />
                                        </Tooltip>
                                    </StarryAbroadIcon>
                                </Space>
                            }
                            rules={[{ required: true }]}
                            initialValue={-1}
                            className="single-content-item"
                        >
                            <VehicleNums disabled={!isEdit} />
                        </Item>
                    )}
                </div>
            </div>
        );
    };
    // 动态识别司机main-content展示的内容
    const dynamicIdentifyContent = () => {
        // 使用从组件顶层计算的列数
        const colSpan = getColSpan();

        return (
            <div className="main-content-container">
                <Item style={{ marginBottom: 0 }} required name='dynamic-identify' label={i18n.t('name', '动态识别司机方式')}>
                    <div className="tabs-header">
                        <Row gutter={[16, 16]}>
                            {tabs.map((item) => (
                                <Col span={colSpan} key={item.value}>
                                    {rectTab(item.text, item.value, item.value === platOrDevice)}
                                </Col>
                            ))}
                        </Row>
                    </div>
                    <Tabs className="main-content-container-tabs" activeKey={platOrDevice}>
                        <TabPane key="plat">
                            {platOrDevice === 'plat' && faceAbnormal}
                            {advancedSetting}
                        </TabPane>
                        <TabPane key="device">{advancedSetting}</TabPane>
                        <TabPane key="button">{advancedSetting}</TabPane>
                        <TabPane key="card">{advancedSetting}</TabPane>
                    </Tabs>
                </Item>
            </div>
        );
    };
    // 指定范围司机驾驶main-content展示的内容
    const rangeIdentifyContent = () => {
        return (
            <div>
                {singleIdentifyContent()}
                {dynamicIdentifyContent()}
            </div>
        );
    };

    return (
        <Container className="driver-discern-wraper-container">
            <div className="app-manage-driver-discern-wraper">
                <Form
                    form={form}
                    // labelCol={{ span: 6 }}
                    layout="vertical"
                    preserve={false}
                >
                    <div className="driver-identify">
                        <div className="left">
                            <Item name="driverIdentify">
                                <Space size={0}>
                                    <div className="title">{i18n.t('name', '司机识别')}</div>
                                    <StarryAbroadIcon>
                                        <Tooltip
                                            title={() => (
                                                <div>
                                                    {i18n.t(
                                                        'message',
                                                        '开启或关闭车辆的司机身份识别',
                                                    )}
                                                </div>
                                            )}
                                            className="question-icon"
                                            placement="right"
                                        >
                                            <IconInformation
                                                style={{ marginLeft: '8px' }}
                                                className="default-icon-information"
                                            />
                                        </Tooltip>
                                    </StarryAbroadIcon>
                                    <Switch
                                        disabled={!isEdit}
                                        className="switch"
                                        onChange={(checked) => switchChange(checked, 'driverIdentify')}
                                        checked={openIdentify}
                                    />
                                </Space>
                            </Item>
                        </div>
                        <div className="right">{operationDom()}</div>
                    </div>
                    {openIdentify && (
                        <div className="choose-identify-way">
                            <Item
                                rules={[{ required: true }]}
                                label={
                                    <>
                                        <div>
                                            {i18n.t('name', '司机驾驶识别范围')}
                                        </div>
                                        <StarryAbroadIcon>
                                            <Tooltip
                                                title={() => (
                                                    <div className="title-driver-popoer">
                                                        {i18n.t(
                                                            'message',
                                                            '指定车辆对比识别表示车辆上报的司机信息与车辆关联的司机进行对比识别；指定车组对比识别表示车辆上报的司机信息与本车组所有司机进行对比识别；任意车辆动态识别表示车辆上报的司机信息与平台任意司机进行对比识别',
                                                        )}
                                                    </div>
                                                )}
                                                overlayClassName="title-driver-popoer"
                                                className="question-icon"
                                                placement="right"
                                            >
                                                <IconInformation
                                                    style={{ marginLeft: '8px' }}
                                                    className="default-icon-information"
                                                />
                                            </Tooltip>
                                        </StarryAbroadIcon>
                                    </>
                                }
                            >
                                <Segmented
                                    onChange={driverIdentifyChange}
                                    value={driverIdentifyWay}
                                    disabled={!isEdit}
                                    options={[
                                        {
                                            label: i18n.t(
                                                'name',
                                                '指定车辆对比识别',
                                            ),
                                            value: 'singleDriver',
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '指定车组对比识别',
                                            ),
                                            value: 'rangeDriver',
                                        },
                                        {
                                            label: i18n.t(
                                                'name',
                                                '任意车辆动态识别',
                                            ),
                                            value: 'dynamicDriver',
                                        },
                                    ]}
                                />
                            </Item>
                            <div className="main-content">
                                {driverIdentifyWay === 'singleDriver' && singleIdentifyContent()}
                                {driverIdentifyWay === 'dynamicDriver' && dynamicIdentifyContent()}
                                {driverIdentifyWay === 'rangeDriver' && rangeIdentifyContent()}
                            </div>
                        </div>
                    )}
                </Form>
            </div>
        </Container>
    );
};
