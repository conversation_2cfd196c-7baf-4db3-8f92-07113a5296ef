@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.app-manage {
    &-wrapper {
        display: flex;
        gap: 24px;
        min-height: 250px;
        &>div>div:first-child{
            gap: 24px;
        }
        .app-manage-wrapper-tab-container::abroad{
            height: 100%;
        }
    }
    &-vertical-tab-wrapper {
        box-sizing: content-box;
        width: 200px;
        // height: calc(100% - 24px);
        height: 100%;
        min-height: 200px;
        &::abroad{
            width: 240px;
        }
        .poppy-tabs {
            height: 100%;
            .poppy-tabs-nav {
                .poppy-tabs-nav-list {
                    .poppy-tabs-tab {
                        padding: 2px 17px;
                        .tab-pane-custom {
                            // 200-34
                            width: 166px;
                            text-align: left;
                        }
                    }
                }
            }
            // 重写边框线
            .poppy-tabs-content-holder {
                border-right: solid 2px @starry-border-level-1-color;
            }
        }
    }
    &-content-panel-wrapper {
        flex: 1;
        // width: 50%;
    }
}

.app-manage-wrapper {
    .app-manage-wrapper-tab-container{
        height: 100%;
    }
    .switch-container {
        margin: 0;
        font-weight: 700;
        font-size: 20px;
        color: @starry-text-color-primary;
        line-height: 28px;
        margin-bottom: 16px;
        .switch-icon {
            margin-left: 8px;
            font-size: 16px;
            &::abroad {
                font-size: 20px;
            }
        }
    }
}

.app-manage-wrapper-drawer {
    height: 100%;
    .app-manage-vertical-tab-wrapper{
        height: 100%;
        .app-manage-wrapper-tab-container{
            height: 100%;
            &::abroad{
                padding: 0;
                box-shadow: none;
            }
        }
        .poppy-tabs{
            height: 100%;
            .poppy-tabs-nav{
                border-left: 1px solid var(--poppy-border-color-split);
                margin-bottom: 0;
            }
            .poppy-tabs-content-holder{
                display: none;
            }
        }
    }
}