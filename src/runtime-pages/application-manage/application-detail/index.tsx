import { Router<PERSON>rompt, StarryBreadcrumb, StarryCard, StarryModal, useSystemComponentStyle } from '@base-app/runtime-lib'; // 使用公共组件

import StarryTabs from '@/components/StarryTabs';
import type { PageBase, PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getCustomJsx } from '@/utils/pageReuse';
import { Auth, getAppGlobalData, i18n } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { Button, message, Tabs } from '@streamax/poppy';
import { IconRequestFill } from '@streamax/poppy-icons';
import classnames from 'classnames';
import { useEffect, useState } from 'react';
import {
    fetchApplicationDetail,
    lockApplication,
    unlockApplication,
} from '../../../service/application';
import AppConfig from './components/AppConfig';
import AppInfo from './components/AppInfo';
import AppMenu from './components/AppMenu';
import AppPage from './components/AppPage';
import './index.less';
/****定制项***/
export type ApplicationDetailShareProps = PageTabs & PageBase;
const { TabPane } = Tabs;

const AddApplication = (props: ApplicationDetailShareProps) => {
    /**定制项 */
    const { getPageTabs, children } = props;
    /**end */
    const history = useHistory();
    // @ts-ignore
    const { isAbroadStyle } = useSystemComponentStyle();
    const { location } = history;
    const { tab, appName } = location?.query;
    const { pathname } = location;
    const [activeKey, setActiveKey] = useState(tab || 'info');
    const [when, setWhen] = useState(false);
    const [whenConfig, setWhenConfig] = useState({
        company: false,
        vehicle: false,
        driver: false,
        driverIdentify: false,
    });

    let appId =
        (pathname?.split('/').length && pathname?.split('/')[pathname?.split('/').length - 1]) ||
        String(getAppGlobalData('APP_ID'));
    if (isNaN(appId)) {
        appId = String(getAppGlobalData('APP_ID'));
    }

    if (appId == ':appId') {
        appId = String(getAppGlobalData('APP_ID'));
    }
    const [appDetails, setAppDetails] = useState<any>({});

    useEffect(() => {
        if (appId) {
            getAppDetails();
        }
    }, []);
    const openIsWhen = (flag: boolean) => {
        setWhen(flag);
    };
    const openIsWhenKey = (key: string, flag: boolean) => {
        // company vehicle driver staticRelation dynamicRelation
        const config = { ...whenConfig, [key]: flag };
        setWhenConfig(config);
    };
    const returnWhenConfig = () => {
        const Flag = !!(
            whenConfig.company ||
            whenConfig.vehicle ||
            whenConfig.driver ||
            whenConfig.driverIdentify
        );
        return Flag;
    };
    function getAppDetails() {
        fetchApplicationDetail({
            appId,
        }).then((res: any) => {
            setAppDetails(res || {});
        });
    }

    function tabsChange(val: any) {
        if (activeKey === 'applicationConfig') {
            if (returnWhenConfig() || when) {
                // 如果当前标签页是applicationConfig且有未保存的更改
                StarryModal.confirm({
                    centered: true,
                    title: i18n.t('name', '提示'),
                    content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                    onOk() {
                        if (appId) getAppDetails();
                        setWhenConfig({
                            company: false,
                            vehicle: false,
                            driver: false,
                            driverIdentify: false,
                        });
                        setWhen(false);
                        setActiveKey(val);
                    },
                    onCancel() {
                        setActiveKey(activeKey);
                    },
                });
            } else {
                setActiveKey(val);
                history.replace({
                    // @ts-ignore
                    query: { tab: val, appName },
                });
            }
        } else {
            if (when) {
                StarryModal.confirm({
                    centered: true,
                    title: i18n.t('name', '提示'),
                    content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                    onOk() {
                        if (appId) getAppDetails();
                        setWhen(false);
                        setActiveKey(val);
                    },
                    onCancel() {
                        setWhen(true);
                        setActiveKey(activeKey);
                    },
                });
            } else {
                setActiveKey(val);
                history.replace({
                    // @ts-ignore
                    query: { tab: val, appName },
                });
            }
        }
    }

    // 启用停用
    function lockOrUnlock() {
        const { state, applicationName, applicationId } = appDetails;
        StarryModal.confirm({
            centered: true,
            title: state === 1 ? i18n.t('name', '停用确认') : i18n.t('name', '启用确认'),
            content:
                state === 1
                    ? i18n.t('message', '确认要停用“{applicationName}”吗？', {
                        applicationName: i18n.t(`@i18n:@app__${applicationId}`, applicationName),
                    })
                    : i18n.t('message', '确认要启用“{applicationName}”吗？', {
                        applicationName: i18n.t(`@i18n:@app__${applicationId}`, applicationName),
                    }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                if (state === 2) {
                    unlockApplication({
                        appIds: applicationId.toString(),
                        logParams: [{ data: applicationName }],
                    })
                        .then(() => {
                            message.success(i18n.t('message', '操作成功'));
                            getAppDetails();
                        })
                        .catch(() => {
                            message.error(i18n.t('message', '操作失败'));
                        });
                } else {
                    lockApplication({
                        appIds: applicationId.toString(),
                        logParams: [{ data: applicationName }],
                    })
                        .then(() => {
                            message.success(i18n.t('message', '操作成功'));
                            getAppDetails();
                        })
                        .catch(() => {
                            // message.error(i18n.t('message', '操作失败'));
                        });
                }
            },
        });
    }
    // 应用信息
    const renderApplicationInfo = () => {
        return Auth.check('@base:@page:application.manage:detail@action:tab.application.detail') ? (
            <TabPane tab={i18n.t('name', '应用信息')} key="info">
                <AppInfo appId={appId} appInfo={appDetails} />
            </TabPane>
        ) : null;
    };
    // 应用菜单
    const renderApplicationMenu = () => {
        return Auth.check('@base:@page:application.manage:detail@action:tab.application.menu') ? (
            <TabPane tab={i18n.t('name', '应用菜单')} key="menu">
                <AppMenu appId={appId} />
            </TabPane>
        ) : null;
    };
    // 应用页面
    const renderApplicationPage = () => {
        return Auth.check('@base:@page:application.manage:detail@action:tab.application.page') ? (
            <TabPane tab={i18n.t('name', '应用页面')} key="page">
                <AppPage appId={appId} />
            </TabPane>
        ) : null;
    };
    // 应用配置
    const renderApplicationConfig = () => {
        return Auth.check('@base:@page:application.manage:detail@action:tab.application.appconfig') ? (
            <TabPane tab={i18n.t('name', '应用配置')} key="applicationConfig">
                <AppConfig appId={appId} appDetails={appDetails} openIsWhen={openIsWhen} openIsWhenKey={openIsWhenKey} when={when} whenConfig={whenConfig}/>
            </TabPane>
        ) : null;
    };
    const applicationTabs = [
        renderApplicationInfo(),
        renderApplicationMenu(),
        renderApplicationPage(),
        renderApplicationConfig(),
    ];
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when || returnWhenConfig()}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
                isIncludeSearch={false}
            />
            <StarryCard
                title={
                    <div>
                        {i18n.t(
                            `@i18n:@app__${appDetails.applicationId}`,
                            appDetails.applicationName || appName,
                        )}
                        {appDetails.state && (
                            <span
                                className={classnames('app-details-title-sign', {
                                    success: appDetails.state === 1,
                                    normal: appDetails.state === 3,
                                    error: appDetails.state === 2,
                                })}
                            >
                                {appDetails.state === 1 && i18n.t('state', '启用')}
                                {appDetails.state === 2 && i18n.t('state', '停用')}
                                {appDetails.state === 3 && i18n.t('state', '到期')}
                            </span>
                        )}
                    </div>
                }
                operation={!Number(getAppGlobalData('APP_ID')) && <Auth code="@base:@page:application.manage:page.detail@action:enable.disenable">
                    <>
                        {appDetails.state === 2 && (
                            <Button onClick={() => lockOrUnlock()}>
                                {i18n.t('action', '启用')}
                            </Button>
                        )}
                        {appDetails.state === 1 && (
                            <Button onClick={() => lockOrUnlock()}>
                                {i18n.t('action', '停用')}
                            </Button>
                        )}
                    </>
                </Auth>}
            >
                <StarryTabs
                    hiddenLine={isAbroadStyle}
                    onChange={tabsChange}
                    activeKey={activeKey}
                    controlledByParent={true} // 添加控制参数，让标签页切换由父组件控制
                >
                    {getCustomJsx(getPageTabs, applicationTabs, appDetails)}
                </StarryTabs>
                {children}
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC<ApplicationDetailShareProps, any>(AddApplication);
