// 将一个对象的所有属性名和值进行排序，然后转为字符串
export const objOrderToString = (obj: Record<string, any>) => {
    const keySort = Object.keys(obj).sort((a: any, b: any) => a.localeCompare(b));
    const result = {};
    keySort.forEach((key) => {
        let value = obj[key];
        const valueType = typeof value;
        if (Object.prototype.toString.call(valueType) === '[object Array]') {
            value = value.sort((a: any, b: any) => (a - b ? 1 : -1));
        }
        result[key] = value;
    });
    return JSON.stringify(result);
};
// 对同配置的报警类型分组
export const formatAlarmTypeGroup = (
    list: Record<'label' | 'value', string>[] = [],
    settingMap: Record<string, any>,
) => {
    // 将设置数据转为数组，并把其所有设置的配置项转为字符串
    const settingList = [];
    for (const key in settingMap) {
        const value = settingMap[key];
        const alarmType = list.find((item) => item.value == key);
        if (alarmType) {
            settingList.push({
                ...alarmType,
                alarmType: key,
                config: objOrderToString(value),
            });
        }
    }
    // 将相同配置的分类归为同一分组
    const alarmTypesGroup: any[] = [];
    settingList.forEach((item) => {
        const { config, label, value } = item;
        let index = -1;
        const info = alarmTypesGroup.find((atg) => {
            index++;
            return atg.config === item.config;
        });
        // 如果已有此分组，则加入到此分组下
        if (info) {
            alarmTypesGroup[index].alarmTypeList.push({
                label,
                value,
            });
            alarmTypesGroup[index].alarmTypes.push(value);
        } else {
            alarmTypesGroup.push({
                config: config,
                alarmTypes: [value],
                alarmTypeList: [
                    {
                        label,
                        value,
                    },
                ],
            });
        }
    });
    // 默认设置
    const defaultSettingTypes = list.filter((at) => !settingMap[at.value]);
    const result = [...alarmTypesGroup, ...defaultSettingTypes];
    // 默认选择第一个分组的第一项
    const firstType = alarmTypesGroup[0]?.alarmTypeList?.[0]?.value;
    const defaultFirstType = defaultSettingTypes[0]?.value;
    return {
        currentType: firstType ?? defaultFirstType,
        currentTypeInGroup: firstType !== undefined,
        groupData: result,
    };
};
// 证据回传设置剔除脏数据
export const filterEvidenceParamData = (data: Record<string, any>) => {
    Object.keys(data).forEach((item) => {
        if (data[item].mediaSwitch == false) {
            const keyNames = ['mediaSwitch', 'streamType', 'mediaChannels', 'beginAt', 'endAt', 'aiFrameSwitch'];
            keyNames.forEach((key: string) => {
                Reflect.deleteProperty(data[item], key);
            });
        }
        if (data[item].needScreenSnap == false) {
            const keyNames = ['needScreenSnap', 'picChannels'];
            keyNames.forEach((key: string) => {
                Reflect.deleteProperty(data[item], key);
            });
        }
        if (data[item].blackBoxSwitch == false) {
            Reflect.deleteProperty(data[item], 'blackBoxSwitch');
        }
    });
    return data;
};
// 报警联动设置剔除脏数据
export const filterAlarmLinkageParamData = (data: Record<string, any>) => {
    Object.keys(data).forEach((item) => {
        if (data[item].mailSend == false) {
            const keyNames = [
                'mailSend',
                'sendTime',
                'sendType',
                'dayTime',
                'weekDay',
                'weekTime',
                'monthDay',
                'monthTime',
                'mailTemplate',
                'defaultLanguage',
                'emailAddress',
                'mailReceiver',
            ];
            keyNames.forEach((key: string) => {
                Reflect.deleteProperty(data[item], key);
            });
        }
        if (data[item].messageSend == false) {
            const keyNames = [
                'messageSend',
                'messageType',
                'sendTextContent',
                'displayTimeType',
                'displayTime',
                'ttsFlag',
                'audioContent',
            ];
            keyNames.forEach((key: string) => {
                Reflect.deleteProperty(data[item], key);
            });
        }
        if (data[item].messagePush == false) {
            const keyNames = ['messagePush', 'popType', 'messageTemplate', 'messageReceiver', 'messageUserReceiver'];
            keyNames.forEach((key: string) => {
                Reflect.deleteProperty(data[item], key);
            });
        }
    });
    return data;
};
