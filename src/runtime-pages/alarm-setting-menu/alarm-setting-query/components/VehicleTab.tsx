import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { Input, Select, Form, Space } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import {
    i18n,
    getAppGlobalData,
    useUrlSearchStore,
    utils,
} from '@base-app/runtime-lib';
// @ts-ignore
import { StarryTable, FleetTreeSelect } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { useState, useEffect, useRef } from 'react';
import { getCompanyByPage } from '../../../../service/company';
import type { ParamsType, TabItemProps } from '../index';
import { useAppSelect } from '@/hooks/useAppSelect';
import { vehicleEfficientStrategy } from '@/service/strategy';
import type {
    ListPageTableBase,
    Instances,
    ListPageQueryForm,
} from '@/types/pageReuse/pageReuseBase';
import { STRATEGY_TYPE } from '@/utils/constant';
import {
    getCustomItems,
    runCustomFun,
    getCustomJsx,
    getTableIconBtns,
} from '@/utils/pageReuse';
/**列表页table定制复用 */
export type alarmSettingQueryVehicleProps = ListPageTableBase &
    Instances &
    ListPageQueryForm;
/**end */

const VehicleTab = (props: TabItemProps & alarmSettingQueryVehicleProps) => {
    /**定制项 */
    const {
        getColumns,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getInstances,
        getQueryForm,
        getColumnSetting,
    } = props;
    /**end */
    const { inSaaS, appList, tabKey, strategyTypeToName } = props;
    const { disableProps, onSelectChange } = useAppSelect(inSaaS);
    const [appId, setAppId] = useState<string | number | undefined>('');
    const [form] = Form.useForm<any>();
    const containerRef = useRef<any>();
    const searchStore = useUrlSearchStore();
    const history: any = useHistory();
    const FleetTreeSelectRef = useRef<any>();
    // 定义变量动态设置 StarryTable 的 fetchDataAfterMount 属性的值，从详情页回退时设置为true,切换Tab页时设置为false
    let fetchDataFlag;
    if (history.action === 'POP') {
        fetchDataFlag = true;
    }

    useEffect(() => {
        if (!appList || appList.length === 0) return;
        const appId = appList?.filter((p: any) => p.value != 0)[0]?.value;
        setAppId(appId);
        containerRef.current?.loadDataSource({
            appId,
            page: 1,
            pageSize: 20,
        });
        onSelectChange(appId);
    }, [appList]);

    const handleAppChange = (value: number) => {
        setAppId(value);
        form.setFieldsValue({
            ...form.getFieldsValue(),
            fleetId: { fleetIds: undefined, includeSubFleet: 1 },
        });
        onSelectChange(value);
    };

    useEffect(() => {
        (appId || appId == '0') &&
            FleetTreeSelectRef.current?.loadData({ appId });
        if (!inSaaS && (appId || appId == '0')) {
            containerRef.current?.loadDataSource({
                appId,
                page: 1,
                pageSize: 20,
            });
        }
    }, [appId]);

    const fetchFleet = async () => {
        if (appId) {
            const { list } = await getCompanyByPage({
                page: 1,
                pageSize: 1e8,
                appId,
                // @ts-ignore
                ...utils.general.getSortParam(),
            });
            return list.map((i: any) => {
                return {
                    key: i['fleetId'],
                    id: i['fleetId'],
                    title: i['fleetName'],
                    parentId: i['parentId'],
                    value: i['fleetId'],
                };
            });
        }
        return [];
    };

    const vehicleForm: QueryFormProps['items'] | any[] = [
        inSaaS && {
            label: i18n.t('name', '归属应用'),
            name: 'appId',
            field: Select,
            fieldProps: {
                options: appList?.filter((p: any) => p.value != 0),
                placeholder: i18n.t('message', '请选择归属应用'),
                onChange: (value: number) => handleAppChange(value),
            },
            itemProps: {
                initialValue: appList?.filter((p: any) => p.value != 0)?.[0]
                    ?.value,
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'fleetId',
            field: FleetTreeSelect,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择车组'),
                treeNodeFilterProp: 'title',
                ref: FleetTreeSelectRef,
                getDataSource: fetchFleet,
            },
            itemProps: {
                dependencies: inSaaS ? ['appId'] : [],
            },
        },
        {
            label: i18n.t('name', '车辆'),
            name: 'vehicleNumber',
            // colSize: 2,
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入车牌号码'),
                maxLength: 50,
            },
            itemProps: {
                rules: [{ max: 50, type: 'string' }],
            },
        },
    ].filter((item) => item);
    const queryItems: QueryFormProps['items'] | any[] = getCustomItems(
        getQueryForm,
        vehicleForm,
        undefined,
    );
    const handleRender = ({
        list,
        type,
        params,
    }: {
        list: any;
        type: number;
        params: any;
    }) => {
        const strategyItem = list.find((p: any) => p.configureType == type);
        if (!strategyItem) return '-';
        const { limited, configureId, configureName } = strategyItem;
        return (
            strategyTypeToName?.({
                limited,
                configureId,
                configureName,
                type,
                appId,
                ...params,
            }) || '-'
        );
    };

    const vehicleColumns: ColumnsType = [
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '报警通知设置'),
            dataIndex: 'alarmLinkageConfigureName',
            ellipsis: true,
            render: (text: string, record: any) =>
                handleRender({
                    list: record.effectiveConfigureResDTOList,
                    type: STRATEGY_TYPE.ALARM_LINKAGE,
                    params: {
                        prefix: 'alarmLinkageStrategy',
                        path: '/strategy/alarm-linkage/detail',
                        defaultDetailPath:
                            '/strategy/default/alarm-linkage/detail',
                        authCode:
                            '@base:@page:alarm.setting.query@action:default.alarm.linkage',
                        moreAuthCode:
                            '@base:@page:alarm.setting.query@action:custom.alarm.linkage',
                    },
                }),
        },
        {
            title: i18n.t('name', '证据上传设置'),
            dataIndex: 'evidenceConfigureName',
            ellipsis: true,
            render: (text: string, record: any) =>
                handleRender({
                    list: record.effectiveConfigureResDTOList,
                    type: STRATEGY_TYPE.EVIDENCE,
                    params: {
                        prefix: 'evidenceReturnStrategy',
                        path: '/strategy/evidence/detail',
                        defaultDetailPath: '/strategy/default/evidence/detail',
                        authCode:
                            '@base:@page:alarm.setting.query@action:default.evidenceupload',
                        moreAuthCode:
                            '@base:@page:alarm.setting.query@action:custom.evidenceupload',
                    },
                }),
        },
        {
            title: i18n.t('name', '报警处理设置'),
            dataIndex: 'autoHandleConfigureName',
            ellipsis: true,
            render: (text: string, record: any) =>
                handleRender({
                    list: record.effectiveConfigureResDTOList,
                    type: STRATEGY_TYPE.AUTO_HANDLE,
                    params: {
                        prefix: 'alarmWebResponseStrategy',
                        path: '/strategy/auto-handle/detail',
                        defaultDetailPath:
                            '/strategy/default/auto-handle/detail',
                        authCode:
                            '@base:@page:alarm.setting.query@action:default.auto.handle',
                        moreAuthCode:
                            '@base:@page:alarm.setting.query@action:custom.auto.handle',
                    },
                }),
        },
    ];

    useEffect(() => {
        if (!inSaaS) {
            fetchDataFlag = false;
            setAppId(getAppGlobalData('APP_ID'));
        }
    }, []);

    useEffect(() => {
        const { appId, fleetId, vehicleNumber, includeSubFleet } =
            searchStore.get();
        if (appId) {
            setAppId(appId);
        }
        if (history.action === 'POP') {
            form.setFieldsValue({
                appId: appId ? Number(appId) : null,
                vehicleNumber: vehicleNumber ? decodeURIComponent(
                    String(vehicleNumber),
                ) : null,
                fleetId: {
                    fleetIds: fleetId,
                    includeSubFleet: includeSubFleet ?? 1,
                },
            });
        }
    }, []);

    // 重置设置appId的初始值
    const handleReset = () => {
        let { appId: newAppId } = form.getFieldsValue();
        if (!inSaaS) {
            newAppId = Number(getAppGlobalData('APP_ID'));
        }
        setAppId(newAppId);
        return true;
    };

    const fetchPage = (params: ParamsType = { pageSize: 20, page: 1 }) => {
        if (!inSaaS) {
            params.appId = params.appId || Number(appId) || 0;
        }
        if (injectSearchList) {
            return injectSearchList(params);
        }
        if (appId && params.appId) {
            if (params.fleetId) {
                params.includeSubFleet = (
                    params.fleetId as any
                )?.includeSubFleet;
                params.fleetId = (params.fleetId as any).fleetIds;
            }
            if (params.vehicleNumber) {
                params.vehicleNumber = encodeURIComponent(
                    params.vehicleNumber.trim(),
                );
                params.decode = 1;
            }

            searchStore.set({
                ...params,
                activeTab: tabKey,
            });
            return vehicleEfficientStrategy({
                ...params,
            }).then((rs: any) => {
                return {
                    list: rs.list,
                    total: rs.total,
                };
            });
        }
        // 无应用id即为重置(中台)
        return Promise.resolve({
            list: [],
            total: 0,
        });
    };
    const { page, pageSize } = searchStore.get();

    runCustomFun(getInstances, {
        form,
        table: containerRef.current,
    });
    return (
        <StarryTable
            aroundBordered
            fetchDataFunc={fetchPage}
            fetchDataAfterMount={fetchDataFlag}
            queryProps={{
                items: queryItems,
                onReset: handleReset,
                form,
                ...disableProps,
            }}
            toolbar={{
                iconBtns: getTableIconBtns(getIconBtns, [
                    'reload',
                    'column-setting',
                ]),
                leftRender: () => (
                    <Space>{getCustomJsx(getTableLeftRender, [])}</Space>
                ),
                columnSetting: {
                    disabledKeys: ['vehicleNumber'],
                    storageKey: '@base:@page:alarm.setting.query.table.vehicle',
                    ...getColumnSetting?.(),
                },
            }}
            ref={containerRef}
            columns={getCustomItems(getColumns, vehicleColumns)}
            pagination={{
                defaultCurrent: Number(page) || 1,
                defaultPageSize: Number(pageSize) || 20,
            }}
        />
    );
};
export default withSharePropsHOC<TabItemProps, alarmSettingQueryVehicleProps>(
    VehicleTab,
);
