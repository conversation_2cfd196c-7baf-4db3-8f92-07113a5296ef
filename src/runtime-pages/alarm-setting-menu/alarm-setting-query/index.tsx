import {
    i18n,
    useUrlSearchStore,
    Auth,
    getAppGlobalData,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { Tabs } from '@streamax/poppy';
// @ts-ignore
import { StarryBreadcrumb, StarryCard, Action } from '@base-app/runtime-lib';
import { useAppList } from '../../../hooks';
import VehicleTab from './components/VehicleTab';
import './index.less';
import { STRATEGY_QUERY_LIMIT, STRATEGY_TYPE } from '@/utils/constant';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import {
    PageBase,
    ListPageTableBase,
    Instances,
} from '@/types/pageReuse/pageReuseBase';
import classNames from 'classnames';

const { TabPane } = Tabs;
/**列表页table定制复用 */
export type alarmSettingQueryShareProps = PageBase &
    ListPageTableBase &
    Instances;
/**end */
type PrefixType =
    | 'userStrategy'
    | 'alarmLinkageStrategy'
    | 'evidenceReturnStrategy'
    | 'emailSendingStrategy'
    | 'faceComparisonStrategy'
    | 'dataCleanStrategy'
    | 'autoUploadStrategy'
    | 'alarmWebResponseStrategy'
    | 'pictureCaptureStrategy';
export interface ParamsType {
    page: number;
    pageSize: number;
    appId?: number;
    fleetId: { fleetIds: string; includeSubFleet: number } | string;
    vehicleNumber?: string;
    fleetName?: string;
    deviceNo?: string;
    userName?: string;
    includeSubFleet: number;
    decode?: 0 | 1;
}

export interface TabItemProps {
    configureNameRender?: (
        text: string,
        record: any,
        prefix: PrefixType,
        key: string,
        route: string,
        param?: number,
    ) => React.ReactNode;
    strategyTypeToName?: (params: any) => React.ReactNode;
    appList?: any[];
    inSaaS?: boolean;
    tabKey: string;
}
const { EVIDENCE, ALARM_LINKAGE, AUTO_HANDLE, MAIL_SEND, USER } = STRATEGY_TYPE;
const ValidatedCode = {
    // 证据回传
    [EVIDENCE]: {
        pageCode: '@base:@page:setting.default.evidence',
        tabCode: '@base:@page:setting.default.config@action:tab.evidence',
    },
    // 报警联动
    [ALARM_LINKAGE]: {
        pageCode: '@base:@page:setting.default.alarm.linkage',
        tabCode: '@base:@page:setting.default.config@action:tab.alarm.linkage',
    },
    // 自动处理
    [AUTO_HANDLE]: {
        pageCode: '@base:@page:setting.default.auto.handle',
        tabCode: '@base:@page:setting.default.config@action:tab.auto.handle',
    },
    // 邮件发送
    [MAIL_SEND]: {
        pageCode: '@base:@page:setting.default.email.sending',
        tabCode: '@base:@page:setting.default.config@action:tab.email.sending',
    },
};

const AlarmSettingQuery = (props: alarmSettingQueryShareProps) => {
    /**定制项 */
    const { children } = props;
    /**end */
    const history: any = useHistory();
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const searchStore = useUrlSearchStore();
    const { isAbroadStyle } = useSystemComponentStyle();
    const strategyTypeToName = ({
        limited,
        configureId,
        configureName,
        type,
        prefix,
        path,
        defaultDetailPath,
        authCode,
        moreAuthCode,
        appId,
    }: {
        limited: number;
        configureId: number;
        configureName: string;
        type: number;
        prefix: string;
        path: string;
        defaultDetailPath: string;
        authCode: string;
        moreAuthCode: string;
        appId: string;
    }) => {
        if (!configureId) return '-';
        const strategyName = i18n.t(
            `@i18n:@${prefix}__${configureId}`,
            configureName,
        );
        const { UNLIMITED, DEFAULT, LIMITED } = STRATEGY_QUERY_LIMIT;
        let jumpable = false;
        switch (limited) {
            case LIMITED:
                jumpable = false;
                break;
            // 不受限，可跳转详情
            case UNLIMITED:
                jumpable = true;
                break;
            // 是默认设置，验证是否有页面权限，有则可跳转
            case DEFAULT:
                // 用户设置不走下面的判断，它没独立的默认设置页
                if (type === USER) {
                    jumpable = true;
                    break;
                }
                // 提取公共逻辑：检查权限
                const checkPermissions = () => {
                    const { actions = {}, pages = [] } = getAppGlobalData('APP_RESOURCE');
                    const currentPageAction = actions[authCode];
                    const hasPageAuth = !!pages.find(
                        (p: any) => p.resourceCode === ValidatedCode[type].pageCode,
                    );

                    // 如果没有操作权限，直接返回false
                    if (!currentPageAction) return false;

                    if (inSaaS) {
                        if (currentPageAction && currentPageAction.pageId !== null) {
                            // 操作有跳转配置，需要tab权限
                            return Auth.check(ValidatedCode[type].tabCode);
                        }
                        // 操作无跳转配置，有页面权限即可
                        return hasPageAuth;
                    } else {
                        // 非SaaS环境：有操作权限和页面权限即可
                        return  !!currentPageAction && hasPageAuth;
                    }
                };

                jumpable = checkPermissions();
                break;
        }
        const jumpFn = () => {
            if (jumpable) {
                // 默认用户设置也走普通设置的跳转逻辑
                if (limited === STRATEGY_QUERY_LIMIT.DEFAULT && type != USER) {
                    // 默认设置跳转
                    Action.openActionUrl({
                        code: authCode,
                        history,
                        url: defaultDetailPath,
                        params: {
                            // 若是跳往设置配置页的tab需要tabKey来定位
                            tabKey: String(type),
                            appId,
                        },
                    });
                } else if (type === USER) {
                    // 用户设置跳转
                    history.push({
                        pathname: path,
                        query: {
                            configureId,
                        },
                    });
                } else {
                    // 自定义设置跳转
                    Action.openActionUrl({
                        code: moreAuthCode,
                        history,
                        url: path,
                        params: {
                            // 若是跳往设置配置页的tab需要tabKey来定位
                            tabKey: String(type),
                            configureId,
                            appId,
                        },
                    });
                }
            }
        };
        return (
            <Auth
                code={limited === UNLIMITED ? moreAuthCode : authCode}
                fellback={strategyName}
            >
                <span
                    className={classNames({
                        link: jumpable,
                    })}
                    title={strategyName}
                    onClick={jumpFn}
                >
                    {strategyName}
                </span>
            </Auth>
        );
    };
    const { activeTab } = searchStore.get();
    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="strategy-query-container">
                    <Tabs
                        activeKey={activeTab || '1'}
                        hiddenLine={isAbroadStyle}
                    >
                        <TabPane key="1" tab={i18n.t('name', '车辆查询')}>
                            <VehicleTab
                                inSaaS={inSaaS}
                                appList={appList}
                                tabKey={'1'}
                                strategyTypeToName={strategyTypeToName}
                            />
                        </TabPane>
                    </Tabs>
                </div>
                {children}
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(AlarmSettingQuery);
