import { useState, useEffect } from 'react';
// @ts-ignore
import {StarryBreadcrumb, StarryCard, StarryModal, useSystemComponentStyle} from '@base-app/runtime-lib';
import { Select, Tabs, Empty, Spin, message } from '@streamax/poppy';
import {
    i18n,
    getAppGlobalData,
    Auth,
    RouterPrompt,
    useUrlSearchStore,
} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import { useAppList } from '@/hooks';
import Evidence from '@/modules/strategy/EvidenceStrategy';
import Linkage from '@/modules/strategy/LinkageStrategy';
import AutoHandle from '@/modules/strategy/AutoHandleStrategy';
import MailSend from '@/modules/strategy/MailSendStrategy';
import { getStrategyListByPage } from '@/service/strategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import './index.less';
import { IconDoublearrowRight } from '@streamax/poppy-icons';
import { useRequest } from '@streamax/hooks';
import { withShareRootHOC } from '@streamax/page-sharing-core';

interface TabItem {
    title: string;
    type: number;
    content: () => React.ReactNode;
}

const { TabPane } = Tabs;
const typeKey = {
    1: 'evidenceUpload', // 证据回传
    2: 'alarmLink', // 报警联动
    10: 'alarmHandle', // 自动处理
    3: 'emailSend', // 邮件发送
};
const LinkSetting = () => {
    // 处理上一次请求覆盖下一次请求
    const { runAsync } = useRequest(getStrategyListByPage, { manual: true });
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const [detailInfo, setDetailInfo] = useState<any>({
        evidenceUpload: {},
        alarmLink: {},
        alarmHandle: {},
        emailSend: {},
    });
    const [currentAppId, setCurrentAppId] = useState<number | undefined>(undefined);
    const [activeKey, setActiveKey] = useState(String(STRATEGY_TYPE.EVIDENCE));
    const location: any = useLocation();
    const [when, setWhen] = useState(false);
    const searchStore = useUrlSearchStore();
    const history = useHistory();
    const [loading, setLoading] = useState<boolean>(true);
    const [uploading, setUploading] = useState<boolean>(false);
    const { isAbroadStyle } = useSystemComponentStyle();
    const setSearchStore = (propName: string, value: string | number) => {
        searchStore.set({
            ...searchStore.get(),
            [propName]: value,
        });
    };

    useEffect(() => {
        if (history.action === 'POP') {
            const { activeTab } = searchStore.get();
            setActiveKey(activeTab);
        }
    }, [searchStore]);

    useEffect(() => {
        location.query?.tabKey && setActiveKey(location.query?.tabKey);
        if (history.action === 'PUSH') {
            setSearchStore('activeTab', STRATEGY_TYPE.EVIDENCE);
        }
    }, []);

    useEffect(() => {
        const { appId } = searchStore.get();
        if (appId) {
            setCurrentAppId(Number(appId));
        } else {
            if (!inSaaS) {
                setCurrentAppId(Number(getAppGlobalData('APP_ID')));
            } else {
                setCurrentAppId(appList?.[0]?.value);
            }
        }
    }, [appList]);

    useEffect(() => {
        getDetailInfo(activeKey);
    }, [currentAppId, activeKey]);

    const getDetailInfo = async (type: string | number) => {
        if (type) {
            setLoading(true);
            const rs = await runAsync({
                appId: currentAppId,
                configureType: Number(type),
                configureLevelType: 1,
                page: 1,
                pageSize: 1,
            });
            const currentTabDetail = typeKey[type];
            setDetailInfo({
                ...detailInfo,
                [currentTabDetail]: rs.list[0],
            });
            setLoading(false);
        }
    };
    const openIsWhen = (flag: boolean) => {
        setWhen(flag);
    };

    const selectChange = (value: any) => {
        // 如何还在上传音频需要提示
        if (uploading) {
            message.warn(i18n.t('message', '音频正在上传中，请稍等'));
            return;
        }
        if (when) {
            setWhen(false);
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setWhen(false);
                    setCurrentAppId(value);
                    setSearchStore('appId', value);
                },
                onCancel() {
                    setWhen(true);
                },
            });
        } else {
            setCurrentAppId(value);
            setSearchStore('appId', value);
        }
    };

    const tabChange = (_activeKey: string) => {
        if (when) {
            setWhen(false);
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setWhen(false);
                    setDetailInfo({
                        evidenceUpload: {},
                        alarmLink: {},
                        alarmHandle: {},
                        emailSend: {},
                    });
                    setActiveKey(_activeKey);
                    setSearchStore('activeTab', _activeKey);
                },
                onCancel() {
                    setWhen(true);
                    setActiveKey(activeKey);
                },
            });
        } else {
            setDetailInfo({
                evidenceUpload: {},
                alarmLink: {},
                alarmHandle: {},
                emailSend: {},
            });
            setActiveKey(_activeKey);
            setSearchStore('activeTab', _activeKey);
        }
    };

    const operations = (
        <div style={{ height: 46 }}>
            <Select
                style={{ width: 280 }}
                value={currentAppId}
                placeholder={i18n.t('message', '请选择归属应用')}
                options={appList}
                onChange={selectChange}
            />
        </div>
    );
    const goMore = (url: string) => {
        history.push(url, { choosedAppId: currentAppId || '' });
    };
    const renderMailSend = (type: number) => {
        if ((inSaaS && appList.length === 0)) {
            return (
                <div
                    style={{
                        height: '500px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Empty
                        imageStyle={{
                            height: 80,
                        }}
                    />
                </div>
            );
        } else {
            return (
                <MailSend
                    activeKey={activeKey}
                    appId={currentAppId as number}
                    type={type}
                    title={i18n.t('name', '邮件信息')}
                    data={detailInfo?.emailSend || {}}
                    updateData={getDetailInfo}
                    authCode={{
                        editCode:
                            '@base:@page:setting.default.config@action:tab.email.sending:edit',
                    }}
                    openIsWhen={openIsWhen}
                    detailLoading={loading}
                />
            );
        }
    };
    // 复制和编辑是同一个资源，避免产品后期调整，先暂时将复制的资源和编辑资源传相同参数，保留逻辑处理
    const tabItems = [
        Auth.check('@base:@page:setting.default.config@action:tab.evidence') && {
            title: i18n.t('name', '证据上传'),
            type: STRATEGY_TYPE.EVIDENCE,
            content: function () {
                return (
                    <div>
                        <div className="alarm-setting-link-setting-tab-header">
                            <span className="title">{i18n.t('name', '证据上传设置')}</span>
                            <Auth code="@base:@page:setting.default.config@action:tab.evidence:more">
                                <a onClick={() => goMore('/strategy/evidence')}>
                                    {i18n.t('action', '更多')}
                                    <IconDoublearrowRight />
                                </a>
                            </Auth>
                        </div>
                        <Evidence
                            activeKey={activeKey}
                            type={this.type}
                            title={i18n.t('name', '设置信息')}
                            data={detailInfo?.evidenceUpload || {}}
                            updateData={getDetailInfo}
                            authCode={{
                                editCode:
                                    '@base:@page:setting.default.config@action:tab.evidence:edit',
                                copyCode:
                                    '@base:@page:setting.default.config@action:tab.evidence:edit',
                            }}
                            openIsWhen={openIsWhen}
                            appId={currentAppId}
                        />
                    </div>
                );
            },
        },
        Auth.check('@base:@page:setting.default.config@action:tab.alarm.linkage') && {
            title: i18n.t('name', '报警通知'),
            type: STRATEGY_TYPE.ALARM_LINKAGE,
            content: function () {
                return (
                    <div>
                        <div className="alarm-setting-link-setting-tab-header">
                            <span className="title">{i18n.t('name', '报警通知设置')}</span>
                            <Auth code="@base:@page:setting.default.config@action:tab.alarm.linkage:more">
                                <a onClick={() => goMore('/strategy/alarm-linkage')}>
                                    {i18n.t('action', '更多')}
                                    <IconDoublearrowRight />
                                </a>
                            </Auth>
                        </div>
                        <Linkage
                            activeKey={activeKey}
                            appId={currentAppId as number}
                            onUploadStatus={(status) => setUploading(status)}
                            type={this.type}
                            title={i18n.t('name', '设置信息')}
                            data={detailInfo?.alarmLink || {}}
                            updateData={getDetailInfo}
                            authCode={{
                                editCode:
                                    '@base:@page:setting.default.config@action:tab.alarm.linkage:edit',
                            }}
                            openIsWhen={openIsWhen}
                            msgSendFormAuthCode={{
                                displayLocationAuthCode: '@base:@page:setting.alarm.linkage@action:display.location',
                                commonTextAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.text', // 常用文本管理操作资源
                                commonAudioAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.audio', // 常用音频管理操作资源
                                textMessageAuthCode: '@base:@page:setting.default.config@action:text.message', // 文本消息资源
                                audioMessageAuthCode: '@base:@page:setting.default.config@action:audio.message', // 音频消息资源
                                audioTransformAuthcode:'@base:@page:setting.default.alarm.linkage@action:text.to.speech'//文本转语音资源
                            }}
                        />
                    </div>
                );
            },
        },
        Auth.check('@base:@page:setting.default.config@action:tab.auto.handle') && {
            title: i18n.t('name', '报警处理'),
            type: STRATEGY_TYPE.AUTO_HANDLE,
            content: function () {
                return (
                    <div>
                        <div className="alarm-setting-link-setting-tab-header">
                            <span className="title">{i18n.t('name', '报警处理设置')}</span>
                            <Auth code="@base:@page:setting.default.config@action:tab.auto.handle:more">
                                <a onClick={() => goMore('/strategy/auto-handle')}>
                                    {i18n.t('action', '更多')}
                                    <IconDoublearrowRight />
                                </a>
                            </Auth>
                        </div>
                        <AutoHandle
                            activeKey={activeKey}
                            appId={currentAppId as number}
                            type={this.type}
                            title={i18n.t('name', '设置信息')}
                            data={detailInfo?.alarmHandle || {}}
                            updateData={getDetailInfo}
                            authCode={{
                                editCode:
                                    '@base:@page:setting.default.config@action:tab.auto.handle:edit',
                            }}
                            openIsWhen={openIsWhen}
                        />
                    </div>
                );
            },
        },
    ].filter((item) => item) as TabItem[];

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <Tabs
                    tabBarExtraContent={inSaaS ? operations : null}
                    onChange={tabChange}
                    activeKey={activeKey}
                    hiddenLine={isAbroadStyle}
                >
                    {tabItems.map((item) => (
                        <TabPane tab={item?.title} key={item?.type}>
                            {item?.content?.()}
                        </TabPane>
                    ))}
                </Tabs>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(LinkSetting);
