import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import Evidence from '@/modules/strategy/EvidenceStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';
import { withSharePropsHOC } from '@streamax/page-sharing-core';

const LinkSettingStrategyDetailPage = () => {
    // 复制和编辑是同一个资源，避免产品后期调整，先暂时将复制的资源和编辑资源传相同参数，保留逻辑处理
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.EVIDENCE}
            subComponent={
                // @ts-ignore
                <Evidence
                    title={i18n.t('name', '证据回传配置')}
                    authCode={{
                        editCode: '@base:@page:setting.default.evidence@action:edit',
                        copyCode: '@base:@page:setting.default.evidence@action:edit',
                    }}
                />
            }
        />
    );
};
export default withSharePropsHOC(LinkSettingStrategyDetailPage);
