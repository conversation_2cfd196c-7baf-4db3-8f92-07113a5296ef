import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import AutoHandle from '@/modules/strategy/AutoHandleStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';

export default () => {
    // 复制和编辑是同一个资源，避免产品后期调整，先暂时将复制的资源和编辑资源传相同参数，保留逻辑处理
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.AUTO_HANDLE}
            subComponent={
                // @ts-ignore
                <AutoHandle
                    title={i18n.t('name', '自动处理配置')}
                    authCode={{
                        editCode: '@base:@page:setting.default.auto.handle@action:edit',
                        copyCode: '@base:@page:setting.default.auto.handle@action:edit',
                    }}
                />
            }
        />
    );
};
