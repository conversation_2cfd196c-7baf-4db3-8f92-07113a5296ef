import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import Linkage from '@/modules/strategy/LinkageStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';

export default () => {
    // 复制和编辑是同一个资源，避免产品后期调整，先暂时将复制的资源和编辑资源传相同参数，保留逻辑处理
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.ALARM_LINKAGE}
            subComponent={
                // @ts-ignore
                <Linkage
                    title={i18n.t('name', '报警联动配置')}
                    authCode={{
                        editCode: '@base:@page:setting.default.alarm.linkage@action:edit',
                        copyCode: '@base:@page:setting.default.alarm.linkage@action:edit',
                    }}
                    msgSendFormAuthCode={{
                        displayLocationAuthCode: '@base:@page:setting.alarm.linkage@action:display.location',
                        commonAudioAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.text',
                        commonTextAuthCode: '@base:@page:setting.alarm.linkage:detail@action:common.audio',
                        textMessageAuthCode: '@base:@page:setting.default.config@action:text.message',
                        audioMessageAuthCode: '@base:@page:setting.default.config@action:audio.message'
                    }}
                />
            }
        />
    );
};
