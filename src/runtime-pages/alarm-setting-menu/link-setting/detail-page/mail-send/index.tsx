import StrategyDetailPage from '@/modules/strategy/StrategyDetailPage';
import MailSend from '@/modules/strategy/MailSendStrategy';
import { STRATEGY_TYPE } from '@/utils/constant';
import { i18n } from '@base-app/runtime-lib';

export default () => {
    return (
        <StrategyDetailPage
            type={STRATEGY_TYPE.MAIL_SEND}
            subComponent={
                // @ts-ignore
                <MailSend
                    title={i18n.t('name', '邮件配置')}
                    authCode={{
                        editCode: '@base:@page:setting.default.email.sending@action:edit',
                    }}
                />
            }
        />
    );
};
