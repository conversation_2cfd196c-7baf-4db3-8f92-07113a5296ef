/*
 * @LastEditTime: 2023-08-08 13:32:38
 */
import { useState, useEffect } from 'react';
import {Input, Dropdown, Form, Menu, Select, Space} from '@streamax/poppy';
import { i18n, useSystemComponentStyle } from '@base-app/runtime-lib';
import { MultipleSearchDropdownLabel } from '@base-app/runtime-lib';
const { useDropdownLabel } = MultipleSearchDropdownLabel;

type Type = 'vehicle' | 'device' | 'driver';

interface VehicleSearchInputProps {
    value?: any;
    searchOptions?: Type[];
    onChange?: (value: any) => void;
}

export default (props: VehicleSearchInputProps) => {
    const { searchOptions: propSearchOptions = ['vehicle', 'device'], onChange, value: propValue } = props;

    // 确保 searchOptions 不为空且只包含有效值
    const searchOptions = propSearchOptions?.filter(
        (option) => option === 'vehicle' || option === 'device' || option === 'driver'
    ) || [];

    // 如果过滤后为空数组，则使用默认值
    const validSearchOptions = searchOptions.length > 0
        ? searchOptions
        : ['vehicle', 'device'] as Type[];

    // 在组件中使用useDropdownLabel
    const { defaultDropDownMenu, fieldProps, updateSelectKey } = useDropdownLabel({
        menuOptions: [
            validSearchOptions.indexOf('vehicle') > -1 ? {
                label: i18n.t('name', '车牌号码'),
                value: 'vehicle',
                placeholder: i18n.t('message', '请输入车牌号码')
            } : null,
            validSearchOptions.indexOf('device') > -1 ? {
                label: i18n.t('name', '设备编号'),
                value: 'device',
                placeholder: i18n.t('message', '请输入设备编号')
            } : null,
            validSearchOptions.indexOf('driver') > -1 ? {
                label: i18n.t('name', '司机名称'),
                value: 'driver',
                placeholder: i18n.t('message', '请输入司机名称')
            } : null,
        ].filter(Boolean),
        onSelect: (newType) => {
            handleSelect(newType)
        },
        defaultKey: propValue?.type
    });

    const [current, setCurrent] = useState<Type>('vehicle');
    const [value, setValue] = useState<any>('');

    useEffect(() => {
        if (propValue && propValue.value) {
            updateSelectKey(propValue.type);
            setValue(propValue.value);
            handleSelect(propValue.type, propValue.value);
        }
    }, [JSON.stringify(propValue)]);

    const handleSelect = (type: Type, propValue?: string) => {
        setCurrent(type);
        onChange &&
            onChange?.({
                type: type,
                value: value || propValue,
            });
    };

    const handleChange = (e: any) => {
        const v = e.target.value;
        setValue(v);
        onChange &&
            onChange?.({
                type: current,
                value: v,
            });
    };

    return (
        <>
            <Space direction="vertical" style={{ width: '100%' }}>
                {defaultDropDownMenu}
                <Input
                    allowClear
                    value={value}
                    maxLength={50}
                    onChange={handleChange}
                    {...fieldProps}  // 使用hooks返回的表单属性,包含海外的下拉组件配置
                />
            </Space>
        </>
    );
};
