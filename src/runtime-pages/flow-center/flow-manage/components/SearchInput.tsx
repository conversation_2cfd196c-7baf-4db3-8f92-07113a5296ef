/*
 * @LastEditTime: 2025-02-06 15:04:37
 */
import { useEffect, useState } from 'react';
import { Input, Dropdown, Menu, Space, Select } from '@streamax/poppy';
import { i18n, useSystemComponentStyle } from '@base-app/runtime-lib';
import { DownOutlined } from '@ant-design/icons';
import "./SearchInput.less";
import { MultipleSearchDropdownLabel } from '@base-app/runtime-lib';
const { useDropdownLabel } = MultipleSearchDropdownLabel;

type SearchType = 'deviceNo' | 'vehicleNumber';

interface Value {
    searchType: SearchType;
    searchValue: string;
}

export interface SearchInputProps {
    value?: Value;
    onChange?: (value: Value) => void;
}

export default (props: SearchInputProps) => {
    const { onChange, value: propValue } = props;
    // 在组件中使用useDropdownLabel
    const { defaultDropDownMenu, fieldProps } = useDropdownLabel({
        menuOptions: [
            {
                label: i18n.t('name', '车牌号码'),
                value: 'vehicleNumber',
                placeholder: i18n.t('message', '请输入车牌号码')
            },
            {
                label: i18n.t('name', '设备编码'),
                value: 'deviceNo',
                placeholder: i18n.t('message', '请输入车辆设备')
            },
        ],
        onSelect: (newType) => {
            handleSelect(newType)
        },
        defaultKey: propValue?.searchType
    });

    const [searchValue, setSearchValue] = useState<any>();
    const [searchType, setSearchType] = useState<SearchType>('vehicleNumber');


    useEffect(() => {
        if (propValue && propValue.searchValue) {
            setSearchValue(propValue.searchValue);
        }
    }, []);

    const handleSelect = ( key : any) => {
        setSearchType(key);
        onChange?.({
            searchType: key,
            searchValue,
        });
    };

    const handleSearchValueChange = async (e: any) => {
        const value = e.target.value;
        setSearchValue(value);
        onChange?.({
            searchType,
            searchValue: value,
        });
    };

    return (
    <Space direction="vertical" style={{ width: '100%' }}>
        {defaultDropDownMenu}
        <Input
            allowClear
            maxLength={50}
            value={searchValue}
            onChange={handleSearchValueChange}
            {...fieldProps}  // 使用hooks返回的表单属性,包含海外的下拉组件配置
        />
    </Space>
    );
};
