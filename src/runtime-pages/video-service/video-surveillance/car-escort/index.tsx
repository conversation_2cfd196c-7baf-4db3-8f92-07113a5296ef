import { withShareRootH<PERSON> } from '@streamax/page-sharing-core';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
    i18n,
    getAppGlobalData,
    useConstructor,
    utils,
    Auth,
    RouterPrompt,
    mosaicManager,
    MosaicTypeEnum,
} from '@base-app/runtime-lib';
import {
    Row,
    Col,
    Timeline,
    Space,
    Tooltip,
    message,
    Button,
    Select,
    Badge,
    Container,
    Splitter,
} from '@streamax/poppy';
import {
    IconMontage,
    IconIntercom,
    IconTextDownload,
    IconMoreDropdownFill,
} from '@streamax/poppy-icons';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { StarryBreadcrumb } from '@base-app/runtime-lib';
import moment from 'moment';
import WebPlayer, {
    VideoPlayDataInfo,
} from '@/runtime-pages/monitoring-center/components/WebPlayer';
import SpeedLine from './components/SpeedLine';
import Map from './components/Map';
import VideoEdit from '../components/ClipModal';
import classNames from 'classnames';
// @ts-ignore
import PlayBackModel from './model';
import {
    DeviceStateType,
    getRealtimeVideoVehicleDetail,
    getVehicleDetail,
    getVehicleStateConfigList,
} from '@/service/vehicle';
import { getDeviceDayquery } from '@/service/device';
import {
    getBaseAlarmList,
    fetchAlarmLevelPage,
    getAlarmType,
} from '@/service/alarm';
import { getBaseGpsList, getGpsLastV2 } from '@/service/gps';
import { getTime } from '@/service/evidence';
import { convertSpeed } from '@/utils/unit';
import MessageSend from '@/components/MessageSend';
import type {
    PlayerStatus,
    WidgetNameType,
} from '@/components/StarryPlayer/types';
import type { RecordStatus } from '@/components/StarryPlayer/types';
import type { SOCKET_DATA } from '@/const/socket-topic';
import { SOCKET_TOPIC, VEHICLE_STATE } from '@/const/socket-topic';
import useRecordFilterFnWrap from '@/hooks/useRecordFilterFnWrap';
// @ts-ignore
import { useDebounceFn, useLatest, useSize, useUpdateEffect } from '@streamax/hooks';
import useRTVehicle from '@/hooks/useRTData/useRTVehicle';
import _, { pick } from 'lodash';
import { getCompleteDevice, getDeviceStates } from '@/utils/commonFun';
import { IntercomInPlayer } from '@base-app/runtime-lib';

// @ts-ignore
import { getDvaApp, useHistory } from '@base-app/runtime-lib/core';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import useSubscribe from '@/hooks/useSubscribe';
// @ts-ignore
import type {
    MessageSendType,
    VehicleTreeType,
} from '@/types/pageReuse/realtimeMonitoring';
import type { RealtimeOnEvent } from '@/types/pageReuse/realtimeVideo';
import type { StarryPlayerCustom } from '@/types/pageReuse/StarryPlayer';
import type { ControlButtons } from '@/types/pageReuse/playbackDevice';
import type { ControlCustomButtons } from '@/types/pageReuse/carEscort';
import { DEVICE_PRIMARY_TYPE } from '@/utils/constant';
import './index.less';
import { getCustomJsx } from '@/utils/pageReuse';
import { streamTypeCodeMap } from '../hooks/useStreamTypeMap';
import usePreLoadH5SDK from '@/hooks/usePreLoadH5SDK';
import useMosaic from '@/hooks/useMosaic';
import { check28181 } from '@/utils/28181utils';
import type {
    PageBase,
    Intercom as IntercomType,
    VehicleParams,
    DeviceParams,
    MapToolCustom,
    JSXBase,
} from '@/types/pageReuse/pageReuseBase';
import { useGetState } from 'ahooks';
import {
    RspBasicLayout,
    RspCenterContainer,
    RspGridLayout,
    RspDynamicPortal,
    useResponsiveShow,
} from '@streamax/responsive-layout';
import {CONTROL_RANGE, isNeedControl} from "@/utils/flow";
const { Item } = RspBasicLayout;

const IC_INTERCOM_SILENCE_LINE = require('@/assets/icons/icon_intercom_silence_line.svg');
const IC_PHONE_FILE = require('@/assets/icons/icon_phone_fill.svg');
const IC_INTERCOM_LINE_LINE = require('@/assets/images/intercom.gif');
const disabledWidgets: WidgetNameType[] = [
    'playPause',
    'currentTime',
    'closeTime',
    'speedSetting',
    'moreSetting',
    'chooseChannel',
    'close',
    'layoutType',
    'videoFrame',
    'frameRateAndBitRate',
    'realtimeRecord',
    'layoutCount',
    'storeType',
    'streamType',
    'enlarge',
    // @ts-ignore
    'clarity',
    'screenshot',
    'volume',
    'playMode',
];

const IconPhoneFill = () => {
    return <img src={IC_PHONE_FILE} alt="" />;
};
const IconIntercomLine = () => {
    return <img style={{ width: '16px' }} src={IC_INTERCOM_LINE_LINE} alt="" />;
};
const IconIntercomSilenceLine = () => {
    return <img src={IC_INTERCOM_SILENCE_LINE} alt="" />;
};
const { timestampToZeroTimeStamp, zeroTimeStampToFormatTime } = utils.formator;

const ONLINE = 1;

const DEVICE_PROTOCOL_TYPE = {
    JT905: 6,
    n9m: 1,
};
/** 消息下发定制 start*/
export type CarEscortShareProps = MessageSendType &
    VehicleTreeType &
    StarryPlayerCustom &
    ControlButtons &
    RealtimeOnEvent &
    ControlCustomButtons &
    IntercomType &
    MapToolCustom & {
        getMapSize: (cacheMapSize: string) => string;
        getVideoEditModalBlock: JSXBase;
    };
/** end */

const CarEscort = (props: CarEscortShareProps) => {
    /** 定制 start */
    const {
        getContentSettingDom,
        getSendSettingDom,
        getSendVehicleDom,
        injectMessageSend,
        getPlayerInitLayout,
        getPlayerInitChannels,
        getPlayerRightTools,
        getChannelTools,
        onScreenshot,
        onSelectVehicleChange,
        getPlayerCustomTools,
        onSelectChannelChange,
        onBeforeIntercom,
        onIntercom,
        onAfterIntercom,
        onIntercomError,
        getUseMapLayerOptions,
        addLoopMethod,
        getLoopTime,
        getMapSize,
        getPlayerInitData,
        getPlayerVehicleData,
        getRealtimeVideoRecordModal,
        getVideoEditModalBlock
    } = props;
    /** 定制 end */
    mosaicManager.useMosaicInit();

    const history = useHistory();
    const { location } = history;
    const { vehicleId = 69, deviceId = undefined } = location?.query;
    const [vehicleInfo, setVehicleInfo] = useState<any>({});
    const latestVehicleInfoRef = useLatest(vehicleInfo);
    const [alarmList, setAlarmList] = useState([]);
    const [gpsList, setGpsList] = useState<any[]>([]);
    const [speedData, setSpeedData] = useState<any[]>([]);
    const [xAxisData, setXAxisData] = useState<any[]>([]);
    const [videoCutTime, setVideoCutTime] = useState<any[]>([0, 0]);
    const [playerChannels, setPlayerChannels] = useState<any[]>([]);
    const [videoCutdownShow, setVideoCutdownShow] = useState<boolean>(false);
    const [havePickFrame, setHavePickFrame] = useState(true);
    const [fullscreen, setFullScreen] = useState(false);
    const [selectedDevice, setSelectedDevice] = useState<{
        authId: string;
        deviceId: string;
    }>({
        authId: '',
        deviceId: '',
    });
    const [recordState, setRecordState] = useState<RecordStatus>('close');
    const [alarmId, setAlarmId] = useState<RecordStatus>('');
    const [selectedChannel, setSelectedChannel, getSelectedChannel] =
        useGetState<number | undefined>(undefined);
    const [cutVideoDisabled, setCutVideoDisabled] = useState(true);
    const [mapSize, setMapSize] = useState<number | string>(
        localStorage.getItem('CAR_ESCORT_MAP_SIZE') || '33%',
    );

    const mapRef = useRef<any>();
    const splitterWrapperRef = useRef<any>();
    const alarmTimer = useRef<any>();
    const playerRef = useRef<any>();
    const voipRef = useRef<any>();
    const textSendRef = useRef<any>();
    const selectedDeviceRef = useRef('');
    const intercomParams = useRef<
        VehicleParams & DeviceParams & { optId?: string }
    >({});
    const intercomRef = useRef<any>();
    const show = useResponsiveShow({
        xl: true,
        xxl: true,
    });
    const [intercomReady, setIntercomReady] = useState(false);

    function _onIntercomStart(params = {}) {
        setIntercomReady(true);
        onIntercom?.({
            ...intercomParams.current,
            ...params,
        });
    }

    function _onAfterIntercom(params = {}) {
        setIntercomReady(false);
        onAfterIntercom?.({
            ...intercomParams.current,
            ...params,
        });
    }

    function _onIntercomError(params = {}) {
        setIntercomReady(false);
        onClickListenClose();
        onIntercomError?.({
            ...intercomParams.current,
            ...params,
        });
    }

    // usePreLoadH5SDK();
    const [when, setWhen] = useState(false);
    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(PlayBackModel);
    });

    const reuseParam = addLoopMethod
        ? {
              getReuseData: addLoopMethod,
              getLoopTime,
          }
        : undefined;

    const { vehicleList, vehicleStateConfig, unLock } = useRTVehicle(
        true,
        DeviceStateType.LIVE,
        undefined,
        reuseParam,
    );
    const latestVehicleRef = useLatest(vehicleList);

    const [loadFlag, setLoadFlag] = useState<boolean>(false);

    useEffect(() => {
        if (Object.keys(latestVehicleInfoRef.current).length === 0) return;
        const curVehicle = vehicleList.find(
            (item) => item.vId === latestVehicleInfoRef.current?.vehicleId,
        );

        if (!curVehicle) return;
        /** 判断设备状态是否变化，变化则更新设备数据 */
        if (
            !_.isEqual(
                curVehicle.deviceList,
                latestVehicleInfoRef.current.deviceList,
            )
        ) {
            unLock();
            setVehicleInfo({
                ...latestVehicleInfoRef.current,
                vType: curVehicle.vType,
                deviceList: getCompleteDevice(
                    latestVehicleInfoRef.current.deviceList,
                    curVehicle.deviceList || [],
                ),
            });
        }
    }, [vehicleList, loadFlag]);

    useEffect(() => {
        message.config({
            getContainer() {
                return document.body;
            },
        });
    }, []);
    useEffect(() => {        
        onSelectVehicleChange?.(vehicleInfo);
        Object.keys(latestVehicleInfoRef.current).length !== 0 &&
            setLoadFlag(true);
    }, [vehicleInfo]);

    useEffect(() => {
        init();
        return () => clearInterval(alarmTimer.current);
    }, []);

    // 监听马赛克权限变化，重新初始化播放器
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.live);
    useUpdateEffect(()=>{
        init();
        return () => clearInterval(alarmTimer.current);
    }, [hasMosaic]);

    const getCurrentDeviceItem = (deviceList: any[]) => {
        let deviceInfo: any;
        if (selectedDeviceRef.current) {
            deviceInfo = deviceList.find(
                (val: any) => val.authId === selectedDeviceRef.current,
            );
        } else if (deviceId && deviceId !== 'undefined') {
            deviceInfo = deviceList?.find(
                (val: any) => val.deviceId === deviceId,
            );
        } else {
            // 默认选择主设备
            deviceInfo =
                deviceList?.find(
                    (val: any) => val.primaryType === DEVICE_PRIMARY_TYPE.main,
                ) || deviceList?.[0];
        }
        return deviceInfo;
    };

    async function init() {
        let rs = await getRealtimeVideoVehicleDetail({
            vehicleId,
            fields: 'device,channel,state,fleet,driver',
        });
        const mosaic = mosaicManager.getVideoMosaicConfigValue(MosaicTypeEnum.live);
        if (mosaic) {
            setHavePickFrame(false);
        }
        const curVehicle = latestVehicleRef.current.find(
            (item) => item.vId === vehicleId,
        );
        rs.deviceList = getCompleteDevice(
            rs.deviceList,
            curVehicle?.deviceList || [],
        );
        unLock();
        const { list = [] } = await getVehicleStateConfigList({
            page: 1,
            pageSize: 9999,
            appId: getAppGlobalData('APP_ID').toString(),
            queryAppIdFlag: true,
        });
        rs.vehicleStateList = list;
        /******
         * 行业层复写源数据
         * *****/
        if(getPlayerVehicleData && getPlayerVehicleData instanceof Function) {
            // @ts-ignore
            rs = await getPlayerVehicleData(rs) || rs;
        };
        /******
         * 行业层复写源数据
         * *****/

        setVehicleInfo({ ...rs, vType: curVehicle?.vType}); //刷新当前页面rs没有车辆类型导致图标显示问题，需要手动增加vType
        const deviceInfo = getCurrentDeviceItem(rs.deviceList);
        setSelectedDevice(deviceInfo);
        if (deviceInfo && deviceInfo.deviceId) {
            selectedDeviceRef.current = deviceInfo.authId;
            const channels = deviceInfo?.deviceChannelList.map(
                (p: any) => p.channelNo,
            );
            getDeviceDayStatusQuery(deviceInfo?.authId, channels || [], rs);

            const channelsVideoData = (deviceInfo?.deviceChannelList || []).map(
                (channel) => {
                    return {
                        vehicleId,
                        vehicleNumber: rs.vehicleNumber,
                        channelAlias: channel.channelAlias,
                        channelNo: channel.channelNo,
                        channel: `${deviceInfo.deviceId}-${channel.channelNo}`,
                        enable: channel.enable,
                        peripheralType: channel.peripheralType,
                        privacyState: channel.privacyState,
                        streamType: 'MINOR',
                        deviceId: deviceInfo.deviceId,
                        // 设备号
                        deviceNo: deviceInfo.deviceNo,
                        // 设备别名
                        deviceAlias: deviceInfo.deviceAlias,
                        // 设备授权ID
                        authId: deviceInfo.authId,
                        flowLimit: channel.flowLimit,
                    };
                },
            );
            const videoPlayData: VideoPlayDataInfo = {
                videoData: {
                    deviceChannelData: channelsVideoData,
                    mosaic,
                    quality: 'SMOOTH',
                    mediaProtocol: 'HTTP_FLV',
                },
            };
            if (deviceInfo.flowLimit) {
                message.warn(i18n.t('message', '流量使用超额，功能暂停使用。'));
            } else {
                playerRef?.current?.play(videoPlayData);
            }
            // 获取报警列表数据
            getAlarmPageListData(vehicleId, true);
            // 获取gps及速度趋势数据
            getGpsListData(vehicleId);
            clearInterval(alarmTimer.current);
            alarmTimer.current = setInterval(() => {
                getAlarmPageListData(vehicleId);
                getGpsListData(vehicleId);
            }, 10000);
        }
    }

    // 打开视频剪辑界面
    const RouterToVideoCutPage = async () => {
        if (intercomReady) {
            message.error({
                content: i18n.t('message', '请先结束对讲'),
                key: 'intercomIng',
            });
            return;
        }
        if (playerRef.current?.getRecordState() == 'start') {
            message.error({
                content: i18n.t('message', '请先结束录制'),
                key: 'overRecord',
            });
            return;
        }
        if (playerChannels.length === 0) {
            return;
        }
        if (cutVideoDisabled) return;

        try {
            setCutVideoDisabled(true);
            const currentTime = await getCurrentTime();
            setVideoCutTime([currentTime - 30, currentTime + 30]);
        } catch (error) {}
        setVideoCutdownShow(true);
        setCutVideoDisabled(false);
    };
    // 前往文本下发页面
    const RouterToTextSendPage = () => {
        if (intercomReady) {
            message.error({
                content: i18n.t('message', '请先结束对讲'),
                key: 'intercomIng',
            });
            return;
        }
        if (playerRef.current?.getRecordState() == 'start') {
            message.error({
                content: i18n.t('message', '请先结束录制'),
                key: 'overRecord',
            });
            return;
        }
        textSendRef.current.openDrawer(
            getDeviceIdWithAuthId(selectedDevice.authId),
            'deviceId',
            vehicleInfo.vehicleNumber,
        );
    };
    // 获取报警类型列表
    const getAlarmTypeList = async (typeIds: string) => {
        if (!typeIds) return [];
        const { list } = await getAlarmType({
            page: 1,
            pageSize: 999999,
            typeIds,
        });
        return list || [];
    };
    // 获取报警等级列表
    const getAlarmLevelList = async (levelIds: string) => {
        if (!levelIds) return [];
        const { list } = await fetchAlarmLevelPage({
            page: 1,
            pageSize: 999999,
            levelIds,
        });
        return list || [];
    };
    // 获取报警列表
    const getAlarmPageListData = (vehicleId: any, isFirst: boolean = false) => {
        const startTime = Math.floor(new Date().getTime() / 1000) - 60 * 30;
        const endTime = Math.floor(new Date().getTime() / 1000);
        getBaseAlarmList({
            startTime,
            endTime,
            vehicleId,
        }).then(async (rs: any) => {
            let levelIds: any[] = [];
            let typeIds: any[] = [];
            rs.map((val: any) => {
                levelIds.push(val?.levelInfo?.levelId);
                typeIds.push(val.alarmType);
            });
            levelIds = [...new Set(levelIds.filter((p) => !!p))];
            typeIds = [...new Set(typeIds.filter((p) => !!p))];
            const levelList = await getAlarmLevelList(levelIds.join(','));
            const typeList = await getAlarmTypeList(typeIds.join(','));
            const newAlarmList = rs
                .map((item: any) => {
                    return {
                        ...item,
                        alarmLevelColor:
                            levelList.find(
                                (p: any) => p.levelId == item.alarmLevelId,
                            )?.levelColor || 'rgba(255, 255, 255, 0.25)',
                        alarmTypeName:
                            typeList.find(
                                (p: any) => p.alarmType == item.alarmType,
                            )?.typeName || '',
                    };
                });
            newAlarmList.sort((a: any, b: any) => b.startTime - a.startTime);
            if (!isFirst && newAlarmList.length > alarmList.length) {
                setAlarmId(newAlarmList?.[0]?.alarmId || '');
            }
            setAlarmList(newAlarmList);
        });
    };
    // gps列表-速度趋势及gps轨迹查询
    const getGpsListData = (vehicleId: any) => {
        const startTime = Math.floor(new Date().getTime() / 1000) - 60 * 30;
        const endTime = Math.floor(new Date().getTime() / 1000);
        getBaseGpsList({
            vehicleId,
            startTime,
            endTime,
        }).then(async (rs) => {
            let list: any[] = rs;
            if (!list || !list.length) {
                // 如果最后30分钟无GPS位置，则查询最后一次GPS位置
                list = await getGpsLastV2({ vehicleIds: vehicleId });
            }
            const newGpsList = list
                .map((val: any) => {
                    const { lat, lng } = { lat: val.lat, lng: val.lng };
                    return {
                        lng,
                        lat,
                        gpsTime: val.gpsTime * 1000,
                        ang: val?.angle,
                    };
                })
                .reverse();
            setGpsList(newGpsList);
            // 速度趋势
            setSpeedData(() => {
                return [
                    ...rs.map((val: any) =>
                        utils.formator.getSpeedFormat(val.speed / 10),
                    ),
                ];
            });
            setXAxisData(() => {
                return [
                    ...rs.map((val: any) =>
                        utils.formator.zeroTimeStampToFormatTime(
                            val.gpsTime,
                            Number(
                                getAppGlobalData('APP_USER_CONFIG')?.timeZone,
                            ) || 0,
                            'HH:mm:ss',
                        ),
                    ),
                ];
            });
        });
    };
    async function getDeviceDayData(
        id: string,
        sTime: string,
        eTime: string,
        vehiclerInfo: any,
    ) {
        const device = getCurrentDeviceItem(vehiclerInfo.deviceList);
        // 若没有选中的通道，则默认使用设备的第一个通道
        const channel28181 =
            getSelectedChannel() ||
            (device?.deviceChannelList || [])[0]?.channelNo;
        const channels = check28181(device || {})
            ? [channel28181]
            : device?.deviceChannelList?.map(
                  (device: any) => device?.channelNo,
              ) || [];
        let storeType = 'ALL';
        let streamType = 'ALL';
        // protocolType为1表示是n9m协议，只有n9m协议才支持通道查询，非n9m协议传0，查询全部通道
        const protocolType = device.protocolType;
        if (protocolType == DEVICE_PROTOCOL_TYPE.JT905) {
            storeType = 'MASTER';
            streamType = 'MAJOR';
        }
        const chan =
            protocolType == DEVICE_PROTOCOL_TYPE.n9m ? channels.join(',') : '0';
        // 【103201】单车护航当设备未上过线存在异常时会存在两个错误提示，一个日历接口提示，一个播放器内的暂无通道提示，
        // 播放器内，播放器是所有直通共用，其他地方需要这个提示，所以单车护航页，查询日历接口前，判断当channel为空时，不发起日历接口查询，和s17确认，只要channel为空接口一定会返回错误
        if (!chan) {
            throw '';
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return await getDeviceDayquery(
            {
                devId: id,
                chan,
                storeType,
                streamType,
                beginTime: sTime,
                endTime: eTime,
                // timeZoneOffset: 0
            },
            {
                _accessKey: `${localStorage.getItem(
                    'AUTH_TOKEN',
                )}#${getAppGlobalData('APP_ID')}`,
            },
            false,
        );
    }
    // 查询日历
    const getDeviceDayStatusQuery = async (
        id: string,
        channels: any[],
        vehicleInfo: any,
    ) => {
        const platformTimeZone = Number(
            getAppGlobalData('PLATFORM_TIME_ZONE') || 0,
        );
        const dateFormat = 'YYYY-MM-DD HH:mm:ss';
        let dataList = [];
        const startTime = moment().startOf('day').unix();
        const endTime = moment().endOf('day').unix();
        // 将用户时间转换成平台时区字符串时间
        // 单车护航不需要将用户电脑时区时间转换成0时区的时间戳，只有用户操作选择时间的场景，需转换成0时区时间戳，再转换成平台时区字符串
        const zeroDateS = zeroTimeStampToFormatTime(
            startTime,
            platformTimeZone,
            dateFormat,
            true,
        );
        const zeroDateE = zeroTimeStampToFormatTime(
            endTime,
            platformTimeZone,
            dateFormat,
            true,
        );
        const data = await getDeviceDayData(
            id,
            zeroDateS,
            zeroDateE,
            vehicleInfo,
        );
        if (!data) {
            return;
        }
        dataList = [...data];
        let channelList: any[] = [];
        let maxTime = 0;
        dataList.map((val: any) => {
            const index = channelList.findIndex((p) => p.channel === val.chan);
            const endStamp = timestampToZeroTimeStamp(
                moment(val.endTime),
                platformTimeZone,
                true,
            );
            if (endStamp > maxTime) {
                maxTime = endStamp;
            }
            if (index > -1) {
                channelList[index].videoList.push({
                    ...val,
                    startUnix: timestampToZeroTimeStamp(
                        moment(val.beginTime),
                        platformTimeZone,
                        true,
                    ),
                    endUnix: endStamp,
                });
            } else {
                channelList.push({
                    channel: val.chan,
                    videoList: [
                        {
                            ...val,
                            startUnix: timestampToZeroTimeStamp(
                                moment(val.beginTime),
                                platformTimeZone,
                                true,
                            ),
                            endUnix: endStamp,
                        },
                    ],
                });
            }
        });
        const deviceChannelList =
            getCurrentDeviceItem(vehicleInfo?.deviceList)?.deviceChannelList ||
            [];
        channelList = channelList.map((item: any) => {
            return {
                ...item,
                channelAlias:
                    deviceChannelList.find(
                        (p: any) => p.channelNo == item.channel,
                    )?.channelAlias || '',
            };
        });

        const channelResult = channelList
            .filter((p: any) => p.channelAlias)
            .sort((a: any, b: any) => a.channel - b.channel);
        setPlayerChannels(channelResult);
    };
    const onClickListenClose = () => {
        setWhen(false);
    };
    const getCurrentTime = async () => {
        return await getTime();
    };
    useEffect(() => {
        playerRef.current?.setVolume(intercomReady ? 0 : 1);
    }, [intercomReady]);

    const onFullScreen = (state: boolean) => {
        setFullScreen(state);
    };

    const textSendEle = !fullscreen ? (
        <Auth
            key="messageSend"
            code="@base:@page:car.escort@action:messagesend"
        >
            <Tooltip title={i18n.t('action', '消息下发')}>
                <span
                    onClick={RouterToTextSendPage}
                    className={`btn-video-cut video-btn ${
                        intercomReady ? 'video-btn-disabled' : ''
                    }`}
                >
                    <IconTextDownload />
                </span>
            </Tooltip>
        </Auth>
    ) : null;

    const cutVideoEle = !fullscreen ? (
        <Auth key="cutVideo" code="@base:@page:car.escort@action:cut.video">
            <Tooltip title={i18n.t('action', '视频剪辑')}>
                <span
                    onClick={RouterToVideoCutPage}
                    className={
                        intercomReady ||
                        playerChannels.length === 0 ||
                        cutVideoDisabled
                            ? 'video-btn-disabled'
                            : ''
                    }
                >
                    <IconMontage />
                </span>
            </Tooltip>
        </Auth>
    ) : null;
    const beforeIntercomPromise = async (deviceInfo: any) => {
        if (playerRef.current?.getRecordState() == 'start') {
            message.error({
                content: i18n.t('message', '请先结束录制'),
                key: 'overRecord',
            });
            return Promise.reject({
                isNext: false,
            });
        }
      
        const params = {
            vehicleId,
            vehicleNumber: vehicleInfo.vehicleNumber,
            deviceId: selectedDevice.deviceId,
            authId: selectedDevice.authId,
        };
        const { optId } = onBeforeIntercom?.(params) || {};
        // @ts-ignore
        params.optId = optId;
        intercomParams.current = params;
        return Promise.resolve({
            isNext: true,
            optId,
        });
    };
    const intercom = (
        <Auth key="intercom" code="@base:@page:car.escort@action:intercom">
            <IntercomInPlayer
                ref={intercomRef}
                deviceInfo={selectedDevice}
                vehicleId={vehicleInfo.vehicleId}
                onIntercomClose={_onAfterIntercom}
                onIntercomError={_onIntercomError}
                onIntercomStart={_onIntercomStart}
                beforeIntercomPromise={beforeIntercomPromise}
            />
        </Auth>
    );
    const controlRender = () => {
        let renderList = [intercom, textSendEle, cutVideoEle];
        if (check28181(selectedDevice || {})) {
            renderList = [intercom, cutVideoEle];
        }
        return (
            <div className="custom-operation-group">
                <Space size={14}>
                    {getCustomJsx(getPlayerCustomTools, renderList)}
                </Space>
            </div>
        );
    };
    const renderDeviceOptions = (): any => {
        return vehicleInfo?.deviceList?.map((item: any) => {
            const curState = getDeviceStates(
                item,
                vehicleStateConfig || [],
            )?.[0];

            return (
                <Select.Option key={item.authId} value={item.authId}>
                    <Tooltip
                        title={
                            item.deviceAlias
                                ? `${item.deviceAlias}(${item.deviceNo})`
                                : item.deviceNo
                        }
                        className="device-alias-option-tooltip-item"
                    >
                        <Badge
                            color={curState?.stateColor || '#D9D9D9'}
                            className="vehicle-status-badge"
                        />
                        {item.deviceAlias || item.deviceNo}
                    </Tooltip>
                </Select.Option>
            );
        });
    };
    const handleDeviceChange = async (val: any) => {
        const realVehicleInfo = await getRealtimeVideoVehicleDetail({
            vehicleId,
        });
        const device = realVehicleInfo?.deviceList?.find(
            (p: any) => p.authId === val,
        );
        if (!device) {
            message.warn(i18n.t('message', '设备不存在'));
            return;
        }
        if (device && device.onlineState !== ONLINE) {
            message.warn(i18n.t('message', '设备离线'));
            return;
        }
        setSelectedDevice(device);
        selectedDeviceRef.current = val;
        // 关闭对讲
        intercomRef.current?.stopIntercom();
        // 重新执行init
        init();
    };
    const getDeviceIdWithAuthId = (authId: string) => {
        const { deviceList = [] } = vehicleInfo || {};
        const item = deviceList.find((p) => p.authId === authId) || {};
        return item.deviceId;
    };
    // 播放器右下角操作工具栏，对GB28181设备处理过滤录制和更多设置
    const getRightToolsNew = (list: any, data: any) => {
        let resultList = list;
        // GB28181过滤操作显示
        if (check28181(selectedDevice || {})) {
            const GB28181_OPTIONS = ['LiveVideoMoreSetting'];
            resultList = list.filter(
                (item) => !GB28181_OPTIONS.includes(item.key),
            );
        }
        return getPlayerRightTools
            ? getPlayerRightTools?.(resultList, data)
            : resultList;
    };

    // 播放器单个通道操作栏，对GB28181设备，删除通道操作
    const getChannelToolsNew = (list: any, data: any) => {
        let resultList = list;
        // GB28181过滤操作显示
        if (check28181(selectedDevice || {})) {
            resultList = [];
        }
        return getChannelTools
            ? getChannelTools?.(resultList, data)
            : resultList;
    };

    // 播放器播放通道设置，GB28181设备，只能播放一个通道
    const getPlayerInitLayoutNew = () => {
        let resultNum = 4;
        // GB28181过滤操作显示
        if (check28181(selectedDevice || {})) {
            resultNum = 1;
        }
        return getPlayerInitLayout ? getPlayerInitLayout?.() : resultNum;
    };

    // 激活通道变更的事件监听，对于28181设备，只能播放一个通道，当激活通道变更，则为切换了通道，
    // 28181设备切换通道，重新查询日历
    const onActiveChannelChange = (channel: string) => {
        const resChannel =
            Number(channel) ||
            Number(channel.split('-')[1]) ||
            getSelectedChannel();
        if (
            check28181(selectedDevice || {}) &&
            resChannel !== getSelectedChannel()
        ) {
            setSelectedChannel(resChannel);
            const channels =
                selectedDevice?.deviceChannelList?.map(
                    (p: any) => p.channelNo,
                ) || [];
            // 设置后，获取日历，会获取通道，使用定时器异步获取
            setTimeout(() => {
                getDeviceDayStatusQuery(
                    selectedDevice.authId,
                    channels,
                    vehicleInfo,
                );
            }, 30);
        }
    };

    const onPlayerStatusChange = ( playerStatus: PlayerStatus)=>{
        intercomRef.current?.setDisabledTalkButton(playerStatus !== 'playing');
        setCutVideoDisabled(playerStatus !== 'playing');
    };
    const size = useSize(splitterWrapperRef);
    const onResizeEnd = (sizes: any) => {
        if (size?.width) {
            localStorage.setItem(
                'CAR_ESCORT_MAP_SIZE',
                String((sizes[1] / size?.width) * 100) + '%',
            );
            mapRef.current?.mapInstance?.invalidateSize();
        }
    };

    useEffect(() => {
        if (getMapSize) {
            const cacheMapSize =
                localStorage.getItem('CAR_ESCORT_MAP_SIZE') || '';
            const outMapSize = getMapSize(cacheMapSize);
            setMapSize(outMapSize);
        }
    }, [getMapSize]);

    const containerList = [
        {
            id: 'videoMap',
            selector: '.video-map-horizontal-wrapper'
        },
        {
            id: 'video',
            selector: '.video-horizontal-wrapper'
        }
    ];


    const renderVideo = (
        <div className={classNames('video-wrapper')}>
            <div className="content-com" id="video-dom">
                <WebPlayer
                    pageCode="@base:@page:car.escort"
                    authWidgets={['enlarge', 'realtimeRecord', 'algorithmMode']}
                    ref={playerRef as any}
                    moveInRefresh={true}
                    controlRender={controlRender()}
                    emptyText={i18n.t('message', '暂无视频')}
                    // @ts-ignore
                    disabledWidgets={intercomReady ? disabledWidgets : []}
                    onFullScreen={onFullScreen}
                    onRecordStateChange={(value) => setRecordState(value)}
                    getPlayerRightTools={getRightToolsNew}
                    getChannelTools={getChannelToolsNew}
                    onScreenshot={onScreenshot}
                    onActiveChannelChange={onActiveChannelChange}
                    getPlayerInitLayout={getPlayerInitLayoutNew}
                    getPlayerInitChannels={getPlayerInitChannels}
                    onSelectChannelChange={onSelectChannelChange}
                    onPlayerStatusChange={onPlayerStatusChange}
                    getPlayerInitData={getPlayerInitData}
                    getRealtimeVideoRecordModal={getRealtimeVideoRecordModal}
                />
            </div>
        </div>
    );
    const renderMap = (
        <div
            className={classNames('car-escort-map-wrapper')}
            style={{
                flex: '0 0 464px',
            }}
        >
            <div className="content-com">
                <Map
                    ref={mapRef}
                    gpsData={gpsList}
                    deviceInfo={vehicleInfo}
                    alarmId={alarmId}
                    getUseMapLayerOptions={getUseMapLayerOptions}
                />
            </div>
        </div>
    );

    const renderVideoEditModal = videoCutdownShow ? (
        <VideoEdit
            havePickFrame={
                havePickFrame &&
                Auth.check(
                    streamTypeCodeMap['PICK_FRAME']
                        .carEscortCode as string,
                )
            }
            visible={videoCutdownShow}
            vehicleInfo={vehicleInfo}
            channelList={playerChannels}
            clipVideoTime={{
                startTime: videoCutTime[0],
                videoLength: videoCutTime[1] - videoCutTime[0],
            }}
            selectedDate={moment().format('YYYY-MM-DD')}
            onCloseModal={() => setVideoCutdownShow(false)}
            deviceInfo={getCurrentDeviceItem(
                vehicleInfo.deviceList,
            )}
        />
    ) : null;

    return (
        <StarryBreadcrumb className="car-escort-container">
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能终止您的车辆对讲程序。',
                )}
            />
            <div className="car-info">
                <Row className="car-info-header">
                    <div className="car-number">
                        {vehicleInfo.vehicleNumber || '-'}
                    </div>
                    <div className="split-line" />
                    <Select
                        bordered={false}
                        className="device-select"
                        suffixIcon={
                            <IconMoreDropdownFill
                                style={{ pointerEvents: 'none' }}
                            />
                        }
                        value={selectedDevice.authId}
                        onChange={handleDeviceChange}
                        disabled={recordState === 'start'}
                    >
                        {renderDeviceOptions()}
                    </Select>
                </Row>
                <Container>
                    <Row>
                        <Col span={8}>
                            <div className="info-item">
                                <span className="info-name">
                                    {i18n.t('name', '所属车组')}:{' '}
                                </span>
                                <OverflowEllipsisContainer className="info-value">
                                    <AuthFleetShow
                                        fleetList={vehicleInfo?.fleetList}
                                    />
                                </OverflowEllipsisContainer>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className="info-item">
                                <span className="info-name">
                                    {i18n.t('name', '司机姓名')}:{' '}
                                </span>
                                <OverflowEllipsisContainer className="info-value">
                                    <AuthDriverShow
                                        driverList={vehicleInfo?.driverList}
                                    />
                                </OverflowEllipsisContainer>
                            </div>
                        </Col>
                    </Row>
                </Container>
            </div>
            <Container>
                <div className="map-video-container">
                    <div
                        className={`video-map-wrapper ${show ? '' : 'video-map-wrapper-xl'}`}
                        ref={splitterWrapperRef}
                    >
                        {show ? (
                            <Splitter
                                style={{ height: '100%' }}
                                layout={'horizontal'}
                                onResize={(sizes) => {
                                    setMapSize(sizes[1]);
                                }}
                                supportResponsive={false}
                                onResizeEnd={onResizeEnd}
                            >
                                <Splitter.Panel>
                                    <div className="video-map-horizontal-wrapper video-dynamic-container" />
                                </Splitter.Panel>
                                <Splitter.Panel
                                    defaultSize={'33%'}
                                    size={mapSize}
                                    min="25%"
                                    max="50%"
                                >
                                    {renderMap}
                                </Splitter.Panel>
                            </Splitter>
                        ) : (
                            <div className="video-map-wrapper-xl">
                                <div className="video-horizontal-wrapper video-dynamic-container" />
                                {renderMap}
                            </div>
                        )}
                    </div>
                </div>
                <RspDynamicPortal 
                    containers={containerList} 
                    activeId={show ? 'videoMap' : 'video'} 
                >
                    {renderVideo}
                </RspDynamicPortal>
            </Container>

            <div className="speed-alarm-container">
                <RspBasicLayout layoutType="responsive-grid">
                    <Item xs={24} sm={24} md={24} lg={24} xl={15} xxl={15}>
                        <div className="speed-box">
                            <div className="speed-title">
                                {i18n.t('name', '速度趋势')}
                            </div>
                            <SpeedLine
                                speedData={speedData}
                                xAxisData={xAxisData}
                                style={{ height: '300px' }}
                            />
                        </div>
                    </Item>
                    <Item xs={24} sm={24} md={24} lg={24} xl={9} xxl={9}>
                        <div className="alarm-box">
                            <div className="alarm-title">
                                {i18n.t('name', '报警事件')}
                            </div>
                            <div className="alarm-list">
                                {alarmList.length === 0 ? (
                                    <div className="no-data">
                                        {i18n.t('message', '暂无数据')}
                                    </div>
                                ) : null}
                                <Timeline>
                                    {alarmList.map(
                                        (item: any, index: number) => (
                                            <Timeline.Item
                                                key={index}
                                                color={
                                                    index === 0
                                                        ? '#1890FF'
                                                        : 'gray'
                                                }
                                            >
                                                <span
                                                    className="dot"
                                                    style={{
                                                        backgroundColor:
                                                            item.alarmLevelColor,
                                                    }}
                                                />
                                                <Tooltip
                                                    title={i18n.t(
                                                        `@i18n:@alarmType__${item.alarmType}`,
                                                        item.alarmTypeName,
                                                    )}
                                                >
                                                    <span className="alarm-text-ellipsis">
                                                        {i18n.t(
                                                            `@i18n:@alarmType__${item.alarmType}`,
                                                            item.alarmTypeName,
                                                        )}
                                                    </span>
                                                </Tooltip>
                                                <span className="space-interget" />
                                                {utils.formator.zeroTimeStampToFormatTime(
                                                    item.startTime,
                                                )}
                                            </Timeline.Item>
                                        ),
                                    )}
                                </Timeline>
                            </div>
                        </div>
                    </Item>
                </RspBasicLayout>
            </div>
            {getCustomJsx(getVideoEditModalBlock, [renderVideoEditModal], {
                havePickFrame: havePickFrame && Auth.check(
                    streamTypeCodeMap['PICK_FRAME']
                        .carEscortCode as string,
                ),
                videoCutdownShow,
                setVideoCutdownShow,
                channelList: playerChannels,
                videoCutTime,
                vehicleInfo: Object.assign({}, {
                    vehicleId: vehicleInfo.vehicleId,
                    vehicleNumber: vehicleInfo.vehicleNumber,
                    deviceList: vehicleInfo.deviceList?.slice(0).map(item =>pick(item, ["deviceId","authId","protocolType","subProtocolType", "deviceAbility","onlineState"])),
                }),
                selectDeviceInfo: {
                    authId: selectedDeviceRef.current
                }
            })}
            <MessageSend
                ref={textSendRef}
                hasSelectVehicle={false}
                authCode={{
                    textSend:
                        '@base:@page:car.escort@action:messagesend:common.text.manage',
                    audioSend:
                        '@base:@page:car.escort@action:messagesend:common.audio.manage',
                    textMessage:
                        '@base:@page:car.escort@action:text.message',
                    audioMessage:
                        '@base:@page:car.escort@action:audio.message',
                    displayLocation:
                        '@base:@page:car.escort@action:messagesend:display.screen',
                    audioTransformAuthCode: '@base:@page:car.escort@action:messagesend:text.to.speech'
                }}
                getContentSettingDom={getContentSettingDom}
                getSendSettingDom={getSendSettingDom}
                getSendVehicleDom={getSendVehicleDom}
                injectMessageSend={injectMessageSend}
            />
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(CarEscort);
