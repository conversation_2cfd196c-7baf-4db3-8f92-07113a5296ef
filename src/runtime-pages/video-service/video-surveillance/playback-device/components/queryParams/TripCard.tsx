import { DoubleLeftOutlined } from '@ant-design/icons';
import { useLatest } from '@streamax/hooks';
import { Space, Spin, Tooltip, message } from '@streamax/poppy';
import { IconArrow02Left, IconArrow02Right } from '@streamax/poppy-icons';
import { i18n, utils, g_emmiter, getAppGlobalData,LocationResolution } from '@base-app/runtime-lib';
import { useLocation } from '@base-app/runtime-lib/core';
import cn from 'classnames';
import { pick } from 'lodash';
import _ from 'lodash';
import { useState, useEffect, useContext } from 'react';
import { CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME, PAGE_ORIGIN, PLAY_MODE } from '../../constant';
import usePageOrigin from '../../hooks/usePageOrigin';
import { Provider, Context } from '../../store';
import { vehicleIsOnline } from '../../utils';
import type { QueryParamsProps } from './index';
import AuthDriverShow from '@/components/AuthDriverShow';
import useRTVehicle from '@/hooks/useRTData/useRTVehicle';
import { fullTravelData } from '@/runtime-pages/track-playback/utils';
import { geoPositionAnalysisV2 } from '@/service/geo';
import { driverRecordVehiclePage, getTravelList } from '@/service/track-playback';
import { DeviceStateType, getVehicleDetailWithEnabledChannels } from '@/service/vehicle';
import { getAppMileageUnit, getCompleteDevice } from '@/utils/commonFun';
import { getCurrentTime } from '@/utils/time';
import { getMiles } from '@/utils/unit';
import './tripCard.less';
import { getCustomJsx } from '@/utils/pageReuse';
// import { getCurrentTime } from '@/utils/time';

const { zeroTimeStampToFormatTime } = utils.formator;
interface RouterParams {
    vehicleId: string;
    deviceId: string;
    startTime: number; // 所有行程的最早行程开始时间
    endTime: number; // 所有行程的最晚行程结束时间
    travelStartTime: number; // 当前行程开始时间
    travelEndTime: number; // 当前行程结束时间
}

interface TravelInfo {
    startTime: number;
    endTime: number;
    mileage: number;
    driverId: string;
    driverName: string;
    startPos?: {
        // 起点位置
        lat?: number;
        lng?: number;
        address?: string;
    };
    endPos?: {
        // 终点位置
        lat?: number;
        lng?: number;
        address?: string;
    };
    during?: {
        // 行程时长
        hour: number;
        minute: number;
    };
    index?: number; // 序号
}

const TripCard = (props: QueryParamsProps) => {
    /**定制项***/
    const { getGpsInfoBlock } = props;
    // end

    const { onChange } = props;
    const { dispatch, cliping, queryParams } = useContext(Context);

    // 从路由中获取需要的参数信息
    const {
        vehicleId,
        deviceId,
        startTime,
        endTime,
        travelStartTime,
    }: // @ts-ignore
    RouterParams = useLocation().query;
    const pageOrigin = usePageOrigin();

    const [travels, setTravels] = useState<any[]>([]); // 所有的行程信息
    const [currentTravel, setCurrentTravel] = useState<TravelInfo>(); // 当前行程信息
    const [vehicleInfo, setVehicleInfo] = useState<any>(null); // 车辆信息
    const latestVehicleInfoRef = useLatest(vehicleInfo);
    const [deviceInfo, setDeviceInfo] = useState<any>(); // 设备信息
    const [loading, setLoading] = useState<boolean>(true);

    /** 钩子内部会在执行第一次之后上锁，因此需要手动调用unlock */
    const { vehicleList, vehicleStateConfig, unLock } = useRTVehicle(
        true,
        DeviceStateType.PLAYBACK,
    );
    const latestVehicleRef = useLatest(vehicleList);
    const latestStateConfigRef = useLatest(vehicleStateConfig);

    useEffect(() => {
        if (!latestVehicleInfoRef.current) return;
        const curVehicle = vehicleList.find(
            (item) => item.vId === latestVehicleInfoRef.current?.vehicleId,
        );
        if (!curVehicle) return;
        /** 判断设备状态是否变化，变化则更新设备数据 */
        if (!_.isEqual(curVehicle?.deviceList || [], latestVehicleInfoRef.current.deviceList)) {
            unLock();
            setVehicleInfo({
                ...latestVehicleInfoRef.current,
                stateConfig: latestStateConfigRef.current,
                deviceList: getCompleteDevice(
                    latestVehicleInfoRef.current.deviceList,
                    curVehicle.deviceList || [],
                ),
            });
        }
    }, [vehicleList]);

    useEffect(() => {
        // 手动切换了播放模式
        g_emmiter.on(CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME, () => {
            // 针对于行程卡片，只需要重新出发一次onChange即可
            formatQueryParams();
        });
        return () => {
            g_emmiter.off(CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME);
        };
    });

    useEffect(() => {
        vehicleId && fetchVehicleDetail();
        deviceId && queryAllTrips();
    }, []);

    useEffect(() => {
        if (queryParams?.deviceInfo) {
            // 解决 轨迹回放跳转设备回放，在切换设备后在切换播放模式（外部切换设备后，内部无法感知设备变化）
            setDeviceInfo(queryParams?.deviceInfo);
        }
    }, [queryParams?.deviceInfo]);

    // 行程切换时，需要调用onChange事件
    useEffect(() => {
        formatQueryParams();
    }, [currentTravel, deviceInfo, vehicleInfo]);

    // 格式化参数，将设置的参数抛给上层
    function formatQueryParams() {
        // bug 62779 查询时间和轨迹回放的查询时间保持一致， 之前为什么要%60？
        const startTime = Number(currentTravel?.startTime); // - (Number(currentTravel?.startTime) % 60);
        const endTime = Number(currentTravel?.endTime); // - (Number(currentTravel?.endTime) % 60);
        if (startTime === endTime) {
            message.warning(i18n.t('message', '行程时间过短，查询不到有效数据'));
            onChange?.(null);
        } else {
            if (vehicleInfo && deviceInfo && currentTravel) {
                const params = {
                    vehicleInfo: pick(vehicleInfo, [
                        'vehicleId',
                        'vehicleNumber',
                        'onlineState',
                        'deviceList',
                        'stateConfig',
                    ]),
                    deviceInfo,
                    // 行程的查询时间只需要精确到分，故抹除秒
                    startTime,
                    endTime,
                };
                onChange?.(params);
            } else {
                onChange?.(null);
            }
        }
    }

    // 查询车辆详情
    function fetchVehicleDetail() {
        getVehicleDetailWithEnabledChannels({
            vehicleId,
            fields: 'device,channel',
            filterDeviceUnactivated: true
        }).then((data) => {
            // 查找到相应的设备信息
            const { deviceList } = data;
            const vehicleOnline = vehicleIsOnline(data);
            dispatch({ type: 'vehicleOnlineState', payload: vehicleOnline });
            const device = (data?.deviceList || []).find(
                (device: any) => device.deviceId === deviceId,
            );
            let deviceOnline = true;
            if (device) {
                deviceOnline = vehicleIsOnline(device);
            } else if (deviceList?.[0]) {
                // 如果设备离线，则设置为图片模式
                deviceOnline = vehicleIsOnline(deviceList?.[0]);
            }
            dispatch({
                type: 'playMode',
                payload: deviceOnline ? PLAY_MODE.video : PLAY_MODE.image,
            });
            const curVehicle = latestVehicleRef.current.find((item) => item.vId === vehicleId);
            data.deviceList = getCompleteDevice(data.deviceList, curVehicle?.deviceList || []);
            data.stateConfig = latestStateConfigRef.current;
            setDeviceInfo(device || deviceList?.[0] || null);
            setVehicleInfo(data);
            unLock();
        });
    }

    // 查询到所有的行程
    async function queryAllTrips() {
        // 给每个行程编一个序号，便于在切换行程是可以通过索引去找
        try {
            const travelList = await getTravelList({
                vehicleId,
                startTime,
                endTime
            });
            const data: any[] = (travelList || []).map((t: any, i: number) => {
                const { startTime, endTime } = t;
                // 添加序号
                t.index = i;
                // 行程时长
                const duringTime = endTime - startTime;
                t.during = {
                    hour: Math.floor(duringTime / (60 * 60)),
                    minute: Math.floor((duringTime % (60 * 60)) / 60),
                };
                return t;
            });
            setFullData(data);
            setTravels(data);
            // 默认选中跳转过来的行程
            const travel = data.find((t) => t.startTime == travelStartTime);
            if (travel) {
                const currentData = await formatAddressInfo(travel);
                const _currentData = { ...travel, ...currentData };
                setCurrentTravel(_currentData);
            }
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    }
    // 设置关联司机新数据
    const setFullData = async (data: any) => {
        const timeParamList = data.map((item: TravelInfo) => {
            return {
                startTime: item.startTime,
                endTime: item.endTime || getCurrentTime(),
            };
        });
        const record = await driverRecordVehiclePage({
            vehicleId,
            timeParamList,
        });
        const _data = fullTravelData(data, record);
        setTravels(_data);
        // 默认选中跳转过来的行程
        const travel = _data.find((t) => t.startTime == travelStartTime);
        if (travel) {
            const currentData = await formatAddressInfo(travel);
            const _currentData = { ...travel, ...currentData };
            setCurrentTravel(_currentData);
        }
    };

    // 格式化行程的地理位置信息
    async function formatAddressInfo(travel: any) {
        // 将gps点转为位置信息
        const { startPos, endPos } = travel;
        travel.startPos.address = await gpsToAddress(startPos.lat, startPos.lng);
        travel.endPos.address = await gpsToAddress(endPos.lat, endPos.lng);
        return travel;
    }

    // 将GPS信息转为位置
    async function gpsToAddress(lat: number, lng: number) {
        const errMsg = i18n.t('message', '解析失败');
        try {
            if (lat & lng) {
                const data = await geoPositionAnalysisV2({
                    lat,
                    lng,
                });
                // 会有address为null的情况出现
                return data?.address ? data.address : errMsg;
            } else {
                return i18n.t('name', '无位置信息');
            }
        } catch (error) {
            return errMsg;
        }
    }

    // 切换行程
    async function handleChangeTrip(type: 'next' | 'prev' | 'last') {
        let travel = null;
        const index: number = currentTravel?.index || 0;
        // 如果正在剪辑，不能操作
        if (cliping) return;
        // 下一段行程 & 判断是否是最后一段行程
        if (type === 'next' && index < travels.length - 1) {
            travel = travels[index + 1];
            // 上一段行程 & 判断是否是第一段行程
        } else if (type === 'prev' && index > 0) {
            travel = travels[index - 1];
            // 最后一段行程
        } else if (type === 'last') {
            travel = travels[travels.length - 1];
        }
        if (travel) {
            setCurrentTravel(await formatAddressInfo(travel));
        }
    }
    const headerContent = (
        <div className="header">
            <Space size={16}>
                <div
                    className={cn('button-wrapper', {
                        disabled: cliping || currentTravel?.index === 0,
                    })}
                    onClick={() => handleChangeTrip('prev')}
                >
                    <Space>
                        <IconArrow02Left />
                        {i18n.t('action', '上一段')}
                    </Space>
                </div>
                <div
                    className={cn('button-wrapper', {
                        disabled: cliping || currentTravel?.index === travels.length - 1,
                    })}
                    onClick={() => handleChangeTrip('next')}
                >
                    <Space>
                        {i18n.t('action', '下一段')}
                        <IconArrow02Right />
                    </Space>
                </div>
            </Space>
            <div
                className={cn('button-wrapper', {
                    disabled: cliping || currentTravel?.index === travels.length - 1,
                })}
                onClick={() => handleChangeTrip('last')}
            >
                {i18n.t('action', '最后一段行程')}
            </div>
        </div>
    );
    const middleContent = (
        <div className="content">
            <div className="time-info">
                <Space>
                    <span>
                        {(currentTravel &&
                            zeroTimeStampToFormatTime(
                                currentTravel?.startTime,
                                undefined,
                                getAppGlobalData('APP_USER_CONFIG').timeFormat,
                            )) ||
                            '-'}
                    </span>
                    <span>
                        {currentTravel?.during?.hour
                            ? `${currentTravel?.during?.hour}${i18n.t('name', '小时')}`
                            : ''}
                        {currentTravel?.during?.minute
                            ? `${currentTravel?.during?.minute}${i18n.t('name', '分钟')}`
                            : ''}
                       <span className='time-info-line' />
                        {currentTravel &&
                            `${getMiles(currentTravel?.mileage)} ${getAppMileageUnit()}`}
                    </span>
                </Space>
            </div>
            <div className="info-detail-wrapper">
                <div className="left">
                    <div className="dot red" />
                    <div className="line" />
                    <div className="icon-wrapper">
                        <DoubleLeftOutlined rotate={90} />
                    </div>
                    <div className="line" />
                    <div className="dot green" />
                </div>
                <div className="right">
                    <p>
                        <Space>
                            <span className="time">
                                {(currentTravel &&
                                    zeroTimeStampToFormatTime(
                                        currentTravel?.endTime,
                                        undefined,
                                        'HH:mm',
                                    )) ||
                                    '-'}
                            </span>
                            <span className="address">
                                <LocationResolution toolTipProps={{ placement: "left"}} point={{
                                    lat: currentTravel?.endPos?.lat || 0,
                                    lng: currentTravel?.endPos?.lng || 0
                                }}/>
                            </span>
                        </Space>
                    </p>
                    <p>
                        <Space>
                            <span className="time">
                                {(currentTravel &&
                                    zeroTimeStampToFormatTime(
                                        currentTravel?.startTime,
                                        undefined,
                                        'HH:mm',
                                    )) ||
                                    '-'}
                            </span>
                            <span className="address">
                                <LocationResolution toolTipProps={{ placement: "left"}} point={{
                                    lat: currentTravel?.startPos?.lat || 0,
                                    lng: currentTravel?.startPos?.lng || 0
                                }}/>
                            </span>
                        </Space>
                    </p>
                    <p className="driver">
                        <Tooltip overlay={currentTravel?.driverName} placement="left">
                            {i18n.t('name', '司机')}：
                            <AuthDriverShow driverList={currentTravel ? [currentTravel] : []} />
                        </Tooltip>
                    </p>
                </div>
            </div>
        </div>
    );
    return (
        <Provider>
            <Spin spinning={loading}>
                <div className="page-playback-query-params-trip-card">
                    {getCustomJsx(getGpsInfoBlock, [headerContent, middleContent], {
                        ...useLocation().query,
                        cliping,
                        queryParams,
                    })}
                </div>
            </Spin>
        </Provider>
    );
};

export default TripCard;
