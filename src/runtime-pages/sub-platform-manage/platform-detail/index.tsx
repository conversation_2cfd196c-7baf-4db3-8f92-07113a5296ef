import React, { useState, useEffect } from 'react';
import { i18n, Auth } from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard, StarryModal } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { Button, message, Space, Tag } from '@streamax/poppy';
import IconRequest from '@streamax/poppy-icons/lib/icons/IconRequest';
import {
    getSubPlatformDetail,
    setSubPlatformState,
    deleteSubPlatform,
} from '@/service/sub-platform';
import type { SubPlatformInfoListInfo } from '@/service/sub-platform';
import BaseInfo from './BaseInfo';
import { useLockFn } from '@streamax/hooks';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import './index.less';
import { PLATFORM_STATE } from '..';

const PlatformDetail = (props: any) => {
    const { platformCode } = props.location.query;
    const [platformItem, setPlatformItem] = useState<SubPlatformInfoListInfo>({});
    const history = useHistory();
    const goBackPage = () => {
        history.goBack();
    };
    
    const deletePlatformSure = useLockFn(async() => {
        await deleteSubPlatform({
            platformCode
        });
        message.success(i18n.t('message', '删除成功'));
        goBackPage();
    });
    const deletePlatform = async () => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确定要删除{platformName}平台？如果该平台已有车辆上报，删除平台后车组和车辆也将被移除', {
                platformName: platformItem.platformName
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                deletePlatformSure();
            }
        });
    };

    const loadData = async () => {
        const rs = await getSubPlatformDetail({ platformCode });
        if (rs && rs.platformCode) {
            setPlatformItem(rs || {});
        } else {
            message.error(i18n.t('message', '平台不存在'));
        }
    };

    useEffect(() => {
        loadData();
    }, []);


    // 车辆停启用
    const handleLockPlatform = () => {
        const checked = platformItem.enableState !== PLATFORM_STATE.ENABLE;
        const modal = StarryModal.confirm({
            centered: true,
            title: checked
                ? i18n.t('message', '启用确认')
                : i18n.t('message', '停用确认'),
            content: checked
                ? i18n.t('message', '确定要启用{platformName}平台？', {
                    platformName: platformItem.platformName
                })
                : i18n.t('message', '确定要停用{platformName}平台？停用后该平台不再继续上报车辆', {
                    platformName: platformItem.platformName
                }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: async () => {
                await setSubPlatformState({
                    platformCode: platformItem.platformCode,
                    enable: platformItem.enableState === PLATFORM_STATE.ENABLE ? PLATFORM_STATE.DISABLE : PLATFORM_STATE.ENABLE
                }).then(() => {
                    modal.destroy();
                    message.success(i18n.t('message', '操作成功'));
                    // @ts-ignore
                    loadData();
                });
            },
        });
    };

    return (
        <StarryBreadcrumb  className="sub-platform-manage-platform-detail">
            <StarryCard
                title={(
                    <div className="platform-detail-title">
                        <span className="platform-detail-title-text">
                            <OverflowEllipsisContainer>
                                {platformItem.platformName}
                            </OverflowEllipsisContainer>
                        </span>
                        
                        <Tag
                            className='platform-detail' 
                            color={platformItem.enableState === PLATFORM_STATE.ENABLE ? 'success' : 'default'}
                        >
                            {platformItem.enableState === PLATFORM_STATE.ENABLE
                                ? i18n.t('state', '启用')
                                : i18n.t('state', '停用')}
                        </Tag>
                    </div>
                )}
                className="driver-detail-card-layout"
                operation={
                    <Space>
                        <Auth code="@base:@page:sub.platform.manage:detail@action:enable.unable">
                            <Button onClick={handleLockPlatform}>
                                {platformItem.enableState === PLATFORM_STATE.ENABLE
                                    ? i18n.t('name', '停用')
                                    : i18n.t('name', '启用')}
                            </Button>
                        </Auth>
                        <Auth code="@base:@page:sub.platform.manage:detail@action:delete">
                            {platformItem.enableState === PLATFORM_STATE.DISABLE && ( // 停用的可删除
                                <Button
                                    danger
                                    onClick={() => {
                                        deletePlatform();
                                    }}
                                >
                                    {i18n.t('action', '删除')}
                                </Button>
                            )}
                        </Auth>
                    </Space>
                }
            >
                <BaseInfo platformItem={platformItem} onReload={loadData} />
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default PlatformDetail;