import { i18n, utils } from '@base-app/runtime-lib';
import { StarryInfoBlock, StarryModal, Action } from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import { Descriptions } from '@streamax/starry-components';
import { DescriptionItem } from '@streamax/poppy/lib/descriptions';
import './BaseInfo.less';
import type { SubPlatformInfoListInfo } from '@/service/sub-platform';

const { zeroTimeStampToFormatTime } = utils.formator;

export default (props: {
    platformItem: SubPlatformInfoListInfo;
}) => {
    const { platformItem } = props;

    const getDesItems = (): DescriptionItem[] => {
        const baseInfoList: DescriptionItem[] = [
            {
                label: i18n.t('name', '平台名称'),
                content: platformItem.platformName || '-',
            },
            {
                label: i18n.t('name', '平台编码'),
                content: platformItem.platformCode || '-',
            },
            {
                label: i18n.t('name', '平台IP'),
                content: platformItem.platformIp || '-',
            },
            {
                label: i18n.t('name', '平台端口'),
                content: platformItem.platformPort || '-',
            },
            {
                label: i18n.t('name', '链接方式'),
                content: platformItem.linkType || '-',
            },
            // {
            //     label: i18n.t('name', '创建人'),
            //     content: platformItem.createUser || '-',
            // },
            // {
            //     label: i18n.t('name', '创建时间'),
            //     content: platformItem.createTime ? zeroTimeStampToFormatTime(platformItem.createTime) : '-',
            // },
        ];
        return baseInfoList;
    };

    return (
        <StarryInfoBlock
            title={i18n.t('name', '基本信息')}
            operation={(
                <Action
                    code="@base:@page:sub.platform.manage:detail@action:edit"
                    url="/sub-platform-manage/edit-platform"
                    fellback={''}
                    params={{
                        platformCode: platformItem.platformCode,
                    }}
                >
                    {i18n.t('action', '编辑')}
                </Action>
            )}
        >
            <Descriptions>
                {
                    getDesItems().map(({ label, span, content }: DescriptionItem) => {
                        return (
                            <Descriptions.Item label={label} column={span}>
                                {content}
                            </Descriptions.Item>
                        );
                    })
                }
            </Descriptions>
        </StarryInfoBlock>
    );
};