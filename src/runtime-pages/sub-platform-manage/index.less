@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.base-data-platform-manage{
    .state-icon {
        display: inline-table;
        margin-top: -2px;
        vertical-align: middle;
        // display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 8px;
        background: @starry-text-color-placeholder;
        border-radius: 6px;
        &.active {
            background: @success-color;
        }
    }
    .state-text {
        position: relative;
        // top: -1px;
        margin-right: 8px;
        max-width: 50px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .poppy-switch-small {
        margin-top: -2px;
    }
    .list-icon.sync{
        width: auto !important;
        height: auto !important;
        padding: 0;
        &.disabled{
            color: @starry-text-color-disabled;
        }
    }
    .list-icon {
        color: @primary-color;
        cursor: pointer;
    }
    .platform-sync-status{
        display: flex;
        align-items: center;
    }
    .platform-sync-text{
        width: calc(100% - 22px);
    }
}