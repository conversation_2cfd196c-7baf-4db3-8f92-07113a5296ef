import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
import { searchSubPlatformList, deleteSubPlatform, syncSubPlatform, setSubPlatformState } from '@/service/sub-platform';
import { useDebounceFn, useLockFn, useSubmitFn } from '@streamax/hooks';
import {
    Button,
    Form,
    Input,
    message,
    Select,
    Space,
    Table,
    Tooltip,
    Switch,
    Badge,
} from '@streamax/poppy';
import { IconAdd, IconDelete, IconEdit, IconRequest, IconRetry } from '@streamax/poppy-icons';
import { Auth, i18n, useUrlSearchStore, utils,useSystemComponentStyle } from '@base-app/runtime-lib';
import classNames from 'classnames';
import {
    StarryBreadcrumb,
    StarryCard,
    StarryModal,
    Action,
} from '@base-app/runtime-lib';
import { Link, useHistory } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    OverflowEllipsisContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import type { ListDataContainerProps } from '@streamax/starry-components/lib/list-data-container';
import { useEffect, useRef, useState } from 'react';
import './index.less';
import { SubPlatformInfoListInfo, SubPlatformQueryParams } from '@/service/sub-platform';
import { isNil } from 'lodash';


export const PLATFORM_STATE = {
    ENABLE: 1,
    DISABLE: 0,
};

export enum SYNC_STATE {
    SYNC_ING,
    SUCCESS,
    FAIL,
    NOT_SYNC
}
const SubPlatformManage = () => {
    const { getSystemColor } = useSystemComponentStyle()
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const { pageSize, page, enableState, syncState, keyword } = searchStore.get();
    const containerRef = useRef<any>();
    const history = useHistory();
    // 同步中的状态
    const [syncing, setSyncing] = useState<boolean>(false);
    
    const PLATFORM_SYNC_STATE = {
        [SYNC_STATE.SYNC_ING]: {
            text: i18n.t('state', '同步中'),
            color: getSystemColor('WARN'),
        },
        [SYNC_STATE.SUCCESS]: {
            text: i18n.t('state', '成功'),
            color: getSystemColor('SUCCESS'),
        },
        [SYNC_STATE.FAIL]: {
            text: i18n.t('state', '失败'),
            color: getSystemColor('FAIL'),
        },
        [SYNC_STATE.NOT_SYNC]: {
            text: i18n.t('state', '未同步'),
            color: getSystemColor('DEFAULT'),
        }
    };

    useEffect(() => {
        form.setFieldsValue({
            page,
            pageSize,
            enableState: Number(enableState) === 0 ? 0 : (Number(enableState) || undefined),
            syncState: Number(syncState) || undefined,
            keyword: keyword || undefined
        });
        containerRef.current?.loadDataSource();
    }, []);

    const enableStateMap = [
        { label: i18n.t('state', '启用'), value: 1 },
        { label: i18n.t('state', '停用'), value: 0 }
    ];

    const syncStateMap = [
        { label: i18n.t('state', '成功'), value: 1 },
        { label: i18n.t('state', '失败'), value: 2 }
    ];

    const queryForm: ListDataContainerProps['queryForm'] = {
        items: [
            {
                label: i18n.t('name', '平台'),
                name: 'keyword',
                field: Input,
                fieldProps: {
                    placeholder: i18n.t('message', '请输入平台名称或平台编码'),
                    maxLength: 50,
                    allowClear: true,
                },
                itemProps: {
                    rules: [{ max: 50, type: 'string' }],
                },
            },
            {
                label: i18n.t('name', '平台状态'),
                name: 'enableState',
                field: Select,
                fieldProps: {
                    placeholder: i18n.t('message', '请选择平台状态'),
                    options: enableStateMap,
                    allowClear: true,
                },
            },
            {
                label: i18n.t('name', '同步状态'),
                name: 'syncState',
                field: Select,
                fieldProps: {
                    placeholder: i18n.t('message', '请选择同步状态'),
                    options: syncStateMap,
                    allowClear: true,
                },
            },
        ],
        form
    };
    const fetchData = async (params: SubPlatformQueryParams) => {
        searchStore.set(params);
        params.keyword = params.keyword?.trim() ? encodeURIComponent(params.keyword?.trim()) : undefined;
        const data = await searchSubPlatformList({
            ...params,
        });
        return data;
    };

    const onOkLock = useLockFn(async (modal, row, checked) => {
        await setSubPlatformState({
            platformCode: row.platformCode,
            enable: checked ? PLATFORM_STATE.ENABLE : PLATFORM_STATE.DISABLE,
        }).then(() => {
            message.success(i18n.t('message', '操作成功'));
        }).finally(() => {
            modal.destroy();
            // @ts-ignore
            containerRef.current?.loadDataSource();
        });
    });

    const handleLockPlatform = (checked: boolean, row: any) => {
        const modal = StarryModal.confirm({
            centered: true,
            title: checked
                ? i18n.t('message', '启用确认')
                : i18n.t('message', '停用确认'),
            content: checked
                ? i18n.t('message', '确认要启用“{platformName}”平台吗？', {
                    platformName: row.platformName,
                })
                : i18n.t('message', '确认要停用“{platformName}”平台吗？停用后该平台不再继续上报车辆', {
                    platformName: row.platformName,
                }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                onOkLock(modal, row, checked);
            },
        });
    };

    // 同步
    const [syncPlatform, loading] = useSubmitFn(async (record: SubPlatformInfoListInfo) => {
        if (loading || record.syncState === SYNC_STATE.SYNC_ING) {
            return;
        }
        try {
            const rs = await syncSubPlatform({platformCode: record.platformCode});
            if (rs) {
                message.success(i18n.t('message', '操作成功'));
            } else {
                message.error(i18n.t('message', '操作失败'));
            }
        } finally{
            containerRef.current?.loadDataSource();
        }
    });

    const columns = [
        {
            title: i18n.t('name', '平台名称'),
            dataIndex: 'platformName',
            render: (text: string, record: SubPlatformInfoListInfo) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Action
                            code="@base:@page:sub.platform.manage@action:detail"
                            url="/sub-platform-manage/platform-detail"
                            fellback={text}
                            params={{
                                platformCode: record.platformCode,
                            }}
                        >
                            {text}
                        </Action>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '平台编码'),
            dataIndex: 'platformCode',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '平台状态'),
            dataIndex: 'enableState',
            ellipsis: true,
            width: 150,
            render: (text: number, row: any) => {
                return (
                    <div className="platform-state">
                        <span
                            className={classNames('state-icon', {
                                active: text === PLATFORM_STATE.ENABLE,
                            })}
                        />
                        <span className="state-text">
                            {text === PLATFORM_STATE.ENABLE
                                ? i18n.t('state', '启用')
                                : i18n.t('state', '停用')}
                        </span>
                        {
                            <Auth code="@base:@page:sub.platform.manage@action:enable.unable">
                                <Switch
                                    size="small"
                                    checked={text === PLATFORM_STATE.ENABLE}
                                    onChange={(checked: boolean) => handleLockPlatform(checked, row)}
                                />
                            </Auth>
                        }
                    </div>
                );
            }
        },
        {
            title: i18n.t('name', '平台IP'),
            dataIndex: 'platformIp',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '平台端口'),
            dataIndex: 'platformPort',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '链接方式'),
            dataIndex: 'linkType',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '同步状态'),
            dataIndex: 'syncState',
            render: (text: string, record: SubPlatformInfoListInfo) => {
                return (
                    <span className="platform-sync-status">
                        <Badge color={PLATFORM_SYNC_STATE[record.syncState]?.color}/>
                        <span className="platform-sync-text">
                            <StarryAbroadOverflowEllipsisContainer>
                                {!isNil(record.syncState) ? PLATFORM_SYNC_STATE[record.syncState]?.text : i18n.t('state', '未开始')}
                            </StarryAbroadOverflowEllipsisContainer>
                        </span>
                    </span>
                );
            },
        },
        {
            title: i18n.t('name', '同步时间'),
            dataIndex: 'syncTime',
            key: 'syncTime',
            render: (text: any, record) => {
                const timeInfo = (
                    <Tooltip title={text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
                // 同步中的状态同步时间展示‘-’，成功和失败展示具体时间
                return record.syncState === SYNC_STATE.SYNC_ING ? '-' : timeInfo;
            },
        },
        {
            title: i18n.t('name', '操作'),
            key: 'action',
            width: 120,
            render: (_: any, record: SubPlatformInfoListInfo) => {
                return (
                    <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Action
                            code="@base:@page:sub.platform.manage@action:edit"
                            url="/sub-platform-manage/edit-platform"
                            fellback={''}
                            params={{
                                platformCode: record.platformCode,
                            }}
                        >
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <IconEdit />
                            </Tooltip>
                        </Action>
                        {record.enableState === PLATFORM_STATE.ENABLE && (
                            <Auth code="@base:@page:sub.platform.manage@action:sync">
                                <Tooltip title={i18n.t('action', '同步')}>
                                    <span 
                                        className={classNames('list-icon sync', {
                                            disabled: loading || record.syncState === SYNC_STATE.SYNC_ING,
                                        })}
                                        onClick={() => syncPlatform(record)}>
                                        <IconRetry />
                                    </span>
                                </Tooltip>
                            </Auth>
                        )}
                        {record.enableState === PLATFORM_STATE.DISABLE && (
                            <Auth code="@base:@page:sub.platform.manage@action:delete">
                                <Tooltip title={i18n.t('action', '删除')}>
                                    <IconDelete
                                        className="list-icon"
                                        onClick={() => delSubPlatform(record)}
                                    />
                                </Tooltip>
                            </Auth>
                        )}
                    </Space>
                );
            },
        },
    ];
    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(columns, {
        storageKey: '@base:@page:sub.platform.manage',
        disabledKeys: ['platformName', 'action'],
    });

    // 删除
    const delSubPlatform = (record: SubPlatformInfoListInfo) => {
        const content = i18n.t('message', '确认要删除“{platformName}”平台？如果该平台已有车辆上报，删除平台后车组和车辆也将被移除', {
            platformName: record.platformName
        });
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            closable: true,
            icon: <IconRequest />,
            onOk: async () => {
                try {
                    await deleteSubPlatform({platformCode: record.platformCode});
                    message.success(i18n.t('message', '操作成功'));
                } finally {
                    containerRef.current?.loadDataSource();
                    modal.destroy();
                }
            },
        });
    };
    const toolbar = {
        extraLeft: (
            <>
                <Action
                    code="@base:@page:sub.platform.manage@action:add"
                    url="/sub-platform-manage/add-platform"
                    fellback={''}
                >
                    <Button type="primary" icon={<IconAdd />}>
                        {i18n.t('action', '添加')}
                    </Button>
                </Action>
            </>
        ),
        extraIconBtns: [<TableColumnSetting {...tableColumnSettingProps} />],
    };
    return (
        <StarryBreadcrumb className="base-data-platform-manage">
            <StarryCard>
                <ListDataContainer
                    queryForm={queryForm}
                    loadDataSourceOnMount={false}
                    getDataSource={fetchData}
                    pagination={{
                        defaultCurrent: Number(page) || 1,
                        defaultPageSize: Number(pageSize) || 20,
                    }}
                    ref={containerRef}
                    toolbar={toolbar}
                    listRender={(data) => {
                        return (
                            <Table
                                columns={tableColumns}
                                dataSource={data}
                                pagination={false}
                                rowKey="platformCode"
                            />
                        );
                    }}
                />
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default SubPlatformManage;
