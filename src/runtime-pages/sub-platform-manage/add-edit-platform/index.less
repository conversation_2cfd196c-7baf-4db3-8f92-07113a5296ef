@import '~@streamax/poppy-themes/starry/index.less';
.platform-create-container {
    .platform-create-header {
        margin: 8px 0 24px;
    }
    &-title {
        padding-bottom: 16px;
        font-weight: 600;
        font-size: 20px;
        &::abroad {
            font-size: 18px;
        }
    }
    .platform-create-body {
        // 消除最后一个FormItem的margin-bottom
        .poppy-row:last-of-type::abroad {
            .poppy-col {
                .poppy-form-item {
                    margin-bottom: 0;
                }
            }
        }
        .poppy-col-6::abroad {
            flex: 1;
            max-width: unset;
        }

        .poppy-radio-group,
        .poppy-checkbox-group {
            .poppy-radio-wrapper,
            .poppy-checkbox-wrapper {
                min-width: 100px;
                margin-right: 8px;
            }
        }
    }
    .poppy-steps-horizontal {
        display: block;
        .poppy-steps-item {
            min-width: 300px;
            max-width: 500px;
            .poppy-steps-item-content {
                max-width: 80%;
                .poppy-steps-item-title {
                    width: 100%;
                }
            }
        }
    }
}
.base-809-starry-card::abroad {
    .starry-card-layout-header {
        padding-top: 0;
    }
}
.platform-create-footer::abroad {
    margin-top: 0;
}
