import { useRef, useState } from 'react';
import {
    Form,
    Input,
    Button,
    Space,
    message,
    Row,
    Col,
    Radio,
    Select,
    Divider,
    Steps,
    Checkbox,
    InputNumber,
    Container,
} from '@streamax/poppy';
import { useAsyncEffect, useLockFn, useSubmitFn } from '@streamax/hooks';
import { StarryBreadcrumb, StarryCard, StarryInfoBlock } from '@base-app/runtime-lib';
import {
    StarryAbroadFormItem,
    useSystemComponentStyle,
    StarryAbroadLRLayout,
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { utils, i18n } from '@base-app/runtime-lib';
import { Rule } from '@streamax/poppy/lib/form';
// import { platformNameExist, platformExist } from '@/service/base-service-809/platform';
import {
    getSubPlatformDetail,
    createSubPlatform,
    updateSubPlatform,
} from '@/service/sub-platform';
import { validIPAddress, checkFieldSpace } from '@/utils/commonFun';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

interface FieldItem {
    label: string;
    name: string | (string | number)[];
    validateTrigger?: string | string[];
    fieldKey?: (string | number)[];
    show?: boolean;
    rules?: Rule[];
    render: () => JSX.Element;
}

const AddOrEditPlatform = (props: any) => {
    const { platformCode } = props.location.query;
    const [form] = Form.useForm();
    const history: any = useHistory();
    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();

    useAsyncEffect(async () => {
        if (platformCode) {
            const rs = await getSubPlatformDetail({ platformCode });
            if (rs && rs.platformCode) {
                form.setFieldsValue(rs);
            } else {
                message.error(i18n.t('message', '平台不存在'));
            }
        }
    }, []);

    const platformIdValidator = async (rule: Rule, value: string) => {
        if (value) {
            if (!platformCode) {
                const rs = await getSubPlatformDetail({
                    platformCode: value,
                });
                if (rs?.platformCode) {
                    return Promise.reject(i18n.t('message', '平台编码重复'));
                }
            }
        }
        return Promise.resolve();
    };

    const platformCodeNumberValidator = async (rule: Rule, value: string) => {
        if (value) {
            if (!/^\d{1,20}$/.test(value)) {
                return Promise.reject(i18n.t('message', '请输入1-20位数字'));
            }
        }
        return Promise.resolve();
    };

    const govIpValidator = (rule: Rule, value: string) => {
        if (!value || validIPAddress(value)) {
            return Promise.resolve();
        } else {
            return Promise.reject(i18n.t('message', '请输入正确的IP'));
        }
    };

    const govPortValidator = (rule: Rule, value: string) => {
        if (!value || (/^[1-9]\d{0,8}$/.test(value) && Number(value) <= 65535)) {
            return Promise.resolve();
        } else {
            return Promise.reject(i18n.t('message', '请输入正确的端口'));
        }
    };

    const [saveBaseInfo, saveInfoLoading] = useSubmitFn(async () => {
        try {
            const values = await form.validateFields();
            const {
                platformName,
                platformCode: newPlatformCode,
                platformIp,
                platformPort,
                linkType,
            } = values;
            const params = {
                platformPort: Number(platformPort),
                platformCode: newPlatformCode?.trim(),
                platformName: platformName?.trim(),
                linkType,
                platformIp,
            };
            if (platformCode) {
                await updateSubPlatform(params);
            } else {
                await createSubPlatform(params);
            }
            message.success(i18n.t('message', '操作成功'));
            goBackPage();
        } catch (e) {
            console.warn(e);
        }
        return null;
    });

    const goBackPage = () => {
        history.goBack();
    };

    const renderFn = ({ field: Field = Input, children, ...fieldProps }: any) => {
        const defaultProps = {
            allowClear: true,
            maxLength: 50,
            ...fieldProps,
        };
        return <Field {...defaultProps}>{children}</Field>;
    };

    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '平台名称不能为空'));
    };

    const baseFieldItemList: FieldItem[] = [
        {
            label: i18n.t('name', '平台名称'),
            name: 'platformName',
            rules: [
                {
                    required: true,
                    validator: checkSpaceName,
                },
                {
                    min: 1,
                    type: 'string',
                },
                {
                    max: 200,
                    type: 'string',
                },
                {
                    validator: utils.validator.illegalCharacter,
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入平台名称'),
                    maxLength: 200,
                }),
        },
        {
            label: i18n.t('name', '平台编码'),
            name: 'platformCode',
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台编码'),
                    validateTrigger: ['onChange', 'onBlur'],
                },
                {
                    validator: platformCodeNumberValidator,
                    validateTrigger: ['onChange', 'onBlur'],
                },
                {
                    validator: platformIdValidator,
                    validateTrigger: 'onBlur',
                },
            ],
            render: () =>
                renderFn({
                    maxLength: 20,
                    placeholder: i18n.t('message', '请输入平台编码'),
                    disabled: !!platformCode,
                }),
        },
        {
            label: i18n.t('name', '平台IP'),
            name: 'platformIp',
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台IP'),
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入平台IP'),
                }),
        },
        {
            label: i18n.t('name', '平台端口'),
            name: 'platformPort',
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台端口'),
                },
                {
                    validator: govPortValidator,
                },
            ],
            render: () =>
                renderFn({
                    field: InputNumber,
                    placeholder: i18n.t('message', '请输入平台端口'),
                    min: 1,
                    max: 65535,
                    precision: 0,
                    step: 1,
                }),
        },
        {
            label: i18n.t('name', '链接方式'),
            name: 'linkType',
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请选择链接方式'),
                },
            ],
            initialValue: 'TCP',
            render: () =>
                renderFn({
                    field: Radio.Group,
                    options: [
                        {
                            label: i18n.t('name', 'TCP'),
                            value: 'TCP',
                        },
                        {
                            label: i18n.t('name', 'UDP'),
                            value: 'UDP',
                        },
                    ],
                }),
        },
    ];

    const renderColList = (colList: FieldItem[], span = 6) => {
        return colList.map((item: FieldItem) => {
            const { render, ...fieldProps } = item;
            return (
                <Col span={span}>
                    <StarryAbroadFormItem {...fieldProps}>{render()}</StarryAbroadFormItem>
                </Col>
            );
        });
    };

    const renderRowList = (renderFieldList: FieldItem[], col = isAbroadStyle ? 1 : 3, span = 6) => {
        const rowList: FieldItem[][] = [];
        while (renderFieldList.length) {
            rowList.push(renderFieldList.splice(0, col));
        }
        return rowList.map((colList: FieldItem[]) => {
            return <Row gutter={isAbroadStyle ? 48 : 80}>{renderColList(colList, span)}</Row>;
        });
    };

    return (
        <StarryBreadcrumb>
            <StarryCard className="base-809-starry-card">
                <Container className="platform-create-container">
                    <div className="platform-create-container-title">
                        {i18n.t('name', '基本信息')}
                    </div>
                    <div className="platform-create-body">
                        <Form
                            layout="vertical"
                            form={form}
                            initialValues={{
                                linkType: 'TCP',
                            }}
                        >
                            {renderRowList(baseFieldItemList)}
                        </Form>
                    </div>
                </Container>
                <div className="platform-create-footer">
                    <StarryAbroadLRLayout>
                        <Button className="btn-control" onClick={goBackPage}>
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            className="btn-control"
                            type="primary"
                            onClick={saveBaseInfo}
                            loading={saveInfoLoading}
                        >
                            {i18n.t('action', '保存')}
                        </Button>
                    </StarryAbroadLRLayout>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default AddOrEditPlatform;
