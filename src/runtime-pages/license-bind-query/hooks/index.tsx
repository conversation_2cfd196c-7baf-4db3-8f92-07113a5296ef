import { useMemoizedFn, useRequest } from "ahooks";
import { defaultOption, licenseTypeOptions } from "../constants";
import { fetchLicenseSetsList, fetchLicenseVehicleStorageList } from "@/service/license";
import { LICENSE_STATE_OPTIONS } from "@/utils/license-common";

export const useLicenseOptions = () => {
	const { data: licenseSetsLIst=[] } = useRequest(fetchLicenseSetsList);
    const { data: licenseVehicleStorageList = [] } = useRequest(fetchLicenseVehicleStorageList);
    
    const licenseSetsOptionsFn = useMemoizedFn(() => {
        const result = licenseSetsLIst
            ?.map(({ setsCode, setsName }) => ({ label: setsName, value: setsCode })).filter(item=>Boolean(item.value));
        result.push(defaultOption);
        return result;
    }
    );
	
    const licenseVehicleStorageOptionsFn = useMemoizedFn(() => {
        const result = licenseVehicleStorageList?.filter(item=>item.vehicleStorageSum !=="0")?.map(({ vehicleStorageSum }) => ({
            label: `${vehicleStorageSum}GB`,
            value: vehicleStorageSum,
        }));
        result.push(defaultOption);
        return result;
    });

    return {
        licenseStateOptions:LICENSE_STATE_OPTIONS,
        licenseTypeOptions,
        licenseSetsOptions:licenseSetsOptionsFn(),
        licenseVehicleStorageOptions: licenseVehicleStorageOptionsFn()
    } as const;
};