import { GroupItem } from '../group-manage/types';
import { i18n } from '@base-app/runtime-lib';
export interface VehicleItem {
    createTime: number;
    state: 1 | 2;
    vehicleId: number;
    vehicleNo: string;
    vehicleNumber: string;
    vehicleType: number;
    channelNum: number;
    channelNo?: string;
    devNo?: string;
    devId: number;
    updateUser: string;
    vehicleColor?: number;
    companyList?: GroupItem[];
}

export const VEHICLE_STATE = {
    START: 1,
    STOP: 2,
};
export const vehicleStates = () => [
    {
        label: i18n.t('state', '启用'),
        value: '1',
    },
    {
        label: i18n.t('state', '停用'),
        value: '2',
    },
];
