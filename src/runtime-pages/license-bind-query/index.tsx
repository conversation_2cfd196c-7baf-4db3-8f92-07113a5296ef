import { useDebounceFn, useLockFn, useSubmitFn } from '@streamax/hooks';
import { Input, Space, message, Form, Tooltip, Select, Button } from '@streamax/poppy';
// @ts-ignore
import type { QueryFormProps } from '@streamax/poppy/lib/query-form';
import type { ColumnsType, TableProps } from '@streamax/poppy/lib/table';
import {
    i18n,
    Auth,
    utils,
    useUrlSearchStore,
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadFormItem,
} from '@base-app/runtime-lib';
import {
    StarryModal,
    StarryBreadcrumb,
    StarryCard,
    StarryTable,
    FleetTreeSelect,
    Action,
} from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { useEffect, useState, useRef, Key, useMemo } from 'react';
import AuthFleetShow from '@/components/AuthFleetShow';
import './index.less';
import DateRange from '@/components/DateRange';
import moment from 'moment';
import { IconExport, IconSettlementFill } from '@streamax/poppy-icons';
import { exportExcel } from '@/service/import-export';
import {
    LICENSE_TYPE,
    LICENSE_STATE_OPTIONS,
    LICENSE_TYPE_OBJECT,
    LICENSE_STATE_OBJECT,
    LICENSE_STATE_ENUM,
    EXPIRED_DAY,
    VALID_TIME_TYPE_ENUM,
    storageSwitchOpen,
} from '@/utils/license-common';
import { queryLicenseDeviceBindPage, bindLicenseByType, bindLicenseByTypeCustomHandle } from '@/service/license';
import LicenseTypeSelect, { LicenseSelectRefProps } from '@/components/LicenseTypeSelect';
import { getCustomItems, getCustomJsx, getTableIconBtns, runCustomFun } from '@/utils/pageReuse';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { ListPageTableBase, ListPageQueryForm, Instances } from '@/types/pageReuse/pageReuseBase';
import { useLicenseOptions } from './hooks';
import InfoBack from '@/components/InfoBack';
import { useResetState } from 'ahooks';
import { isNil } from 'lodash';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
const { getLocalMomentByZeroTimeStamp } = utils.formator;
type LicenseBindQueryShareProps = ListPageTableBase & ListPageQueryForm & Instances;
const LicenseBindQuery = (props: LicenseBindQueryShareProps) => {
    /******定制项******/
    const {
        getColumnSetting,
        getColumns,
        getTableLeftRender,
        getIconBtns,
        injectSearchList,
        getQueryForm,
        getInstances,
    } = props;
    /********end********/
    const tableRef = useRef<any>();
    const [form] = Form.useForm();
    const [modalForm] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const [currentVehicle, setCurrentVehicle, resetCurrentVehicle] = useResetState<any[]>([]);
    const [isBath, setIsBath, resetIsBath] = useResetState<boolean>(false);
    const [currentLicense, setCurrentLicense] = useState<any>({});
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
    const [lastQueryParams, setLastQueryParams] = useState({});
    const licenseTypeSelectRef = useRef<LicenseSelectRefProps>(null);
    const cacheSelectedRowsRef = useRef<any[]>([]);
    const {
        licenseStateOptions,
        licenseTypeOptions,
        licenseSetsOptions,
        licenseVehicleStorageOptions,
    } = useLicenseOptions();
    const {
        pageSize,
        page,
        fleetId,
        includeSubFleet,
        expireStartTime,
        expireEndTime,
        licenseTypes,
        ...formQuery
    } = searchStore.get();
    useEffect(() => {
        const searchList = {
            ...formQuery,
            page,
            pageSize,
            timeRange: expireStartTime
                ? [
                      getLocalMomentByZeroTimeStamp(expireStartTime),
                      getLocalMomentByZeroTimeStamp(expireEndTime),
                  ]
                : undefined,
            company: {
                fleetIds: fleetId,
                includeSubFleet: (includeSubFleet && Number(includeSubFleet)) ?? 1,
            },
            licenseTypes: isNil(licenseTypes) ? undefined : Number(licenseTypes),
        };
        form.setFieldsValue(searchList);
    }, []);

    const fetchData = (params: any) => {
        const { timeRange, vehicleNumber, licenseStatus, company, complexSort, ...restParams } =
            params;
        const searchStoreParams = {
            ...restParams,
            vehicleNumber: vehicleNumber?.trim(),
            fleetId: company?.fleetIds || company?.fleetId,
            includeSubFleet: company?.includeSubFleet ?? 1,
            licenseStatus: licenseStatus === LICENSE_STATE_ENUM.DUE ? null : licenseStatus,
            aboutToExpireTime:
                licenseStatus === LICENSE_STATE_ENUM.DUE
                    ? utils.formator.timestampToZeroTimeStamp(
                          moment().subtract(-EXPIRED_DAY, 'days'),
                      )
                    : null,
            fields: 'device,vehicle',
            complexSort: complexSort
                ? `orderBy validTimeType ${
                      complexSort?.indexOf('desc') > -1 ? 'asc' : 'desc'
                  },expireTime ${complexSort?.indexOf('desc') > -1 ? 'desc' : 'asc'}`
                : '',
            bindingStatus: 1
        };
        if (timeRange?.[0]) {
            searchStoreParams.expireStartTime = utils.formator.timestampToZeroTimeStamp(
                timeRange[0].startOf('day'),
            );
            searchStoreParams.expireEndTime = utils.formator.timestampToZeroTimeStamp(
                timeRange[1].endOf('day'),
            );
        }
        searchStore.set({ ...searchStoreParams, complexSort, licenseStatus });
        setLastQueryParams(searchStoreParams);
        setSelectedRowKeys([]);
        resetCurrentVehicle();
        resetIsBath();
        if (injectSearchList) {
            return injectSearchList(searchStoreParams);
        }
        return queryLicenseDeviceBindPage(searchStoreParams);
    };

    const renewal = (row: any) => {
        setCurrentVehicle([row]);
        modalForm.setFieldsValue({
            vehicleNumber: row.vehicleInfo?.vehicleNumber,
            licenseType: row.licenseType.toString(),
        });
        setModalVisible(true);
        setIsBath(false);
    };
    const showRenewal = (record: any) => {
        if (
            record.validTimeType == VALID_TIME_TYPE_ENUM.LIMITED &&
            (record.licenseStatus == LICENSE_STATE_ENUM.INUSE ||
                record.licenseStatus == LICENSE_STATE_ENUM.EXPIRED)
        ) {
            return true;
        }
        return false;
    };
    const renderTime = (row: any) => {
        // 待使用的都不展示时间
        if (row.validTimeType == VALID_TIME_TYPE_ENUM.BUYOUT) {
            return i18n.t('name', '永久有效');
        }
        const time = row.expireTime;
        return (
            <OverflowEllipsisContainer>
                {time && time != 0 && time != -1
                    ? utils.formator.zeroTimeStampToFormatTime(time)
                    : '-'}
            </OverflowEllipsisContainer>
        );
    };
    const columns: ColumnsType = [
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'licenseId',
            key: 'licenseId',
            ellipsis: { showTitle: false },
            render: (text: string, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {row.vehicleInfo?.vehicleId ? (
                            <Action
                                code="@base:@page:license.bind.query@action:vehicle.detail"
                                url="/vehicle-manage/vehicle-detail"
                                fellback={row.vehicleInfo?.vehicleNumber || '-'}
                                params={{
                                    vehicleId: row.vehicleInfo?.vehicleId,
                                    activeKey: 'ability',
                                }}
                            >
                                {row.vehicleInfo?.vehicleNumber || '-'}
                            </Action>
                        ) : (
                            row.vehicleInfo?.vehicleNumber || '-'
                        )}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '车辆设备'),
            dataIndex: 'deviceInfo',
            key: 'deviceInfo',
            ellipsis: { showTitle: false },
            render: (text, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>{text?.deviceNo || '-'}</StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'vehicleInfo',
            key: 'vehicleInfo',
            render: (text, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={text?.fleetList} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', 'License类型'),
            dataIndex: 'licenseType',
            key: 'licenseType',
            ellipsis: { showTitle: false },
            render: (text: string) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {LICENSE_TYPE_OBJECT[text] || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '额外开通套餐'),
            dataIndex: 'setsName',
            key: 'setsName',
        },
        // @ts-ignore
        storageSwitchOpen()
            ? {
                  title: i18n.t('name', '单车存储空间'),
                  dataIndex: 'storageSum',
                  key: 'storageSum',
                  ellipsis: { showTitle: false },
                render: (text: number) => {
                    const data = Number(text || "0");
                      return data ? `${data}GB` : '-';
                  },
              }
            : null,
        {
            title: i18n.t('name', 'License状态'),
            dataIndex: 'licenseStatus',
            width: 150,
            render: (text: string, row: any) => {
                let status: string = text;
                // 正在使用中，且剩余时间小于30天时，展示即将到期
                if (
                    status == LICENSE_STATE_ENUM.INUSE &&
                    row.validTimeType == VALID_TIME_TYPE_ENUM.LIMITED &&
                    row.remainder < EXPIRED_DAY
                ) {
                    status = EXPIRED_DAY;
                }
                return (
                    <div className="vehicle-state">
                        <span className={`state-icon state-icon-${status}`} />
                        <StarryAbroadOverflowEllipsisContainer>
                            <span className="state-text">{LICENSE_STATE_OBJECT[status]}</span>
                        </StarryAbroadOverflowEllipsisContainer>
                    </div>
                );
            },
        },
        {
            title: i18n.t('name', '到期时间'),
            dataIndex: 'expireTime',
            key: 'expireTime',
            sorter: true,
            defaultSortOrder:
                searchStore.get().complexSort &&
                (searchStore.get().complexSort == 'orderBy validTimeType asc'
                    ? 'ascend'
                    : 'descend'),
            render: (text: any, record) => {
                return <StarryAbroadOverflowEllipsisContainer>{renderTime(record)}</StarryAbroadOverflowEllipsisContainer>;
            },
        },
        {
            title: i18n.t('name', '操作'),
            key: 'action',
            width: 100,
            fixed: 'right',
            render: (item: any, row: any) => (
                <Space size={24}>
                    <Auth code="@base:@page:license.bind.query@action:vehicle.renewal">
                        {showRenewal(row) && (
                            <Tooltip title={i18n.t('action', '续期')}>
                                <a
                                    onClick={() => {
                                        renewal(row);
                                    }}
                                    className='renewal-icon'
                                >
                                    <IconSettlementFill />
                                </a>
                            </Tooltip>
                        )}
                    </Auth>
                </Space>
            ),
        },
    ];
    const filterColumns = getCustomItems(
        getColumns,
        columns.filter((item) => item),
    );

    const queryBindItems: QueryFormProps['items'] = [
        {
            label: i18n.t('name', '归属车组'),
            name: 'company',
            field: FleetTreeSelect,
            fieldProps: {
            },
        },
        {
            label: i18n.t('name', '车辆'),
            name: 'vehicleNumber',
            field: Input,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入车牌号码'),
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', 'License状态'),
            name: 'licenseStatus',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择License状态'),
                options: licenseStateOptions,
            },
        },
        {
            label: i18n.t('name', '到期时间'),
            name: 'timeRange',
            colSize: 2,
            field: DateRange,
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    allowClear: true,
                    ranges: getPickerRangeTWM(),
                    style: {
                        width: '100%',
                    },
                },
            },
        },
        {
            label: i18n.t('name', 'License类型'),
            name: 'licenseTypes',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择License类型'),
                options: licenseTypeOptions,
            },
        },
        {
            label: i18n.t('name', '额外开通套餐'),
            name: 'setsCode',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择额外开通套餐'),
                options: licenseSetsOptions,
            },
        },
        storageSwitchOpen()
            ? {
                  label: i18n.t('name', '单车存储空间'),
                  name: 'vehicleStorageSum',
                  field: Select,
                  fieldProps: {
                      allowClear: true,
                      placeholder: i18n.t('message', '请选择单车存储空间'),
                      options: licenseVehicleStorageOptions,
                  },
              }
            : null,
    ].filter(Boolean);
    const queryFormItems: QueryFormProps['items'] | any[] = getCustomItems(
        getQueryForm,
        queryBindItems,
    );

    // 导出数据
    const _exportData = () => {
        const excelIndex = storageSwitchOpen() ? 5 : 4; //存储套餐没开启时列数有变化
        const headersArr = [
            {
                columnName: 'vehicleNumber',
                title: i18n.t('name', '车牌号码'),
                index: 0,
            },
            {
                columnName: 'deviceNo',
                title: i18n.t('name', '车辆设备'),
                index: 1,
            },
            {
                columnName: 'fleetName',
                title: i18n.t('name', '归属车组'),
                index: 2,
            },
            {
                columnName: 'licenseType',
                title: i18n.t('name', 'License类型'),
                index: 3,
            },
            {
                columnName: 'setsName',
                title: i18n.t('name', '额外开通套餐'),
                index: 4,
            },
            storageSwitchOpen()
                ? {
                      columnName: 'storageSum',
                      title: i18n.t('name', '单车存储空间'),
                      index: excelIndex,
                  }
                : null,
            {
                columnName: 'licenseStatus',
                title: i18n.t('name', 'License状态'),
                index: excelIndex + 1,
            },
            {
                columnName: 'expireTime',
                title: i18n.t('name', '到期时间'),
                index: excelIndex + 2,
            },
        ];
        const filterHeadersArr = headersArr.filter((item) => item);
        const sheetArr = [
            {
                sheetName: i18n.t('name', 'licenseRecord'),
                excelHeaders: filterHeadersArr,
                queryParam: {
                    param: {
                        ...lastQueryParams,
                        pageSize: Number.MAX_SAFE_INTEGER,
                    },
                },
            },
        ];
        exportExcel({
            executorHandler: 'bindingLicenseExport',
            serviceName: 'base-server-service',
            isAsync: true,
            excelType: 'XLSX',
            fileName: i18n.t('name', '车辆License绑定记录') + `_${moment().unix()}`,
            sheetQueryParams: sheetArr,
            taskType: 1518,
        }).then(() => {
            message.success(i18n.t('message', '导出成功，请到个人中心中查看导出详情'));
        });
    };
    const { run: exportData } = useDebounceFn(_exportData, {
        wait: 500,
    });
    const [submit, submitLoading] = useSubmitFn(async () => {
        await modalForm.validateFields();
        const { licenseType, licenseStatus, storageSum, setsCode, remainder } =
            currentLicense || {};
        const bindParams = {
            deviceIdList: selectDeviceDuplicatesList,
            licenseList: [
                {
                    licenseType,
                    licenseStatus,
                    remainder,
                    storageSum,
                    setsCode,
                },
            ],
        };
        const { code } = await bindLicenseByTypeCustomHandle(bindParams);
        if ([*********, *********].includes(code)) {
            modalForm.setFieldsValue({
                setsCode: undefined,
            });
            licenseTypeSelectRef.current?.reload();
            return;
        }
        setModalVisible?.(false);
        message.success(i18n.t('message', '操作成功'));
        tableRef.current?.loadDataSource();
        modalForm.resetFields();
    });
    runCustomFun(getInstances, {
        form,
        table: tableRef.current,
    });

    const selectDeviceDuplicatesList = useMemo(() => {
        return Array.from(new Set(currentVehicle?.map((item) => item?.deviceInfo?.deviceId)));
    }, [currentVehicle]);

    const rowSelection: TableProps['rowSelection'] = {
        selectedRowKeys,
        onChange: (selectedKeys, selectedRows) => {
            setSelectedRowKeys(selectedKeys);
            cacheSelectedRowsRef.current = selectedRows;
        },
        getCheckboxProps: (record) => ({
            disabled: record.validTimeType == VALID_TIME_TYPE_ENUM.BUYOUT,
        }),
    };

    const handleBathRenewal = () => {
        setModalVisible(true);
        setIsBath(true);
        setCurrentVehicle(cacheSelectedRowsRef.current);
        modalForm.setFieldsValue({ licenseType: undefined });
    };

    const leftRender = (
        <Auth code="@base:@page:license.bind.query@action:bath.vehicle.renewal">
            <Button type="primary" disabled={!selectedRowKeys.length} onClick={handleBathRenewal}>{i18n.t('name', '批量续期')}</Button>
        </Auth>
    );

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="license-bind-query-container">
                    <StarryTable
                        fetchDataAfterMount={true}
                        aroundBordered
                        fetchDataFunc={fetchData}
                        ref={tableRef}
                        queryProps={{
                            items: queryFormItems,
                            form,
                        }}
                        columns={filterColumns}
                        pagination={{
                            defaultCurrent: Number(page) || 1,
                            defaultPageSize: Number(pageSize) || 20,
                        }}
                        rowSelection={rowSelection}
                        getTableIconBtns
                        toolbar={{
                            iconBtns: getTableIconBtns(getIconBtns, [
                                <Tooltip
                                    title={i18n.t('action', '导出')}
                                    placement="top"
                                    key="action-top"
                                >
                                    <IconExport onClick={exportData} />
                                </Tooltip>,
                                'reload',
                                'column-setting',
                            ]),
                            leftRender: () => (
                                <Space>{getCustomJsx(getTableLeftRender, [leftRender])}</Space>
                            ),
                            columnSetting: {
                                storageKey: '@base:@page:license.bind.query',
                                disabledKeys: ['licenseId', 'action'],
                                ...getColumnSetting?.(),
                            },
                        }}
                        rowKey={'id'}
                    />
                </div>
            </StarryCard>
            <StarryModal
                visible={modalVisible}
                className="license-renew-open-modal"
                title={isBath ? i18n.t('name', '批量续期') : i18n.t('name', '续期')}
                destroyOnClose
                onCancel={() => {
                    modalForm.resetFields();
                    setModalVisible?.(false);
                }}
                onOk={submit}
                size="small"
                confirmLoading={submitLoading}
            >
                <Form layout="vertical" form={modalForm} preserve={false}>
                    {isBath ? (
                        <InfoBack
                            title={i18n.t(
                                'message',
                                '已选择{selectItem}项，共{totalDeviceNumber}个设备',
                                {
                                    selectItem: selectedRowKeys?.length,
                                    totalDeviceNumber: selectDeviceDuplicatesList?.length || 0,
                                },
                            )}
                            style={{ marginBottom: 24 }}
                        />
                    ) : (
                        <StarryAbroadFormItem
                            name="vehicleNumber"
                            label={i18n.t('name', '车牌号码')}
                        >
                            <Input disabled={true} />
                        </StarryAbroadFormItem>
                    )}
                    <StarryAbroadFormItem
                        name="licenseType"
                        label={i18n.t('name', 'License类型')}
                        required
                        rules={[
                            {
                                required: true,
                                message: i18n.t('message', 'License类型不能为空'),
                            },
                        ]}
                    >
                        <Select
                            options={LICENSE_TYPE}
                            placeholder={i18n.t('name', '请选择License类型')}
                            disabled={!isBath}
                            onChange={() => {
                                // 多选切换重置
                                modalForm.setFieldsValue({ setsCode: undefined });
                            }}
                        />
                    </StarryAbroadFormItem>
                    <Form.Item noStyle dependencies={['licenseType']}>
                        {({ getFieldValue }) => (
                            <LicenseTypeSelect
                                ref={licenseTypeSelectRef}
                                licenseType={
                                    isBath
                                        ? getFieldValue('licenseType')
                                        : currentVehicle?.[0]?.licenseType
                                }
                                onSelectedChange={(value) => {
                                    setCurrentLicense(value);
                                }}
                                otherSearchParams={{
                                    storageSum: isBath
                                        ? undefined
                                        : currentVehicle?.[0]?.storageSum ?? -1,
                                    setsCode: isBath
                                        ? undefined
                                        : currentVehicle?.[0]?.setsCode || -1,
                                }}
                                licenseStatus={'0,4'}
                                formItem={{
                                    label: i18n.t('name', 'License'),
                                    name: 'setsCode',
                                    required: true,
                                    customRules: isBath && [
                                        {
                                            validator: (rule: any, value: any) => {
                                                const { num } = currentLicense || {};
                                                if (num < selectDeviceDuplicatesList?.length || 0) {
                                                    return Promise.reject(
                                                        i18n.t('message', 'License数量不足'),
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        },
                                    ],
                                }}
                            />
                        )}
                    </Form.Item>
                </Form>
            </StarryModal>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(LicenseBindQuery);
