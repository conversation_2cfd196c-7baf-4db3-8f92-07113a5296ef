import { LicenseTypeEnum } from "@/service/license";
import { OptionItem } from "@/types";
import { storageSwitchOpen } from "@/utils/license-common";
import { i18n } from "@base-app/runtime-lib";

// license类型
export const licenseTypeOptions: (OptionItem | false)[] = [
    {
        label: i18n.t('name', '设备使用'),
        value: LicenseTypeEnum.DEVICE,
    },
    {
        label: i18n.t('name', '功能开通'),
        value: LicenseTypeEnum.FUNCTION,
    },
    storageSwitchOpen() && {
        label: i18n.t('name', '存储空间'),
        value: LicenseTypeEnum.STORAGE,
    },
].filter(Boolean) as OptionItem[];

//默认类型
export const defaultOption: OptionItem = {
    label: i18n.t('name', '无'),
    value: "-1",
};