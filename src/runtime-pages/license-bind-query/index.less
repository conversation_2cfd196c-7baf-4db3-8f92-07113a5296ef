@import '~@streamax/poppy-themes/starry/index.less';
.license-bind-query-container {
    height: 100%;
    // position: relative;
    overflow: hidden;
    .vehicle-state{
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }
    .state-icon {
        display: inline-table;
        margin-top: -2px;
        vertical-align: middle;
        // display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 8px;
        background: #bfbfbf;
        border-radius: 6px;
    }
    .state-icon-1{
        background: #597ef7;
    }
    .state-icon-2{
        background: #52C41A;
    }
    .state-icon-3{
        background: rgba(0, 0, 0, 0.25);
    }
    .state-icon-30{
        background: #FAAD14;
    }
    .state-text {
        position: relative;
        // top: -1px;
        margin-right: 8px;
        max-width: 50px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .poppy-switch-small {
        margin-top: -2px;
    }
    .renewal-icon{
        font-size: 16px;
    }
}
.license-renew-open-modal{
    .poppy-modal-body {
        padding-bottom: 16px !important;
    }
}