@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.vehicle-realtime-video {
    .car-info {
        display: flex;
        flex-direction: column;
        padding: 0 24px;
        background-color: @starry-bg-color-container;
        .car-number {
            margin: 24px 0;
            color: @starry-text-color-primary;
            font-weight: 700;
            font-size: 20px;
            line-height: 28px;
        }
    }
    .video-container {
        height: calc(100vh - 220px);
        padding: 56px 0 0;
        background-color: var(--poppy-grey-900);
    }
}
.car-info::abroad {
    padding: 0;
    background-color: transparent;
}
.video-container::abroad {
    border-radius: 16px;
    .starry-player-controller-bar {
        border-radius: 0 0 16px 16px;
    }
}
