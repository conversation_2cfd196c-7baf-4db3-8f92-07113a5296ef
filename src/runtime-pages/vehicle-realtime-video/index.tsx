import React, {
    useState,
    useEffect,
    useRef,
    ReactNode,
    useCallback,
} from 'react';
import {
    i18n,
    utils,
    getAppGlobalData,
    RouterPrompt,
    useConfigParameter, mosaicManager, MosaicTypeEnum,
} from '@base-app/runtime-lib';
import { StarryBreadcrumb } from '@base-app/runtime-lib';
import { Link, useHistory, useLocation } from '@base-app/runtime-lib/core';
import { useAsyncEffect, useLockFn } from '@streamax/hooks';
import { getRealtimeVideoVehicleDetail } from '@/service/vehicle';
import './index.less';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import WebPlayer from '../monitoring-center/components/WebPlayer';
import { RealtimeVideoRightContainer } from '@/types/pageReuse/realtimeVideo';
import { StarryPlayerCustom } from '@/types/pageReuse/StarryPlayer';
import {
    hiddenWidgetName,
    PlayerStatus,
    WidgetNameType,
} from '@/components/StarryPlayer/types';
import {
    Intercom as IntercomType,
    VehicleParams,
    DeviceParams,
} from '@/types/pageReuse/pageReuseBase';
import { LIVE_RECONNECT_COUNT_TIME } from '@/utils/constant';
import useMosaic from '@/hooks/useMosaic';
import { Container, message } from '@streamax/poppy';
import { getCustomJsxAsync } from '@/utils/pageReuse';
import { check28181 } from '@/utils/28181utils';
import { handleAlarmRecord } from '@/service/alarm';
import { IntercomInPlayer } from '@base-app/runtime-lib';
import {CONTROL_RANGE, isNeedControl} from "@/utils/flow";

const APP_ENTRY = getAppGlobalData('APP_ENTRY');
const authWidgets = ['enlarge', 'realtimeRecord', 'algorithmMode'];
const pageCode = '@base:@page:realtime.monitoring';
const HANDLE_REALVIDEO = 'saas.search.alarm.handle.type.realvideo';
const disabledWidgets: WidgetNameType[] = [
    'playPause',
    'currentTime',
    'closeTime',
    'speedSetting',
    'moreSetting',
    'chooseChannel',
    'close',
    'layoutType',
    'videoFrame',
    'frameRateAndBitRate',
    'realtimeRecord',
    'layoutCount',
    'storeType',
    'streamType',
    'enlarge',
    // @ts-ignore
    'clarity',
    'screenshot',
    'volume',
    'playMode',
    'closeChannel',
];
const hiddenWidgets: hiddenWidgetName[] = [
    'playMode',
    'channelSelect',
    'frameRateAndBitRate',
    'layoutCount',
    'layoutType',
    'speedSetting',
    'streamType',
    'storeType',
];
const emptyArray = [];

type RealtimeVideoShareProps = RealtimeVideoRightContainer &
    StarryPlayerCustom &
    IntercomType;

type RealtimeVideoProps = {
    mapContainerSelector: any;
    alarmListContainerSelector: any;
    realtimeVideoData: any;
};
type RealtimeVideoAllProps = RealtimeVideoShareProps & RealtimeVideoProps;
const VehicleRealtimeVideoModule = (props: RealtimeVideoAllProps) => {
    /**
     * 复用
     */
    const {
        getPlayerRightTools,
        getChannelTools,
        onScreenshot,
        getPlayerInitChannels,
        getPlayerInitLayout,
        onSelectChannelChange,
        onPlayerInit,
        onIntercom,
        onBeforeIntercom,
        onAfterIntercom,
        onIntercomError,
        getLivePlayerControlBtn,
    } = props;
    /**
     * 复用end
     */
    // @ts-ignore
    const playerRef: any = useRef();
    const { query } = useLocation();
    const { vehicleId, deviceId } = query;
    const [actionData, setActionData] = useState<any>({});
    const operateIntercomParams = useRef<
        VehicleParams & DeviceParams & { optId?: string }
    >({});
    const [intercomReady, setIntercomReady] = useState(false);

    useAsyncEffect(async () => {
        await getVehicleDetailInfo();
    }, []);

    const getVehicleDetailInfo = async () => {
        try {
            const detailInfo = await getRealtimeVideoVehicleDetail(
                {
                    vehicleId,
                },
                false,
            );
            const vehicleInfo = detailInfo || {};
            const deviceInfo = deviceId
                ? // @ts-ignore
                  (vehicleInfo.deviceList || []).find(
                      (item: any) => item.deviceId === deviceId,
                  )
                : // @ts-ignore
                  vehicleInfo.deviceList?.[0];

            setActionData({ ...vehicleInfo, deviceInfo });
        } catch (error) {
            console.error('getVehicleDetail error', error);
        }
    };
    const { data: reconnectArray } = useConfigParameter({
        key: 'LIVE.RECONNECT.COUNT.TIME',
        options: {
            defaultValue: LIVE_RECONNECT_COUNT_TIME,
            formatFn: (params) => {
                // @ts-ignore
                if (params) {
                    const parameterArray = params
                        .split(',')
                        .map((item: string) => Number(item));
                    if (
                        parameterArray.length > 3 &&
                        Array.isArray(parameterArray) &&
                        parameterArray.every((item) => Number.isFinite(item))
                    ) {
                        return parameterArray;
                    }
                    return LIVE_RECONNECT_COUNT_TIME;
                }
                return LIVE_RECONNECT_COUNT_TIME;
            },
        },
    });
    const [reconnectCount, reconnectTime, reconnectAgainCount, reconnectAgainTime] = reconnectArray;
    function _onIntercomStart(params = {}) {
        setIntercomReady(true);
        onIntercom?.({
            ...operateIntercomParams.current,
            ...params,
        });
    }
    function _onAfterIntercom(params = {}) {
        setIntercomReady(false);
        onAfterIntercom?.({
            ...operateIntercomParams.current,
            ...params,
        });
    }

    function _onIntercomError(params = {}) {
        setIntercomReady(false);
        onIntercomError?.({
            ...operateIntercomParams.current,
            ...params,
        });
    }

    const lastTimeSelectedVehicleRef = useRef<string>('');

    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.live);
    // 设置实时视频弹窗视频数据
    useEffect(() => {
        // 判断如果和上次选的是同一个车就不初始化播放器
        const { deviceInfo } = actionData;
        // 移除实时视频同一个时可以再次初始化的逻辑
        // if (lastTimeSelectedVehicleRef.current === deviceInfo?.deviceId) return;
        lastTimeSelectedVehicleRef.current = deviceInfo?.deviceId;
        settingVideoData();
        if (intercomReady) {
            // 关闭对讲
            intercomRef.current?.stopIntercom();
        }
    }, [actionData, hasMosaic]);

    async function settingVideoData() {
        playerRef.current?.beforePlay();
        const { deviceInfo, vehicleId, vehicleNumber } = actionData;
        if (!deviceInfo) return;
        let channels = (deviceInfo?.deviceChannelList || []).map((channel) => {
            return {
                vehicleId,
                vehicleNumber,
                channelAlias: channel.channelAlias,
                channelNo: channel.channelNo,
                channel: `${deviceInfo.deviceId}-${channel.channelNo}`,
                enable: channel.enable,
                peripheralType: channel.peripheralType,
                privacyState: channel.privacyState,
                streamType: 'MINOR',
                deviceId: deviceInfo.deviceId,
                // 设备号
                deviceNo: deviceInfo.deviceNo,
                // 设备别名
                deviceAlias: deviceInfo.deviceAlias,
                // 设备授权ID
                authId: deviceInfo.authId,
                flowLimit: channel.flowLimit,
            };
        });
        // @ts-ignore
        const videoPlayData: VideoPlayDataInfo = {
            videoData: {
                deviceChannelData: channels,
                mosaic: mosaicManager.getVideoMosaicConfigValue(MosaicTypeEnum.live),
                quality: 'SMOOTH',
                mediaProtocol: 'HTTP_FLV',
            },
        };
        //报警处理
        query?.alarmId &&
            handleAlarmRecord([
                {
                    alarmId: query.alarmId,
                    type: HANDLE_REALVIDEO,
                },
            ]);
        // @ts-ignore
        onPlayerInit?.({ deviceInfo });
        playerRef.current?.play(videoPlayData);
    }
    const [customPlayerControlBtn, setCustomPlayerControlBtn] =
        useState<ReactNode>();

    const intercomRef = useRef<any>();

    const beforeIntercomPromise = async (deviceInfo: any) => {
        if (playerRef.current?.getRecordState() == 'start') {
            message.error({ content: i18n.t('message', '请先结束录制'), key: 'overRecord' });
            return Promise.reject({
                isNext: false
            });
        }
        const params = {
            vehicleId: deviceInfo.vehicleId,
            vehicleNumber: deviceInfo.vehicleNumber,
            deviceId: deviceInfo.deviceId,
            authId: deviceInfo.authId,
        };

        const { optId } = onBeforeIntercom?.(params) || {};
        // @ts-ignore
        params.optId = optId;
        operateIntercomParams.current = params;
        return Promise.resolve({
            isNext: true,
            optId
        });
    };

    const renderInfoCardFooter = async () => {
        const InterComEle = (
            <IntercomInPlayer
                ref={intercomRef}
                deviceInfo={actionData.deviceInfo}
                vehicleId={actionData.vehicleId}
                onIntercomClose={_onAfterIntercom}
                onIntercomError={_onIntercomError}
                onIntercomStart={_onIntercomStart}
                beforeIntercomPromise={beforeIntercomPromise}
            />
        );
        let customJsx = [InterComEle];
        if (getLivePlayerControlBtn) {
            customJsx = await getCustomJsxAsync(
                getLivePlayerControlBtn,
                [InterComEle],
                { vehicleId: actionData.vehicleId },
            );
        }
        setCustomPlayerControlBtn(customJsx);
    };
    useEffect(() => {
        renderInfoCardFooter();
    }, [
        getLivePlayerControlBtn,
        intercomReady,
        actionData,
    ]);
    useEffect(() => {
        if (!intercomReady) {
            setWhenData({
                ...whenData,
                when: false,
            });
        }
        return () => {
            // 当前页面关闭时，重置window.name，避免在切换菜单后，再次点击实时视频时，不能新开页面对讲
            if (window.name = 'vehicleTalk') {
                window.name = '';
            }
        }
    }, [intercomReady]);
    // 播放器右下角操作工具栏，对GB28181设备处理过滤录制和更多设置
    const getRightToolsNew = useCallback(
        (list: any, data: any) => {
            let resultList = list;
            // GB28181过滤操作显示
            if (check28181(actionData.deviceInfo || {})) {
                const GB28181_OPTIONS = ['LiveVideoMoreSetting'];
                resultList = list.filter(
                    (item) => !GB28181_OPTIONS.includes(item.key),
                );
            }
            return getPlayerRightTools
                ? getPlayerRightTools?.(resultList, data)
                : resultList;
        },
        [actionData.deviceInfo],
    );

    // 播放器单个通道操作栏，对GB28181设备，删除通道操作
    const getChannelToolsNew = useCallback(
        (list: any, data: any) => {
            let resultList = list;
            // GB28181过滤操作显示
            if (check28181(actionData.deviceInfo || {})) {
                resultList = [];
            }
            return getChannelTools
                ? getChannelTools?.(resultList, data)
                : resultList;
        },
        [actionData.deviceInfo],
    );

    // 播放器播放通道设置，GB28181设备，只能播放一个通道
    const getPlayerInitLayoutNew = useCallback(() => {
        let resultNum = 4;
        // GB28181过滤操作显示
        if (check28181(actionData.deviceInfo || {})) {
            resultNum = 1;
        }
        return getPlayerInitLayout ? getPlayerInitLayout?.() : resultNum;
    }, [actionData.deviceInfo]);

    const onPlayerStatusChange = (playerStatus: PlayerStatus) => {
        intercomRef.current?.setDisabledTalkButton(playerStatus !== 'playing');
    };

    const [whenData, setWhenData] = useState({
        when: false,
        promptMessage: '',
    });

    const promptMessage = whenData.promptMessage || '';
    return (
        <StarryBreadcrumb className="vehicle-realtime-video">
            <RouterPrompt when={whenData.when} message={promptMessage} />
            <div className="car-info">
                <div className="car-number">
                    {actionData.vehicleNumber || '-'}
                </div>
                <div className="video-container">
                    <WebPlayer
                        isLive={true}
                        ref={playerRef}
                        pageCode={pageCode}
                        authWidgets={authWidgets}
                        openMode="realTimeVideoCloseTIme"
                        controlRender={customPlayerControlBtn}
                        moveInRefresh={true}
                        reconnectCount={reconnectCount}
                        reconnectTime={reconnectTime}
                        reconnectAgainCount={reconnectAgainCount}
                        reconnectAgainTime={reconnectAgainTime}
                        // @ts-ignore
                        disabledWidgets={
                            intercomReady ? disabledWidgets : emptyArray
                        }
                        hiddenWidgets={
                            intercomReady ? hiddenWidgets : emptyArray
                        }
                        emptyText={i18n.t('message', '无数据')}
                        getPlayerRightTools={getRightToolsNew}
                        getChannelTools={getChannelToolsNew}
                        onScreenshot={onScreenshot}
                        getPlayerInitChannels={getPlayerInitChannels}
                        getPlayerInitLayout={getPlayerInitLayoutNew}
                        onSelectChannelChange={onSelectChannelChange}
                        onPlayerStatusChange={onPlayerStatusChange}
                    />
                </div>
            </div>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(VehicleRealtimeVideoModule);
