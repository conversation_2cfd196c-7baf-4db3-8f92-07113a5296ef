import { i18n, StarryAbroadIcon, utils } from '@base-app/runtime-lib';
import { StarryInfoBlock, StarryModal, Action } from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import { Descriptions, InfoPanel } from '@streamax/starry-components';
import { DescriptionItem } from '@streamax/poppy/lib/descriptions';
import { SuperPlatform } from '@/service/super-platform';
import { useState } from 'react';
import EyeFilled from '@ant-design/icons/lib/icons/EyeFilled';
import EyeInvisibleFilled from '@ant-design/icons/lib/icons/EyeInvisibleFilled';

const { zeroTimeStampToFormatTime } = utils.formator;

export default (props: { platformItem: SuperPlatform }) => {
    const { platformItem } = props;

    const getDesItems = (): DescriptionItem[] => {
        const pwd = platformItem.password
            ? utils.aes.AesDecrypt(platformItem.password)
            : '';
        const baseInfoList: DescriptionItem[] = [
            {
                label: i18n.t('name', '平台名称'),
                content: platformItem.platformName || '-',
            },
            {
                label: i18n.t('name', '平台编码'),
                content: platformItem.platformCode || '-',
            },
            {
                label: i18n.t('name', '平台IP'),
                content: platformItem.platformIp || '-',
            },
            {
                label: i18n.t('name', '平台端口'),
                content: platformItem.platformPort || '-',
            },
            {
                label: i18n.t('name', '链接方式'),
                content: platformItem.linkType || '-',
            },
            {
                label: i18n.t('name', '接入用户名'),
                content: platformItem.userName || '-',
            },
            {
                label: i18n.t('name', '用户密码'),
                content: <LabelEye text={pwd} />,
            },
        ];
        return baseInfoList;
    };

    return (
        <InfoPanel
            title={i18n.t('name', '基本信息')}
            extraRight={
                <Action
                    code="@base:@page:super.platform.manage:detail@action:edit"
                    url="/super-platform-manage/edit-base"
                    fellback={''}
                    params={{
                        platformCode: platformItem.platformCode,
                    }}
                >
                    {i18n.t('action', '编辑')}
                </Action>
            }
        >
            <Descriptions>
                {getDesItems().map(
                    ({ label, span, content }: DescriptionItem) => {
                        return (
                            <Descriptions.Item
                                label={label}
                                column={span}
                                key={label?.toString()}
                            >
                                {content}
                            </Descriptions.Item>
                        );
                    },
                )}
            </Descriptions>
        </InfoPanel>
    );
};

const LabelEye = ({ text }: { text: string }) => {
    const [visible, setVisible] = useState(false);
    return (
        <div className="starry-descriptions-item-label-eye">
            <span>{visible ? text || '-' : '**********'}</span>
            <span
                style={{ paddingLeft: 8, cursor: 'pointer' }}
                onClick={() => setVisible(!visible)}
            >
                {visible ? (
                    <a>
                        <StarryAbroadIcon>
                            <EyeInvisibleFilled />
                        </StarryAbroadIcon>
                    </a>
                ) : (
                    <a>
                        <StarryAbroadIcon>
                            <EyeFilled />
                        </StarryAbroadIcon>
                    </a>
                )}
            </span>
        </div>
    );
};
