import { message, Switch, Tooltip } from '@streamax/poppy';
import {
    useSystemComponentStyle,
    i18n,
    FleetSelectModal,
    StarryAbroadOverflowEllipsisContainer,
    StarryModal,
} from '@base-app/runtime-lib';
import { useEffect, useRef, useState } from 'react';
import TableWrapper from '@/hooks/useDataAuthorizeTabs/AuthorizeRangeCom/components/TableWrapper';
import { TipConfirm } from '@streamax/starry-components';
import type { FleetItem } from '@/runtime-lib/components/FleetSelectModal';
import { IconDeleteFill, IconRequest } from '@streamax/poppy-icons';
import type { SuperPlatform } from '@/service/super-platform';
import {
    superPlatformCancelFleetAuth,
    superPlatformFleetAuth,
    superPlatformFleetAuthEdit,
    superPlatformFleetAuthPage,
} from '@/service/super-platform';
import { fetchFleetPage } from '@/service/fleet';

const DataAuth = (props: { platformItem: SuperPlatform }) => {
    const { platformItem } = props;
    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();
    const [modalVisible, setModalVisible] = useState(false);
    const [dataSource, setDataSource] = useState<FleetItem[]>([]);
    const tableWrapperRef = useRef<any>(null);

    const getDataList = async () => {
        const { list } = await superPlatformFleetAuthPage({
            platformCode: platformItem.platformCode,
            page: 1,
            pageSize: 1e6,
        });
        // 通过fleetId查询，如果查不到要移除。可能存在先授权，后删除车组的情况
        const { list: fleetList } = await fetchFleetPage({
            fleetIds: list.map((x: any) => x.fleetId).join(','),
            page: 1,
            pageSize: list.length,
        });
        const fleetMap: Record<string, any> = {};
        fleetList.forEach((x: any) => {
            fleetMap[x.fleetId] = x;
        });
        const data = list
            .filter((x) => fleetMap[x.fleetId])
            .map((x) => ({
                fleetId: x.fleetId,
                fleetName: fleetMap[x.fleetId].fleetName,
                isIncludeSub: x.scope,
            }));
        setDataSource(data);
    };

    useEffect(() => {
        if (platformItem.platformCode) {
            getDataList();
        }
    }, [platformItem]);

    const handleDelete = async (record: FleetItem) => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            content: i18n.t('message', '确认要删除“{name}”授权吗？', {
                name: record.fleetName,
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: async () => {
                await superPlatformCancelFleetAuth({
                    platformCode: platformItem.platformCode,
                    fleetIds: record.fleetId,
                });
                const newList = [...dataSource];
                const list = newList.filter((x) => x.fleetId != record.fleetId);
                setDataSource(list);
            },
        });
    };
    const handleMultiDelete = (keys: string[]) => {
        if (!keys || keys.length === 0) {
            message.warning(i18n.t('message', '请选择车组'));
            return;
        }
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            content: `${i18n.t('name', '确认要删除已选车组名称的授权吗？')}`,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: async () => {
                await superPlatformCancelFleetAuth({
                    platformCode: platformItem.platformCode,
                    fleetIds: keys.join(','),
                });
                const newList = [...dataSource];
                const list = newList.filter((x) => !keys.includes(x.fleetId));
                setDataSource(list);
            },
        });
    };
    const handleCheck = async (check: boolean, record: FleetItem) => {
        await superPlatformFleetAuthEdit({
            platformCode: platformItem.platformCode,
            fleetId: record.fleetId,
            scope: check ? 1 : 0,
        });
        const newList = [...dataSource];
        const row = newList.find((x) => x.fleetId == record.fleetId);
        row!.isIncludeSub = check ? 1 : 0;
        setDataSource(newList);
    };
    const columns = [
        {
            title: i18n.t('name', '车组名称'),
            dataIndex: 'fleetName',
            ellipsis: { showTitle: false },
            render: (text: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '是否包含下级'),
            dataIndex: 'isIncludeSub',
            ellipsis: { showTitle: false },
            render: (text: number, record: any) => {
                return (
                    <Switch
                        size="small"
                        checkedChildren={i18n.t('state', '是')}
                        unCheckedChildren={i18n.t('state', '否')}
                        checked={!!text}
                        onChange={(checked: boolean) =>
                            handleCheck(checked, record)
                        }
                    />
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            // @ts-ignore
            render: (text: any, record: any) => {
                return (
                    <Tooltip placement="top" title={i18n.t('action', '删除')}>
                        <IconDeleteFill
                            className="opertate-icon"
                            onClick={() => handleDelete(record)}
                        />
                    </Tooltip>
                );
            },
        },
    ];

    const handleAddFleet = async (values: FleetItem[]) => {
        const map: any = {};
        dataSource.forEach((x) => {
            map[x.fleetId] = x;
        });
        const newList: FleetItem[] = [];
        values.forEach((x) => {
            if (!map[x.fleetId]) {
                newList.push(x);
            }
        });
        if (newList.length) {
            await superPlatformFleetAuth({
                platformCode: platformItem.platformCode,
                isFull: false,
                authInfo: newList.map((x) => ({
                    fleetId: x.fleetId,
                    scope: x.isIncludeSub,
                })),
            });
        }

        setModalVisible(false);
        getDataList();
    };
    return (
        <div>
            <TableWrapper
                ref={tableWrapperRef}
                showOperate
                title={i18n.t('name', '适用车组')}
                operateText={i18n.t('action', '添加车组')}
                descText={`${i18n.t('message', '暂无车组')}`}
                clickText={i18n.t('action', '添加车组')}
                columns={columns}
                rowKey="fleetId"
                showSearch={false}
                showDelete={false}
                showSelectbox={false}
                dataSource={dataSource}
                onClick={() => setModalVisible(true)}
                onDelete={(keys: React.Key[]) =>
                    handleMultiDelete(keys as string[])
                }
            />
            <FleetSelectModal
                visible={modalVisible}
                selectedFleetData={dataSource}
                onSave={handleAddFleet}
                onCancel={() => setModalVisible(false)}
            />
        </div>
    );
};

export default DataAuth;
