import { i18n, utils } from '@base-app/runtime-lib';
import { StarryInfoBlock, StarryModal, Action } from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import { Descriptions, InfoPanel } from '@streamax/starry-components';
import { DescriptionItem } from '@streamax/poppy/lib/descriptions';
import { SuperPlatform } from '@/service/super-platform';

const { zeroTimeStampToFormatTime } = utils.formator;

export default (props: {
    platformItem: SuperPlatform;
}) => {
    const { platformItem } = props;

    const getDesItems = (): DescriptionItem[] => {
        const baseInfoList: DescriptionItem[] = [
            {
                label: i18n.t('name', '下级平台专网IP'),
                content: platformItem.downVpnIp || '-',
            },
            {
                label: i18n.t('name', '下级平台名称'),
                content: platformItem.downName || '-',
            },
            {
                label: i18n.t('name', '下级平台域名'),
                content: platformItem.downIp || '-',
            },
            {
                label: i18n.t('name', '下级平台国标编码'),
                content: platformItem.downCode || '-',
            },
        ];
        return baseInfoList;
    };

    return (
        <InfoPanel
            title={i18n.t('name', '基本信息')}
            extraRight={(
                <Action
                    code="@base:@page:super.platform.manage:detail@action:edit"
                    url="/super-platform-manage/edit-sub"
                    fellback={''}
                    params={{
                        platformCode: platformItem.platformCode,
                    }}
                >
                    {i18n.t('action', '编辑')}
                </Action>
            )}
        >
            <Descriptions>
                {
                    getDesItems().map(({ label, span, content }: DescriptionItem) => {
                        return (
                            <Descriptions.Item label={label} column={span} key={label?.toString()}>
                                {content}
                            </Descriptions.Item>
                        );
                    })
                }
            </Descriptions>
        </InfoPanel>
    );
};