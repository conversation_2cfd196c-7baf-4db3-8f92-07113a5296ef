import { StarryAbroadOverflowEllipsisContainer } from '@base-app/runtime-lib';
import type {
    SuperPlatform,
    SuperPlatformQueryParams,
} from '@/service/super-platform';
import {
    superPlatformDelete,
    superPlatformEnable,
    superPlatformUpdateBasic,
} from '@/service/super-platform';
import { useLockFn } from '@streamax/hooks';
import {
    Button,
    Form,
    Input,
    message,
    Select,
    Space,
    Table,
    Tooltip,
    Switch,
} from '@streamax/poppy';
import {
    IconAdd,
    IconDelete,
    IconEdit,
    IconRequest,
} from '@streamax/poppy-icons';
import {
    Auth,
    i18n,
    useUrlSearchStore,
    utils,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import classNames from 'classnames';
import {
    StarryBreadcrumb,
    StarryCard,
    StarryModal,
    Action,
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import type { ListDataContainerProps } from '@streamax/starry-components/lib/list-data-container';
import { useEffect, useRef } from 'react';
import './index.less';
import { superPlatformPage } from '@/service/super-platform';

export const PLATFORM_STATE = {
    ENABLE: 1,
    DISABLE: 0,
};

export enum SYNC_STATE {
    SYNC_ING,
    SUCCESS,
    FAIL,
    NOT_SYNC,
}
const SuperPlatformManage = () => {
    const { getSystemColor } = useSystemComponentStyle();
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const { pageSize, page, enableState, syncState, keyword } =
        searchStore.get();
    const containerRef = useRef<any>();
    const history = useHistory();

    useEffect(() => {
        form.setFieldsValue({
            page,
            pageSize,
            enableState:
                Number(enableState) === 0
                    ? 0
                    : Number(enableState) || undefined,
            keyword: keyword || undefined,
        });
        containerRef.current?.loadDataSource();
    }, []);

    const enableStateMap = [
        { label: i18n.t('state', '启用'), value: 1 },
        { label: i18n.t('state', '停用'), value: 0 },
    ];

    const queryForm: ListDataContainerProps['queryForm'] = {
        items: [
            {
                label: i18n.t('name', '平台'),
                name: 'keyword',
                field: Input,
                fieldProps: {
                    placeholder: i18n.t('message', '请输入平台名称或平台编码'),
                    maxLength: 50,
                    allowClear: true,
                },
                itemProps: {
                    rules: [{ max: 50, type: 'string' }],
                },
            },
            {
                label: i18n.t('name', '平台状态'),
                name: 'enableState',
                field: Select,
                fieldProps: {
                    placeholder: i18n.t('message', '请选择平台状态'),
                    options: enableStateMap,
                    allowClear: true,
                },
            },
        ],
        form,
    };
    const fetchData = async (params: SuperPlatformQueryParams) => {
        searchStore.set(params);
        params.keyword = params.keyword?.trim()
            ? encodeURIComponent(params.keyword?.trim())
            : undefined;
        const data = await superPlatformPage({
            ...params,
        });
        return data;
    };

    const onOkLock = useLockFn(async (modal, row, checked) => {
        await superPlatformEnable({
            platformCode: row.platformCode,
            enableState: checked
                ? PLATFORM_STATE.ENABLE
                : PLATFORM_STATE.DISABLE,
        })
            .then(() => {
                message.success(i18n.t('message', '操作成功'));
            })
            .finally(() => {
                modal.destroy();
                // @ts-ignore
                containerRef.current?.loadDataSource();
            });
    });

    const handleLockPlatform = (checked: boolean, row: SuperPlatform) => {
        const modal = StarryModal.confirm({
            centered: true,
            title: checked
                ? i18n.t('message', '启用确认')
                : i18n.t('message', '停用确认'),
            content: checked
                ? i18n.t('message', '确定要启用{platformName}平台？', {
                      platformName: row.platformName,
                  })
                : i18n.t(
                      'message',
                      '确定要停用{platformName}平台？停用后该平台不再继续上报车辆',
                      {
                          platformName: row.platformName,
                      },
                  ),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                onOkLock(modal, row, checked);
            },
        });
    };

    const columns = [
        {
            title: i18n.t('name', '平台名称'),
            dataIndex: 'platformName',
            render: (text: string, record: SuperPlatform) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Action
                            code="@base:@page:super.platform.manage@action:detail"
                            url="/super-platform-manage/platform-detail"
                            fellback={text}
                            params={{
                                platformCode: record.platformCode,
                            }}
                        >
                            {text}
                        </Action>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '平台编码'),
            dataIndex: 'platformCode',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '平台状态'),
            dataIndex: 'enableState',
            ellipsis: true,
            width: 150,
            render: (text: number, row: any) => {
                return (
                    <div className="platform-state">
                        <span
                            className={classNames('state-icon', {
                                active: text === PLATFORM_STATE.ENABLE,
                            })}
                        />
                        <span className="state-text">
                            {text === PLATFORM_STATE.ENABLE
                                ? i18n.t('state', '启用')
                                : i18n.t('state', '停用')}
                        </span>
                        {
                            <Auth code="@base:@page:super.platform.manage@action:enable.unable">
                                <Switch
                                    size="small"
                                    checked={text === PLATFORM_STATE.ENABLE}
                                    onChange={(checked: boolean) =>
                                        handleLockPlatform(checked, row)
                                    }
                                />
                            </Auth>
                        }
                    </div>
                );
            },
        },
        {
            title: i18n.t('name', '平台IP'),
            dataIndex: 'platformIp',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '平台端口'),
            dataIndex: 'platformPort',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '链接方式'),
            dataIndex: 'linkType',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '操作'),
            key: 'action',
            width: 120,
            render: (_: any, record: SuperPlatform) => {
                return (
                    <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Action
                            code="@base:@page:super.platform.manage@action:edit"
                            url="/super-platform-manage/edit-base"
                            fellback={''}
                            params={{
                                platformCode: record.platformCode,
                            }}
                        >
                            <Tooltip title={i18n.t('action', '编辑')}>
                                <IconEdit />
                            </Tooltip>
                        </Action>
                        {record.enableState === PLATFORM_STATE.DISABLE && (
                            <Auth code="@base:@page:super.platform.manage@action:delete">
                                <Tooltip title={i18n.t('action', '删除')}>
                                    <IconDelete
                                        className="list-icon"
                                        onClick={() => delPlatform(record)}
                                    />
                                </Tooltip>
                            </Auth>
                        )}
                    </Space>
                );
            },
        },
    ];
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(columns, {
            storageKey: '@base:@page:super.platform.manage',
            disabledKeys: ['platformName', 'action'],
        });

    // 删除
    const delPlatform = (record: SuperPlatform) => {
        const content = i18n.t(
            'message',
            '确认要删除“{platformName}”平台？如果该平台已有车辆上报，删除平台后车组和车辆也将被移除',
            {
                platformName: record.platformName,
            },
        );
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            closable: true,
            icon: <IconRequest />,
            onOk: async () => {
                try {
                    await superPlatformDelete({
                        platformCode: record.platformCode,
                    });
                    message.success(i18n.t('message', '操作成功'));
                } finally {
                    containerRef.current?.loadDataSource();
                    modal.destroy();
                }
            },
        });
    };
    const toolbar = {
        extraLeft: (
            <>
                <Action
                    code="@base:@page:super.platform.manage@action:add"
                    url="/super-platform-manage/add-platform"
                    fellback={''}
                >
                    <Button type="primary" icon={<IconAdd />}>
                        {i18n.t('action', '添加')}
                    </Button>
                </Action>
            </>
        ),
        extraIconBtns: [<TableColumnSetting {...tableColumnSettingProps} />],
    };
    return (
        <StarryBreadcrumb className="base-data-super-platform-manage">
            <StarryCard>
                <ListDataContainer
                    queryForm={queryForm}
                    loadDataSourceOnMount={false}
                    getDataSource={fetchData}
                    pagination={{
                        defaultCurrent: Number(page) || 1,
                        defaultPageSize: Number(pageSize) || 20,
                    }}
                    ref={containerRef}
                    toolbar={toolbar}
                    listRender={(data) => {
                        return (
                            <Table
                                columns={tableColumns}
                                dataSource={data}
                                pagination={false}
                                rowKey="platformCode"
                            />
                        );
                    }}
                />
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default SuperPlatformManage;
