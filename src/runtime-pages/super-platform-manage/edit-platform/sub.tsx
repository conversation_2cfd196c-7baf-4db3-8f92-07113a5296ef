import { Button, Form, message } from '@streamax/poppy';
import {
    i18n,
    Router<PERSON>rompt,
    StarryAbroadLRLayout,
    StarryBreadcrumb,
    StarryCard,
} from '@/runtime-lib';
import { useSubmitFn } from '@streamax/hooks';
import SubInfo from '../components/form-sub';
import { useEffect, useRef, useState } from 'react';
import { useHistory } from '@base-app/runtime-lib/core';
import {
    superPlatformDetail,
    superPlatformUpdateDown,
} from '@/service/super-platform';

export default (props: any) => {
    const { platformCode } = props.location.query;
    const history = useHistory();
    const [form] = Form.useForm();
    const platFormDataRef = useRef<any>({});
    const [when, setWhen] = useState(false);

    const loadData = async () => {
        const rs = await superPlatformDetail({ platformCode });
        if (rs && rs.platformCode) {
            platFormDataRef.current = rs;
            form.setFieldsValue({
                ...rs,
            });
        } else {
            message.error(i18n.t('message', '平台不存在'));
        }
    };

    useEffect(() => {
        loadData();
    }, []);

    const goBackPage = () => {
        history.goBack();
    };

    const [save, loading] = useSubmitFn(async () => {
        const values = await form.validateFields();
        const params = {
            platformCode: platformCode,
            platformType: platFormDataRef.current.platformType,
            ...values,
            downName: values.downName?.trim(),
            downCode: values.downCode?.trim(),
        };

        await superPlatformUpdateDown(params);

        message.success(i18n.t('message', '操作成功'));
        setWhen(false);
        goBackPage();

        return null;
    });

    const handleFormChange = (changedValues: any, values: any) => {
        setWhen(true);
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <StarryCard>
                <Form
                    layout="vertical"
                    form={form}
                    initialValues={{
                        linkType: 'TCP',
                    }}
                    onValuesChange={handleFormChange}
                >
                    <SubInfo />
                </Form>
                <StarryAbroadLRLayout>
                    <Button onClick={goBackPage}>
                        {i18n.t('action', '取消')}
                    </Button>

                    <Button
                        type="primary"
                        onClick={() => save()}
                        loading={loading}
                    >
                        {i18n.t('action', '保存')}
                    </Button>
                </StarryAbroadLRLayout>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
