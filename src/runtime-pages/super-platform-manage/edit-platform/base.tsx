import { Button, Form, message } from '@streamax/poppy';
import BaseInfo from '../components/form-base';
import {
    i18n,
    RouterPrompt,
    StarryAbroadLRLayout,
    StarryBreadcrumb,
    StarryCard,
    utils,
} from '@/runtime-lib';
import { useSubmitFn } from '@streamax/hooks';
import { useEffect, useRef, useState } from 'react';
import { superPlatformDetail } from '@/service/super-platform';
import { useHistory } from '@base-app/runtime-lib/core';
import { superPlatformUpdateBasic } from '@/service/super-platform';

export default (props: any) => {
    const { platformCode } = props.location.query;
    const [form] = Form.useForm();
    const history = useHistory();
    const platFormDataRef = useRef<any>({});
    const [when, setWhen] = useState(false);

    const loadData = async () => {
        const rs = await superPlatformDetail({ platformCode });
        if (rs && rs.platformCode) {
            platFormDataRef.current = rs;
            form.setFieldsValue({
                ...rs,
                sp_password: utils.aes.AesDecrypt(rs.password),
                sp_userName: rs.userName,
            });
        } else {
            message.error(i18n.t('message', '平台不存在'));
        }
    };

    useEffect(() => {
        loadData();
    }, []);

    const goBackPage = () => {
        history.goBack();
    };

    const [saveBaseInfo, saveInfoLoading] = useSubmitFn(async () => {
        const values = await form.validateFields();
        const params = {
            // ...platFormDataRef.current,
            ...values,
            platformName: values.platformName?.trim(),
            password: utils.aes.AesEncrypt(values.sp_password),
            userName: values.sp_userName?.trim(),
        };

        await superPlatformUpdateBasic(params);

        message.success(i18n.t('message', '操作成功'));
        setWhen(false);
        goBackPage();

        return null;
    });

    const handleFormChange = (changedValues: any, values: any) => {
        setWhen(true);
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <StarryCard>
                <Form
                    layout="vertical"
                    form={form}
                    initialValues={{
                        linkType: 'TCP',
                    }}
                    onValuesChange={handleFormChange}
                >
                    <BaseInfo platformCode={platformCode} />
                </Form>
                <StarryAbroadLRLayout>
                    <Button onClick={goBackPage}>
                        {i18n.t('action', '取消')}
                    </Button>

                    <Button
                        type="primary"
                        onClick={() => saveBaseInfo()}
                        loading={saveInfoLoading}
                    >
                        {i18n.t('action', '保存')}
                    </Button>
                </StarryAbroadLRLayout>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
