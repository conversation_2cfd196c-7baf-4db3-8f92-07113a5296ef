import { Row, Col, Input, Radio, InputNumber, Select } from '@streamax/poppy';
import {
    StarryAbroadFormItem,
    useSystemComponentStyle,
    StarryAbroadLRLayout,
    i18n,
    utils,
} from '@base-app/runtime-lib';
import { Rule } from '@streamax/poppy/lib/form';
import { checkFieldSpace, validIPAddress } from '@/utils/commonFun';
import { InfoPanel, PasswordInput } from '@streamax/starry-components';
import { superPlatformCodeCheck } from '@/service/super-platform';
import { useState } from 'react';
import { RspFormLayout } from '@streamax/responsive-layout';
import { S17illegalCharacter } from '../common';

interface FieldItem {
    label: string;
    name: string | (string | number)[];
    validateTrigger?: string | string[];
    fieldKey?: (string | number)[];
    show?: boolean;
    rules?: Rule[];
    validateFirst?: boolean;
    render: () => JSX.Element;
}

const BaseInfo = (props: { platformCode?: string }) => {
    const { platformCode } = props;
    const [singleColumn, setSingleColumn] = useState(false);

    const renderRowList = (renderFieldList: FieldItem[]) => {
        return (
            <RspFormLayout
                layoutType="auto"
                onSingleColumn={(val) => setSingleColumn(val)}
            >
                {renderFieldList.map((field: FieldItem) => {
                    const { render, ...fieldProps } = field;
                    return (
                        <StarryAbroadFormItem
                            key={field.label}
                            {...fieldProps}
                            style={{ width: singleColumn ? '100%' : '340px' }}
                        >
                            {render()}
                        </StarryAbroadFormItem>
                    );
                })}
            </RspFormLayout>
        );
    };

    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '平台名称不能为空'));
    };

    const platformCodeNumberValidator = async (rule: Rule, value: string) => {
        if (value) {
            if (!/^\d{1,20}$/.test(value)) {
                return Promise.reject(i18n.t('message', '请输入1-20位数字'));
            }
        }
        return Promise.resolve();
    };

    const platformCodeValidator = async (rule: Rule, value: string) => {
        if (value) {
            if (!platformCode) {
                const rs = await superPlatformCodeCheck({
                    platformCode: value,
                });
                // 0:不重复 1:上级平台编码重复 2:下级平台编码重复 3:两个都重复
                if (rs != 0) {
                    return Promise.reject(i18n.t('message', '平台编码重复'));
                }
            }
        }
        return Promise.resolve();
    };

    const govIpValidator = (rule: Rule, value: string) => {
        if (!value || validIPAddress(value)) {
            return Promise.resolve();
        } else {
            return Promise.reject(i18n.t('message', '请输入正确的IP'));
        }
    };

    const govPortValidator = (rule: Rule, value: string) => {
        if (
            !value ||
            (/^[1-9]\d{0,8}$/.test(value) && Number(value) <= 65535)
        ) {
            return Promise.resolve();
        } else {
            return Promise.reject(i18n.t('message', '请输入正确的端口'));
        }
    };

    const renderFn = ({
        field: Field = Input,
        children,
        ...fieldProps
    }: any) => {
        const defaultProps = {
            allowClear: true,
            maxLength: 50,
            ...fieldProps,
        };
        return <Field {...defaultProps}>{children}</Field>;
    };

    const baseFieldItemList: FieldItem[] = [
        {
            label: i18n.t('name', '平台名称'),
            name: 'platformName',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    validator: checkSpaceName,
                },
                {
                    min: 1,
                    type: 'string',
                },
                {
                    max: 200,
                    type: 'string',
                },
                {
                    validator: S17illegalCharacter,
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入平台名称'),
                    maxLength: 200,
                }),
        },
        {
            label: i18n.t('name', '平台编码'),
            name: 'platformCode',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台编码'),
                    validateTrigger: ['onChange', 'onBlur'],
                },
                {
                    validator: platformCodeNumberValidator,
                    validateTrigger: ['onChange', 'onBlur'],
                },
                {
                    validator: platformCodeValidator,
                    validateTrigger: 'onBlur',
                },
            ],
            render: () =>
                renderFn({
                    maxLength: 20,
                    placeholder: i18n.t('message', '请输入平台编码'),
                    disabled: !!platformCode,
                }),
        },
        {
            label: i18n.t('name', '平台IP'),
            name: 'platformIp',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台IP'),
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入平台IP'),
                    maxLength: 200,
                }),
        },
        {
            label: i18n.t('name', '平台端口'),
            name: 'platformPort',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入平台端口'),
                },
                {
                    validator: govPortValidator,
                },
            ],
            render: () =>
                renderFn({
                    field: InputNumber,
                    placeholder: i18n.t('message', '请输入平台端口'),
                    min: 1,
                    max: 65535,
                    precision: 0,
                    step: 1,
                }),
        },
        {
            label: i18n.t('name', '链接方式'),
            name: 'linkType',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请选择链接方式'),
                },
            ],
            render: () =>
                renderFn({
                    field: Radio.Group,
                    options: [
                        {
                            label: i18n.t('name', 'TCP'),
                            value: 'TCP',
                        },
                        {
                            label: i18n.t('name', 'UDP'),
                            value: 'UDP',
                        },
                    ],
                }),
        },
        {
            label: i18n.t('name', '接入用户名'),
            name: 'sp_userName',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入接入用户名'),
                },
                {
                    validator: S17illegalCharacter,
                },
            ],
            render: () =>
                renderFn({
                    field: Input,
                    placeholder: i18n.t('message', '请输入接入用户名'),
                    maxLength: 50,
                }),
        },
        {
            label: i18n.t('name', '用户密码'),
            name: 'sp_password',
            validateFirst: true,
            rules: [
                {
                    required: true,
                    message: i18n.t('message', '请输入用户密码'),
                },
                {
                    validator: S17illegalCharacter,
                },
            ],
            render: () =>
                renderFn({
                    field: PasswordInput,
                    autoComplete: 'new-password',
                    placeholder: i18n.t('message', '请输入用户密码'),
                    maxLength: 50,
                }),
        },
    ];
    return (
        <InfoPanel title={i18n.t('name', '基本信息')}>
            {renderRowList(baseFieldItemList)}
        </InfoPanel>
    );
};

export default BaseInfo;
