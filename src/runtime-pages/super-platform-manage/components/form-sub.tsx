import { Row, Col, Input, Radio, InputNumber } from '@streamax/poppy';
import {
    StarryAbroadFormItem,
    useSystemComponentStyle,
    StarryAbroadLRLayout,
    i18n,
    utils,
} from '@base-app/runtime-lib';
import { Rule } from '@streamax/poppy/lib/form';
import { checkFieldSpace, validIPAddress } from '@/utils/commonFun';
import { InfoPanel } from '@streamax/starry-components';
import { superPlatformCodeCheck } from '@/service/super-platform';
import { RspFormLayout } from '@streamax/responsive-layout';
import { useState } from 'react';
import { S17illegalCharacter } from '../common';

interface FieldItem {
    label: string;
    name: string | (string | number)[];
    validateTrigger?: string | string[];
    fieldKey?: (string | number)[];
    show?: boolean;
    rules?: Rule[];
    validateFirst?: boolean;
    render: () => JSX.Element;
}

const SubInfo = (props: { platformCode?: string }) => {
    const { platformCode } = props;
    const [singleColumn, setSingleColumn] = useState(false);
    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();

    const renderRowList = (renderFieldList: FieldItem[]) => {
        return (
            <RspFormLayout
                layoutType="auto"
                onSingleColumn={(val) => setSingleColumn(val)}
            >
                {renderFieldList.map((field: FieldItem) => {
                    const { render, ...fieldProps } = field;
                    return (
                        <StarryAbroadFormItem
                            key={field.label}
                            {...fieldProps}
                            style={{ width: singleColumn ? '100%' : '340px' }}
                        >
                            {render()}
                        </StarryAbroadFormItem>
                    );
                })}
            </RspFormLayout>
        );
    };

    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '平台名称不能为空'));
    };

    const lengthValidator = async (rule: Rule, value: string) => {
        if (value) {
            if (!/^\d{6}$/.test(value)) {
                return Promise.reject(i18n.t('message', '请输入6位数字'));
            }
        }
        return Promise.resolve();
    };

    const repeatValidator = async (rule: Rule, value: string) => {
        if (value) {
            // 新增时校验
            if (!platformCode) {
                const rs = await superPlatformCodeCheck({
                    downCode: value,
                });
                // 0:不重复 1:上级平台编码重复 2:下级平台编码重复 3:两个都重复
                if (rs != 0) {
                    return Promise.reject(i18n.t('message', '平台编码重复'));
                }
            }
        }
        return Promise.resolve();
    };

    const ipPortValidator = (rule: Rule, value: string) => {
        // ip+端口
        const reg = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
        if (!value || reg.test(value)) {
            return Promise.resolve();
        } else {
            return Promise.reject(
                i18n.t(
                    'message',
                    '请输入有效的专网IP地址，如：***********:8080',
                ),
            );
        }
    };

    const domainValidator = (rule: Rule, value: string) => {
        // 后端说S17要求 http|https+域名|ip+端口
        const reg = new RegExp(
            '^(https?://)([a-zA-Z0-9.-]+|(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]?\\d)):(?:[1-9]\\d{0,3}|[1-5]\\d{4}|6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5]))))$',
        );
        if (!value || reg.test(value)) {
            return Promise.resolve();
        } else {
            return Promise.reject(i18n.t('message', '请输入正确的域名'));
        }
    };

    const renderFn = ({
        field: Field = Input,
        children,
        ...fieldProps
    }: any) => {
        const defaultProps = {
            allowClear: true,
            maxLength: 50,
            ...fieldProps,
        };
        return <Field {...defaultProps}>{children}</Field>;
    };

    const baseFieldItemList: FieldItem[] = [
        {
            label: i18n.t('name', '下级平台专网IP'),
            name: 'downVpnIp',
            validateFirst: true,
            rules: [
                {
                    validator: ipPortValidator,
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入下级平台专网IP'),
                    maxLength: 200,
                }),
        },
        {
            label: i18n.t('name', '下级平台名称'),
            name: 'downName',
            validateTrigger: ['onChange', 'onBlur'],
            validateFirst: true,
            rules: [
                {
                    required: true,
                },
                {
                    validator: S17illegalCharacter,
                },
            ],
            render: () =>
                renderFn({
                    maxLength: 200,
                    placeholder: i18n.t('message', '请输入下级平台名称'),
                }),
        },
        {
            label: i18n.t('name', '下级平台域名'),
            name: 'downIp',
            validateFirst: true,
            rules: [
                {
                    required: true,
                },
                {
                    validator: domainValidator,
                },
            ],
            render: () =>
                renderFn({
                    placeholder: i18n.t('message', '请输入下级平台域名'),
                }),
        },
        {
            label: i18n.t('name', '下级平台国标编码'),
            name: 'downCode',
            validateFirst: true,
            rules: [
                {
                    required: true,
                },
                {
                    validator: lengthValidator,
                },
            ],
            render: () =>
                renderFn({
                    field: Input,
                    placeholder: i18n.t('message', '请输入下级平台国标编码'),
                    maxLength: 6,
                }),
        },
    ];
    return (
        <InfoPanel title={i18n.t('name', '下级平台信息')}>
            {renderRowList(baseFieldItemList)}
        </InfoPanel>
    );
};

export default SubInfo;
