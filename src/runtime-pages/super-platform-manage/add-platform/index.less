@import '~@streamax/poppy-themes/starry/index.less';
.base-super-platform-manage {
    .platform-create-header {
        margin: 8px 0 24px;
    }
    &-title {
        padding-bottom: 16px;
        font-weight: 600;
        font-size: 20px;
        &::abroad {
            font-size: 18px;
        }
    }
    .platform-create-body {
        // 消除最后一个FormItem的margin-bottom
        .poppy-row:last-of-type::abroad {
            .poppy-col {
                .poppy-form-item {
                    margin-bottom: 0;
                }
            }
        }
        .poppy-col-6::abroad {
            flex: 1;
            max-width: unset;
        }

        .poppy-radio-group,
        .poppy-checkbox-group {
            .poppy-radio-wrapper,
            .poppy-checkbox-wrapper {
                min-width: 100px;
                margin-right: 8px;
            }
        }
    }

    .steps-content {
        margin: 16px 0 20px;
    }

    .opertate-icon {
        color: @primary-color;
        cursor: pointer;
    }
}
.base-809-starry-card::abroad {
    .starry-card-layout-header {
        padding-top: 0;
    }
}
.platform-create-footer::abroad {
    margin-top: 0;
}
