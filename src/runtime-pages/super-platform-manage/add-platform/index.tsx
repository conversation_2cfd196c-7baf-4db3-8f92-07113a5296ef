import { useState } from 'react';
import { Form, Button, message, Steps } from '@streamax/poppy';
import { useSubmitFn } from '@streamax/hooks';
import {
    RouterPrompt,
    StarryBreadcrumb,
    StarryCard,
} from '@base-app/runtime-lib';
import { StarryAbroadLRLayout } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { utils, i18n } from '@base-app/runtime-lib';
import './index.less';
import FormBase from '../components/form-base';
import FormSub from '../components/form-sub';
import DataAuth from '../components/data-auth';
import {
    superPlatformAdd,
    superPlatformFleetAuth,
} from '@/service/super-platform';
import { FleetItem } from '@/runtime-lib/components/FleetSelectModal';

enum StepEnum {
    'one' = 0,
    'two' = 1,
    'three' = 2,
}
const { Step } = Steps;
const AddOrEditPlatform = (props: any) => {
    const [form1] = Form.useForm();
    const [form2] = Form.useForm();

    const [step, setStep] = useState(StepEnum.one);
    const [authList, setAuthList] = useState<FleetItem[]>([]);
    const [when, setWhen] = useState(false);
    const history: any = useHistory();

    const [handleSaveBaseInfo, baseloading] = useSubmitFn(async () => {
        const form2Fields = await form2.validateFields();
        const form1Fields = form1.getFieldsValue(true);
        // const form2Fields = form2.getFieldsValue();

        const params = {
            platformName: form1Fields.platformName?.trim(),
            platformCode: form1Fields.platformCode?.trim(),
            platformIp: form1Fields.platformIp,
            platformPort: form1Fields.platformPort,
            linkType: form1Fields.linkType,
            userName: form1Fields.sp_userName.trim(),
            password: utils.aes.AesEncrypt(form1Fields.sp_password),

            downVpnIp: form2Fields.downVpnIp,
            downName: form2Fields.downName?.trim(),
            downIp: form2Fields.downIp,
            downCode: form2Fields.downCode?.trim(),
        };
        await superPlatformAdd(params);
        message.success(i18n.t('message', '操作成功'));
        setStep(StepEnum.three);
        return null;
    });

    const [handleSave, loading] = useSubmitFn(async () => {
        if (authList.length) {
            const platformCode = form1.getFieldValue('platformCode');
            const params = {
                platformCode,
                isFull: true,
                authInfo: authList.map((x) => {
                    return {
                        fleetId: x.fleetId,
                        scope: x.isIncludeSub,
                    };
                }),
            };
            await superPlatformFleetAuth(params);
        }
        message.success(i18n.t('message', '操作成功'));
        setWhen(false);
        goBackPage();
    });

    const goBackPage = () => {
        history.goBack();
    };

    const steps = [
        {
            title: i18n.t('name', '基本信息'),
            footer: (
                <>
                    <Button onClick={goBackPage}>
                        {i18n.t('action', '取消')}
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            await form1.validateFields();
                            setStep(StepEnum.two);
                        }}
                    >
                        {i18n.t('action', '下一步')}
                    </Button>
                </>
            ),
        },
        {
            title: i18n.t('name', '下级平台'),
            footer: (
                <>
                    <Button onClick={goBackPage}>
                        {i18n.t('action', '取消')}
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => setStep(StepEnum.one)}
                    >
                        {i18n.t('action', '上一步')}
                    </Button>
                    <Button
                        type="primary"
                        onClick={async () => {
                            await handleSaveBaseInfo();
                        }}
                        loading={baseloading}
                    >
                        {i18n.t('action', '保存并下一步')}
                    </Button>
                </>
            ),
        },
        {
            title: i18n.t('name', '数据授权'),
            footer: (
                <>
                    <Button onClick={goBackPage}>
                        {i18n.t('action', '取消')}
                    </Button>
                    <Button
                        type="primary"
                        loading={loading}
                        onClick={async () => {
                            await handleSave();
                        }}
                    >
                        {i18n.t('action', '保存')}
                    </Button>
                </>
            ),
        },
    ];

    const handleFormChange = (changedValues: any, values: any) => {
        setWhen(true);
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <StarryCard className="base-super-platform-manage">
                <div className="steps-content">
                    <Steps current={step}>
                        {steps.map((item) => (
                            <Step key={item.title} title={item.title} />
                        ))}
                    </Steps>
                </div>

                <div
                    style={{ display: step == StepEnum.one ? 'block' : 'none' }}
                >
                    <Form
                        layout="vertical"
                        form={form1}
                        initialValues={{
                            linkType: 'TCP',
                        }}
                        onValuesChange={handleFormChange}
                    >
                        <FormBase />
                    </Form>
                </div>
                <div
                    style={{ display: step == StepEnum.two ? 'block' : 'none' }}
                >
                    <Form
                        layout="vertical"
                        form={form2}
                        onValuesChange={handleFormChange}
                    >
                        <FormSub />
                    </Form>
                </div>
                <div
                    style={{
                        display: step == StepEnum.three ? 'block' : 'none',
                    }}
                >
                    <DataAuth onChange={(list) => setAuthList(list)} />
                </div>

                <div className="platform-create-footer">
                    <StarryAbroadLRLayout>
                        {steps[step].footer}
                    </StarryAbroadLRLayout>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default AddOrEditPlatform;
