import { useEffect, useRef, useState } from 'react';
import { GpsDataItem } from '../interface';
import useMarkerSvg from './useMarkerSvg';
import { formatLatOrLng, splitToMacroTask } from '../utils';
import { getCustomItems } from '@/utils/pageReuse';
import rmPageConfig from '../../../page-config';
import { OFFLINE_STATE } from '../const';
import { performanceMeasureUtil } from '@base-app/runtime-lib';
import { DEFAULT_ICON, getVehicleTypeIconMap } from '@/utils/getVehicleIcon';
import { useAsyncEffect } from 'ahooks';

const useVehicleCanvasMarker = (
    vehicleList: GpsDataItem[],
    map: any,
    L: any,
    getMapVehicle: () => void,
    onVehicleUpdate: () => void,
    oneTaskMarkerLength = 1000,
) => {
    const { RTDataInstance, MapInstance } = rmPageConfig.data;
    const selectInfo = MapInstance.useDataStore((state) => state.selectInfo);
    const { deviceMap, deviceChangedVidsSet, gpsMap, gpsChangedVidsSet, vehicleStateConfig } =
        RTDataInstance.useDataStore((state) => {
            return {
                deviceMap: state.deviceStatus.deviceMap,
                deviceChangedVidsSet: state.deviceStatus.changedVids,
                gpsMap: state.gps.gpsMap,
                gpsChangedVidsSet: state.gps.changed.changedVids,
                vehicleStateConfig: state.deviceStatus.stateConfig
            };
        });    
    const vehicleStateConfigRef = useRef<any>({});
    const markerMap = useRef(new Map());
    const [markers, setMarkers] = useState<any>([]);

    const { loaded: markerSvgLoaded, vehicleMarkers } =
        useMarkerSvg(vehicleStateConfig);
    const vehicleMarkersRef = useRef(vehicleMarkers);
    const markerSvgCreated = useRef<boolean>(false);
    const latLngInMapView = (latlng: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return map.getPixelBounds().contains(map.project(latlng));
    };
    const [canDrawMarkers, setCanDrawMarkers] = useState(false);
    //自定义车辆图标
    const iconMapRef = useRef<Record<string, string>>();

    useAsyncEffect(async() => {
        iconMapRef.current = await getVehicleTypeIconMap();
    }, [])

    useEffect(() => {
        if (
            markerSvgLoaded &&
            map
        ) {
            vehicleMarkersRef.current = vehicleMarkers;
            setCanDrawMarkers(true)
        }
    }, [markerSvgLoaded, map, vehicleMarkers]);

    useAsyncEffect(async() => {
        performanceMeasureUtil.map.start("drawMarkers")        
        //只执行一次全量绘制
        if (vehicleList.length && canDrawMarkers && !markerSvgCreated.current && iconMapRef.current) {
            // console.log(new Date().getTime(),'mark drawMarkers start');
            drawMarkers();
        }
    }, [vehicleList.length, canDrawMarkers, markerSvgCreated.current, iconMapRef.current]);

    // 根据聚合的数量获取图标基本属性: 字体、大小、图标Url
    const getMarkerProperty = (num: number) => {
        const styleMap = {
            default: {
                textFont: '14px Helvetica Neue',
                size: 40,
                iconUrl: vehicleMarkersRef.current.clusterIcon.default,
            },
            small: {
                textFont: '16px Helvetica Neue',
                size: 50,
                iconUrl: vehicleMarkersRef.current.clusterIcon.small,
            },
            middle: {
                textFont: '18px Helvetica Neue',
                size: 70,
                iconUrl: vehicleMarkersRef.current.clusterIcon.middle,
            },
            big: {
                textFont: '20px Helvetica Neue',
                size: 85,
                iconUrl: vehicleMarkersRef.current.clusterIcon.big,
            },
        };

        let size: 'default' | 'small' | 'middle' | 'big' = 'default';
        if (num >= 1000) {
            size = 'big';
        } else if (num >= 100) {
            size = 'middle';
        } else if (num >= 10) {
            size = 'small';
        }
        return styleMap[size];
    };
    const loadClusterIcon = (count: number) => {
        const { textFont, size, iconUrl } = getMarkerProperty(count);
        return {
            icon: {
                iconUrl,
                iconAnchor: [size / 2, size / 2],
                iconSize: [size, size],
            },
            text: {
                textContent: count,
                realText: count,
                textAnchor: [0, 0],
                textBackground: false,
                textColor: '#fff',
                textFont,
            },
        };
    };
    //获取车辆icon图标
    const getIconConfig = (
        data: Pick<GpsDataItem, 'gps' | 'vStates' | 'vType'>,
        selected = false,
    ) => {
        const { gps, vStates = [OFFLINE_STATE] } = data;
        const angle = gps?.ang ? gps.ang + 180 : 180;
        const angleIconUrl = selected
            ? vehicleMarkersRef.current.selected[vStates[0]]
            : vehicleMarkersRef.current.default[vStates[0]];
        const carIconUrl =
            iconMapRef.current[String(data.vType)] ||
            DEFAULT_ICON;
        if (!angleIconUrl || !carIconUrl) return null;
        const size = {
            default: {
                angleIconSize: 44,
                carIconSize: 16,
            },
            selected: {
                angleIconSize: 45,
                carIconSize: 18,
            },
        };
        const { angleIconSize, carIconSize } = selected
            ? size.selected
            : size.default;
        const icons = [
            {
                iconUrl: angleIconUrl,
                // 地图 0 方向角指向正右方，需要 -90 调整为正上方
                iconRotation: angle - 90,
                // (7.5 * angleIconSize / 50) 表示箭头那部分应该的偏移量
                iconAnchor: [
                    angleIconSize / 2 + (7.5 * angleIconSize) / 50,
                    angleIconSize / 2,
                ],
                iconSize: [angleIconSize, angleIconSize],
            },
            {
                iconUrl: carIconUrl,
                iconRotation: 0,
                iconAnchor: [carIconSize / 2, carIconSize / 2],
                iconSize: [carIconSize, carIconSize],
            },
        ];
        return L?.icon({
            icons,
            iconAnchor: [20, 20],
            iconSize: [40, 40],
        });
    };
    //获取车辆车牌字体
    const fontSize = 12;
    const fontFamily = 'Helvetica Neue';
    const canvasTextStyle = {
        textFont: `${fontSize}px ${fontFamily}`,
        textBackgroundPadding: [5, 4],
    };
    const getTextConfig = (data: GpsDataItem, selected = false) => {
        const textStyle = selected
            ? {
                textMaxLength: 16,
                textBackgroundColor: '#597ef7',
                textBackgroundRadius: 12,
            }
            : {
                textMaxLength: 10,
                textBackgroundColor: '#0b2267',
                textBackgroundRadius: 10,
            };
        const fontWeight = selected ? 'bold' : 'normal';
        const textFont = `${fontWeight} ${fontSize}px ${fontFamily}`;
        const text = data.name;
        const textAnchor = [0, 35];
        return Object.assign(
            {
                textContent: text,
                realText: text,
                textAnchor,
                textBackground: true,
                textBackgroundRadius: 10,
                textColor: '#fff',
                textBackgroundPadding: canvasTextStyle.textBackgroundPadding,
                textFont,
            },
            textStyle,
        );
    };
    //生成车辆mark
    const loadCarIcon = (data: GpsDataItem, selected = false) => {
        const { gps, vId, fId, vType } = data;
        let newMarker = null;
        const icon = getIconConfig(data, selected);
        const text = getTextConfig(data, selected);
        if (!icon || !text) return null;
        newMarker = L.marker(
            L.latLng(
                formatLatOrLng(gps?.lat || 0),
                formatLatOrLng(gps?.lng || 0),
            ),
            {
                data: {
                    type: 'vehicle',
                    vId,
                    fId,
                    vType
                },
                icon,
                text,
            },
        );
        const dataSource = {
            data,
            vehicleStateConfig: vehicleStateConfigRef.current,
            isSelected: selected,
        };
        return getCustomItems(getMapVehicle, newMarker, dataSource);
    };
    const renderMarkersByMacroTask = splitToMacroTask(async (gpsData) => {
        const markers = [];
        for (let index = 0; index < gpsData.length; index++) {
            const item = gpsData[index];
            if (!markerMap.current.get(item.vId)) {
                const newMarker = loadCarIcon(
                    item,
                    selectInfo?.vId === item.vId,
                );
                newMarker && markers.push(newMarker);
                markerMap.current.set(item.vId, newMarker);
            }
        }
        return markers;
    }, oneTaskMarkerLength);
    const drawMarkers = async () => {
        //车辆数据去重
        const vids = new Set();
        const uniqueGpsData = vehicleList.filter((item) => {
            if (vids.has(item.vId)) {
                return false;
            }
            vids.add(item.vId);
            return true;
        });
        markerSvgCreated.current = false;
        const markers = await renderMarkersByMacroTask(uniqueGpsData);
        setMarkers(markers);
        // console.log(new Date().getTime(),'mark  drawMarkers end');
        markerSvgCreated.current = true;
    };

    //更新选中
    const updateSelectMarker = (vehicle: GpsDataItem, selected: boolean) => {
        const { vId } = vehicle;
        const marker = markerMap.current.get(vId);
        if (marker) {
            const icon = getIconConfig(vehicle, selected);
            const text = getTextConfig(vehicle, selected);
            marker.options.text = text;
            marker.setIcon?.(icon, true);
            return marker;
        }
        return null;
    };

    useEffect(() => {
        vehicleStateConfigRef.current = vehicleStateConfig;
    }, [vehicleStateConfig]);
    useEffect(() => {
        vehicleMarkersRef.current = vehicleMarkers;
    }, [markerSvgLoaded]);

    //车辆状态、GPS更新
    // 合并处理GPS和状态更新
    const handleCombinedUpdates = async (gpsVids: string[], statusVids: string[]) => {
        performanceMeasureUtil.map.start("updateMarkersGpsAndStatus")
        // 创建一个Set来存储所有需要更新的vId
        const allUpdateVids = new Set([...gpsVids, ...statusVids]);

        // 创建一个Map来存储每个vId需要更新的内容类型
        const updateTypeMap = new Map();
        gpsVids.forEach((vId) => updateTypeMap.set(vId, { needGps: true }));
        statusVids.forEach((vId) => {
            const current = updateTypeMap.get(vId) || {};
            updateTypeMap.set(vId, { ...current, needStatus: true });
        });
        const getUpdateVids = splitToMacroTask(async (vids: string[]) => {
            const updates = [];
            for (const vId of vids) {
                const marker = markerMap.current.get(vId);
                if (!marker) continue;

                const gps = gpsMap[vId];

                if (!gps) continue;

                // 计算新的坐标
                const lat = formatLatOrLng(gps.lat);
                const lng = formatLatOrLng(gps.lng);
                const latlng = L.latLng(lat, lng);

                // 只处理视图范围内的marker
                // 与产品沟通优先保证实时性，避免出现没有更新视图范围外车辆移动地图过去后显示不一致问题
                // 后续看地图能不能实现只更新可视区域车辆
                // if (
                //     !latLngInMapView(latlng) &&
                //     !latLngInMapView(marker.getLatLng())
                // )
                //     continue;

                const updateInfo = updateTypeMap.get(vId);
                const update: any = { marker };

                // 处理GPS更新
                if (updateInfo.needGps) {
                    update.latlng = latlng;
                    update.needUpdatePos =
                        lat !== marker._latlng.lat ||
                        lng !== marker._latlng.lng;

                    const icon = marker.getIcon()?.options?.icons[0];
                    const ang = gps.ang ? gps.ang + 90 : 90;
                    if (icon && icon.iconRotation !== ang) {
                        update.needUpdateAngle = true;
                        update.angle = ang;
                    }
                }

                // 处理状态更新
                if (updateInfo.needStatus) {
                    const device = deviceMap[vId];
                    if (device) {
                        const { vType } = marker.options.data;
                        update.needUpdateIcon = true;
                        update.iconConfig = {
                            gps,
                            vStates: device.vStates,
                            vType,
                        };
                        update.isSelected = selectInfo?.vId === vId;
                    }
                }

                if (
                    update.needUpdatePos ||
                    update.needUpdateAngle ||
                    update.needUpdateIcon
                ) {
                    updates.push(update);
                }
            }
            return updates;
        }, oneTaskMarkerLength)
        // 批量处理所有更新
        const batchUpdate = splitToMacroTask(async (updates: any[]) => {
            // 批量应用更新
            for(const update of updates) {
                const { marker } = update;
                // 更新位置
                if (update.needUpdatePos) {
                    marker.setLatLng?.(update.latlng);
                }

                // 更新图标（包括角度和状态）
                if (update.needUpdateAngle || update.needUpdateIcon) {
                    let icon = marker.getIcon();

                    if (update.needUpdateIcon) {
                        icon = getIconConfig(
                            update.iconConfig,
                            update.isSelected,
                        );
                    } else if (
                        update.needUpdateAngle &&
                        icon?.options?.icons[0]
                    ) {
                        icon.options.icons[0].iconRotation = update.angle;
                    }

                    if (icon) {
                        marker.setIcon(icon);
                    }
                }
            }
            //触发地图渲染更新
            onVehicleUpdate?.();
            return []
        }, oneTaskMarkerLength);

        const updateVids =  await getUpdateVids(Array.from(allUpdateVids))
        await batchUpdate(updateVids);

        performanceMeasureUtil.map.end("updateMarkersGpsAndStatus")
    };

    // 使用useRef存储防抖定时器
    const updateDebounceTimer = useRef<any>(null);
    useEffect(() => {
        //未创建完成不更新
        if (!markerSvgCreated.current) return;
        // 清理上一次的防抖定时器
        if (updateDebounceTimer.current) {
            clearTimeout(updateDebounceTimer.current);
        }

        const gpsVids = Array.from(gpsChangedVidsSet);
        const statusVids = Array.from(deviceChangedVidsSet);

        if (gpsVids.length === 0 && statusVids.length === 0) return;

        // 使用16ms的防抖，确保在一帧内的更新被合并
        updateDebounceTimer.current = setTimeout(() => {
            handleCombinedUpdates(gpsVids, statusVids);
        }, 16);

        return () => {
            if (updateDebounceTimer.current) {
                clearTimeout(updateDebounceTimer.current);
            }
        };
    }, [gpsChangedVidsSet, deviceChangedVidsSet, markerSvgCreated.current]);

    return {
        markers,
        loadClusterIcon,
        updateSelectMarker,
    };
};
export default useVehicleCanvasMarker;
