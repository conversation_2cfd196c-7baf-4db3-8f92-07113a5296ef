import { useAsyncEffect, useDebounceFn, useSize, useUnmount } from '@streamax/hooks';
import { message } from '@streamax/poppy';
import {
    i18n,
    useInitMap,
    useConfigParameter,
    getAppGlobalData,
    performanceMeasureUtil,
} from '@base-app/runtime-lib';
import { useEffect, useState, useRef, useCallback } from 'react';
import { GpsDataItem } from '../interface';
import {
    aggregationCarTotal,
    DEFAULT_CENTER_TIME,
    maxShowMarkerZoom,
} from '../const';
import useVehicleCanvasMarker from '../hooks/useVehicleCanvasMarker';
import { formatLatOrLng } from '../utils';
import MapUi from '../ui';
import rmPageConfig from '../../../page-config';
import { isNil } from 'lodash';
import { buildTreeKey, joinWithSeparator, NodeType } from '../../AsyncStarryTreeData';

let setMapWithCache = false;
const Map = () => {
    const {
        MapInstance,
        StaticDataInstance,
        FilteredVehicleInstance,
        RTDataInstance,
        VehicleDetailInstance,
        NormalTreeInstance,
        PageCacheDataInstance,
        MapToolInstance
    } = rmPageConfig.data;
    const { MapTool } = rmPageConfig.ui;
    const selectInfo = MapInstance?.useDataStore((state) => state.selectInfo);
    const reuseObj =
        rmPageConfig.useReuseStore.getState()?.reuseData?.sharePropData?.[
        'RealtimeMonitoring.Map'
        ] || {};
    const { getMapVehicle, onMonitorMapMounted, onMapGpsChange } = reuseObj;
    const vehicleList = StaticDataInstance?.useDataStore(
        (state) => state.vehicleList,
    );
    const deviceMap = RTDataInstance?.useDataStore(
        (state) => state.deviceStatus.deviceMap,
    );
    const gpsMap = RTDataInstance?.useDataStore((state) => state.gps.gpsMap);
    const changedVids = RTDataInstance?.useDataStore((state) => state.gps.changed.changedVids);

    const filterVehicleIds = FilteredVehicleInstance?.useDataStore(
        (state) => state.filterVehicleIds,
    );
    const [allVehicleList, setAllVehicleList] = useState<any[]>([])
    const gpsData = useRef<any[]>([]);
    const mapContainer = useRef<HTMLDivElement>(null);
    const markerGroup = useRef<any>();
    const resetCenterRef = useRef(true);
    const selectedDrawVehicleRef = useRef<GpsDataItem>();
    const centerTimeRef = useRef<any>(null);
    const { width, height } = useSize(mapContainer) || { width: 0, height: 0 };
    performanceMeasureUtil.map.start("mapInit")
    const { map, setDefaultCenter, L, LR } = useInitMap(
        'map_container',
        undefined,
        true,
    );
    const mapRef = useRef(map);
    const mapCenterRef = useRef<any>(null);
    const setMapCacheViewRef = useRef(false);
    const isFirstGetVehicleListRef = useRef(true);
    const disableVehicleSet = rmPageConfig.data.RTDataInstance.useDataStore(
        (state) => state.disableVehicle.disableVehicleSet,
    );

    const onVehicleUpdate = useCallback(() => {
        markerGroup.current?.requestRender?.(true);
    }, [markerGroup.current])

    const { markers, loadClusterIcon, updateSelectMarker } =
        useVehicleCanvasMarker(allVehicleList, map, L, getMapVehicle, onVehicleUpdate);

    useEffect(() => {
        if (vehicleList.length > 0) {
            const { id } = vehicleList[0];
            //防止gpsMap或者deviceMap为空情况            
            const device = deviceMap[id];
            if (!device) return;
            const gpsLength = Object.keys(gpsMap)?.length || 0;
            if (!gpsLength) return;
            const allVehicleList = vehicleList.map((item) => {
                const {
                    id,
                    name,
                    parentId,
                    extends: { vType },
                } = item;
                const { devices, state, vStates, isOnline, vOnlineNumber } =
                    deviceMap[id] || {};
                const gps = gpsMap[id];
                return {
                    vId: id,
                    vNo: name,
                    fId: parentId,
                    vType,
                    gps: gps || null,
                    extends: item.extends,
                    devices,
                    state,
                    vStates,
                    isOnline,
                    vOnlineNumber,
                    name: item.name,
                };
            });
            if(isFirstGetVehicleListRef.current){
                setAllVehicleList(allVehicleList)
                // console.log(new Date().getTime(),'mark  setList start');
                isFirstGetVehicleListRef.current = false
            }
            gpsData.current = allVehicleList.filter((item) =>
                filterVehicleIds.has(item.vId),
            );
            MapInstance.useDataStore.setState({
                vehicleData: gpsData.current,
            });
        }
    }, [deviceMap, gpsMap, vehicleList, filterVehicleIds, JSON.stringify(changedVids)]);
    //选中打开详情
    const onClickMark = useCallback(
        ({ fId, vId }) => {
            MapInstance.selectInfoLockVehicle(vId);
            //树选中打开车辆详情
            NormalTreeInstance.positionNode(buildTreeKey({
                id: joinWithSeparator([fId, vId]),
                parentId: fId,
                type: NodeType.VEHICLE
            }));
            VehicleDetailInstance.open(vId);
        },
        [selectInfo],
    );
    //传入数据vId的map结构
    const vehicleIdSet = useRef(new Set());
    //地图绘制完成
    const mapDrawLoaded = useRef<boolean>(false);
    // 车辆聚合规则开启显示为车辆图标的地图缩放等级
    const [aggregationShowMarkerZoom, setAggregationShowMarkerZoom] =
        useState(5);

    //获取车辆聚合规则租户参数
    const { data: aggregationRule, loading } = useConfigParameter({
        key: 'MAP.VEHICLE.AGGREGATION.RULE.CONFIG',
        options: { defaultValue: false },
    });
    useEffect(() => {
        const { mapType = 'GMapEN' } =
            getAppGlobalData('APP_USER_CONFIG') || {};
        if (mapType === 'BMap') {
            //百度地图市级参数为缩放等级5
            setAggregationShowMarkerZoom(6);
        }
        return () => {
            selectedDrawVehicleRef.current = undefined;
        };
    }, []);

    const { run: mapDebounceInvalidateSize } = useDebounceFn(
        () => {
            map?.invalidateSize();
        },
        {
            wait: 200,
            leading: false,
            trailing: true,
        },
    );
    useEffect(() => {
        mapDebounceInvalidateSize();
    }, [height, width]);

    /************************ 组件数据缓存逻辑 开始 ************************/
    const collect = () => {
        const { lat, lng } = mapCenterRef.current || {};
        const mapLevel = mapRef.current?.getZoom()        
        setMapWithCache = true
        PageCacheDataInstance.setCacheItem('realtimeMonitorMap', {
            mapLevel,
            mapCenter: `${lat},${lng}`,
        });
    };
    const distribute = async () => {
        try {
            const { mapLevel, mapCenter } =
                await PageCacheDataInstance.getCacheItemAsync('realtimeMonitorMap');
            if (map && mapCenter && mapLevel) {
                const [lat, lng] = (mapCenter as string).split(',');
                map.setView(
                    L.latLng(Number(lat), Number(lng)),
                    Number(mapLevel),
                );
                setMapCacheViewRef.current = true
            } else {
                setMapCacheViewRef.current = false
            }
        } catch (error) {
            console.log(error, 'error');
        }

    };
    useEffect(() => {
        return () => {
            if (centerTimeRef.current) clearTimeout(centerTimeRef.current);
            collect();
            mapRef.current?.off('movestart')
            mapRef.current?.off('moveend')
            mapDrawLoaded.current = false;
            MapInstance.destroy();
        };
    }, []);
    /************************ 组件数据缓存逻辑 结束 ************************/

    useEffect(() => {
        MapInstance.useDataStore.setState({
            map,
            mapContainer: mapContainer.current,
            L,
            LR,
        });
    }, [mapRef.current, mapContainer]);

    useAsyncEffect(async () => {
        if (map && !loading) {
            mapRef.current = map;
            try {
                await setDefaultCenter();
            } catch (error) {
                console.error(error);
            }
            if (setMapWithCache) {
                await distribute();
            }
            // @ts-ignore
            map.dragging.removeHooks();
            // @ts-ignore
            map.dragging._draggable = null;
            // @ts-ignore
            map.dragging.addHooks();

            map.on('movestart', () => {
                // 解决如下场景：用户拖动过程中，数据改变且有选中的车辆时，由于有锁定机制，会将车辆定位回中间，打断了用户拖动流畅性
                // 因此开个定时器，30秒才可以锁定回去
                resetCenterRef.current = false;
                if (centerTimeRef.current) clearTimeout(centerTimeRef.current);
                centerTimeRef.current = setTimeout(() => {
                    resetCenterRef.current = true;
                }, DEFAULT_CENTER_TIME);
            });
            map.on('moveend', () => {
                mapCenterRef.current = map.getCenter()
            });
            onMonitorMapMounted?.(map);
            performanceMeasureUtil.map.end("mapInit")
        }
    }, [map, loading]);

    useEffect(() => {
        if (vehicleIdSet.current.size !== gpsData.current.length) {
            //缓存车辆数量与传入数据不相等
            vehicleIdSet.current.clear();
            gpsData.current?.forEach((item: { vId: unknown }) => {
                vehicleIdSet.current.add(item.vId);
            });

            if (markerGroup.current && mapDrawLoaded.current) {
                //隐藏车辆图标,避免切换重点监控树和筛选重新生成图标
                markerGroup.current.setFilter(
                    (marker: {
                        getLatLng: () => { lat: any; lng: any };
                        options: { data: { vId: string } };
                    }) => {
                        const { lat, lng } = marker.getLatLng();
                        return (
                            vehicleIdSet.current.has(
                                marker?.options?.data.vId,
                            ) &&
                            (lat || lng) &&
                            !disableVehicleSet.has(marker?.options?.data.vId)
                        );
                    },
                );
            }
        }
    }, [gpsData.current, disableVehicleSet, mapDrawLoaded.current]);

    useEffect(() => {
        // gpsDada复用
        onMapGpsChange &&
            onMapGpsChange?.({
                gpsData: (gpsData.current || []).map(
                    (item: {
                        vId: any;
                        gps: any;
                        fId: any;
                        vOnlineNumber: any;
                    }) => {
                        const { vId, gps, fId, vOnlineNumber } = item;
                        return {
                            gps,
                            vId,
                            fId,
                            vOnlineNumber,
                        };
                    },
                ),
            });
    }, [gpsData.current]);

    useEffect(() => {
        renderMarkers();
    }, [markers, mapRef.current, mapDrawLoaded.current]);
    const renderMarkers = async () => {
        const map = mapRef.current;
        if (!map || mapDrawLoaded.current) return;

        if (!markerGroup.current) {
            markerGroup.current = new L.MarkerClusterLayer([], {
                singleMarkerMode: false,
                maxClusterRadius: 200,
                // 最大聚合等级
                disableClusteringAtZoom: aggregationRule
                    ? aggregationShowMarkerZoom
                    : maxShowMarkerZoom,
                // 最小车辆聚合数
                minClusterMakers: aggregationRule ? aggregationCarTotal : 2,
                //聚合图标
                iconCreateFunction: (cluster: { count: number }) =>
                    loadClusterIcon(cluster.count),
            }).addTo(map);
            markerGroup.current.bindTooltip(
                (layer: {
                    marker: { options: { text: { realText: any } } };
                }) => {
                    if (layer.marker) {
                        return layer.marker?.options?.text?.realText;
                    }
                },
                { direction: 'bottom', offset: [0, 35] },
            );
            // 绑定点击事件
            markerGroup.current.addOnClickListener((e: any, rect: any) => {
                if (!e.layer?.marker?.options?.data) return;
                const { type, fId, vId } = e.layer?.marker?.options.data;
                if (type === 'vehicle') {
                    onClickMark({
                        fId,
                        vId,
                    });
                }
            });
        }
        if (!markers.length) return;
        markerGroup.current.clearLayers();
        markerGroup.current
            .addLayers(markers)
            .setFilter(
                (marker: { getLatLng: () => { lat: any; lng: any } }) => {
                    mapDrawLoaded.current = true;
                    const { lat, lng } = marker.getLatLng();
                    return lat || lng;
                },
            );
        markerGroup.current?.requestRender?.(true);
        //设置地图展示所有车辆
        // [123785]只有未勾选围栏，不渲染围栏区域时并且没有缓存时，才展示所有车辆调用fitBounds
        if (!MapToolInstance.useDataStore.getState().fenceRelatedAreaRendered && !setMapCacheViewRef.current) {
            const latlngs = markers.map((marker) => marker.getLatLng());
            // 【10273】在设置fitBounds前，将经纬度都为0的gps点过滤掉
            const bounds = L.latLngBounds(latlngs.filter(i => !isNil(i.lat) && !isNil(i.lng) && i.lat !== 0 && i.lng !== 0));
            map.fitBounds(bounds);
        }
        // console.log(new Date().getTime(),'mark  renderMarkers end');
        performanceMeasureUtil.map.end("drawMarkers")
    };

    const selectInfoLockVehicle = useCallback(
        (vId: string | undefined, isMessage?: boolean) => {
            performanceMeasureUtil.map.start("selectInfoLockVehicle")
            //取消树选中清除选中效果
            if (
                !vId &&
                selectedDrawVehicleRef.current?.vId &&
                markerGroup.current
            ) {
                const vehicle = gpsData.current.find((item) => item.vId === selectedDrawVehicleRef.current?.vId);
                updateSelectMarker(vehicle, false);
                markerGroup.current?.requestRender?.(true);
            }
            if (!vId) return;
            const vehicle = gpsData.current.find(
                (item: { vId: string }) => item.vId === vId,
            );
            if (!vehicle) return;
            // 车组树选中车辆定位
            const { lat, lng } = vehicle?.gps || {};            
            selectedDrawVehicleRef.current &&
                updateSelectMarker(
                    selectedDrawVehicleRef.current,
                    selectedDrawVehicleRef.current.vId === vId,
                );
            if (lat && lng) {
                if (
                    Math.abs(lat / 1000000) < 90 &&
                    Math.abs(lng / 1000000) < 180
                ) {
                    //更新车辆选中样式、位置
                    const marker = updateSelectMarker(vehicle, true);
                    marker &&
                        marker.setLatLng(
                            L.latLng(formatLatOrLng(lat), formatLatOrLng(lng)),
                        );
                    // 选中的 marker 放最上面
                    marker && markerGroup.current?.bringToFront(marker);
                    markerGroup.current?.requestRender?.(true);
                    selectedDrawVehicleRef.current = vehicle;
                    MapInstance.setView(lat, lng, 18);
                    performanceMeasureUtil.map.end("selectInfoLockVehicle")
                }
            } else {
                if (isMessage) {
                    message.warn(i18n.t('message', '车辆未定位'));
                }
            }
        },
        [
            gpsData.current,
            markerGroup.current,
            selectedDrawVehicleRef.current,
        ],
    );

    // 手动选择，需要提示，第二个参数为 true
    useEffect(() => {                
        if(mapDrawLoaded.current) selectInfoLockVehicle(selectInfo?.vId, true);
    }, [selectInfo, mapDrawLoaded.current]);

    // 非手动选择，不需要提示，第二个参数为 false
    const selectGpsInfo = selectInfo ? gpsMap[selectInfo?.vId] : null;    
    useEffect(() => {
        //gps位置信息变化时才更新锁定选择车辆在地图中心  
        if (!selectInfo) return;
        const { vId } = selectInfo;
        if (!vId || !resetCenterRef.current) {
            return;
        }
        if(mapDrawLoaded.current) selectInfoLockVehicle(vId);
    }, [selectGpsInfo, mapDrawLoaded.current, resetCenterRef.current]);
    return (
        <MapUi ref={mapContainer}>
            <MapTool />
        </MapUi>
    );
};

export default Map;
