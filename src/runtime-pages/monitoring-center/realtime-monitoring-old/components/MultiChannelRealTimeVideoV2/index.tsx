/*
 * @LastEditTime: 2025-04-19 14:22:54
 */
import {
    i18n,
    Auth,
    utils,
    g_emmiter,
    getAppGlobalData,
    lifecycle, mosaicManager, MosaicTypeEnum,
} from '@base-app/runtime-lib';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import React, {
    useEffect,
    useRef,
    useState,
    forwardRef,
    useImperativeHandle,
    useCallback,
} from 'react';
import { StarryModal, StarryPlayer } from '@base-app/runtime-lib';
import WebPlayer from '../../../components/WebPlayer';
import {
    RealtimeVideoVehicleDetail,
    VehicleOrderState,
    getRealtimeVideoVehiclesDetail,
    getVehicleDetail,
} from '@/service/vehicle';
import { RealtimeVideoRightContainer } from '@/types/pageReuse/realtimeVideo';
import { StarryPlayerCustom } from '@/types/pageReuse/StarryPlayer';
import { checkFieldSpace } from '@/utils/commonFun';
import {
    DeviceItem,
    DeviceChannelItem,
    ChannelData,
    hiddenWidgetName,
    WidgetNameType,
    FMPTYPE,
    PlayMode,
    PlayerStatus,
} from '@/components/StarryPlayer/types';
import AlarmAudio from '../../../components/AlarmAudio';
import MultiPlayer from '../../../components/MultiPlayer';
import './index.less';
import { useDebounceFn, useLatest, useLockFn, useUpdateEffect } from '@streamax/hooks';
import DeviceListMenu from './DeviceListMenu';
import VideoAlarmHandle from './VideoAlarmHandle';
import { Button, Form, Input, Tooltip, message } from '@streamax/poppy';
import { IconRequest, IconTextDownload } from '@streamax/poppy-icons';
import ContextHandle from './ContextHandle';
import useUpdateState from './useUpdateState';
import { cloneDeep, unionBy } from 'lodash';
import { groupIllegalCharacter } from '@/utils/commonFun';
import { SelectedStatusType } from '@/components/VehicleStatusSelect';
import { VehicleListItem } from '@/service/monitor-group';
import MultiMoreSetting from './MultiLiveMoreSetting';
import useMosaic from '@/hooks/useMosaic';
import { useDispatch } from '@base-app/runtime-lib/core';
import { UPDATE_STATE_TYPE } from '../../model';
import {
    getDeviceByChannel,
    getDeviceIdByChannel,
    getDeviceIdByList,
} from './utils';
import { IntercomInPlayer } from '@base-app/runtime-lib';

import type {
    Intercom as IntercomType,
    VehicleParams,
    DeviceParams,
} from '@/types/pageReuse/pageReuseBase';
import useAllowAiType from "@/hooks/useAllowAiType";
import { CachePlayerState, logoutSavePlayerState, PlayVehicleListItem } from './logout-save-player-state';
import {CONTROL_RANGE, isNeedControl} from "@/utils/flow";

const {
    PlayerContextProvider,
    constant: { LAYOUT_TYPE },
} = StarryPlayer;

const noVideoImg = require('@/assets/images/icon_emptystate_deep_06.svg');
const defaultPlaceholder = <img src={noVideoImg} alt="" />;
const DEFAULT_LAYOUT_COUNT =
    (window as any).APP_CONFIG['videoplayer.perpage.channels.number'] == 4
        ? 4
        : 9; //若配置的是4通道，默认打开四通道
const DEFAULT_VOLUME = 0.5;
const MAX_VEHICLE = 50; //最大播放车辆数
const hiddenWidgets: hiddenWidgetName[] = [
    'playMode',
    'channelSelect',
    'frameRateAndBitRate',
    'layoutCount',
    'layoutType',
    'speedSetting',
    'streamType',
    'storeType',
];
const disabledWidgets: WidgetNameType[] = [
    'playPause',
    'currentTime',
    'closeTime',
    'speedSetting',
    'moreSetting',
    'chooseChannel',
    'close',
    'layoutType',
    'videoFrame',
    'frameRateAndBitRate',
    'realtimeRecord',
    'layoutCount',
    'storeType',
    'streamType',
    'enlarge',
    // @ts-ignore
    'clarity',
    'screenshot',
    'volume',
    'playMode',
    'closeChannel',
];

const authWidgets = ['enlarge', 'realtimeRecord', 'algorithmMode'];

export type RealtimeVideoShareProps = RealtimeVideoRightContainer &
    StarryPlayerCustom;

export type VehicleItem = RealtimeVideoVehicleDetail & {
    fleetId?: string;
};
// {
//     vehicleNumber: string; //车牌号
//     vehicleId: string; //车辆id
//     vehicleState: number; //车辆状态
//     onlineState: 0 | 1; //在线状态
//     vehicleColor: string; //车辆颜色
//     networkState: string; //网络
//     deviceList: DeviceItem[]; //设备列表
// };
export type PlayerPlayMode = 'group' | 'normal';
export type PlayModeType = {
    mode?: PlayerPlayMode;
    monitorFleetId?: string;
};
export type MultiChannelRealTimeVideoProps = {
    vehicleStateConfig: VehicleOrderState[];
    onMessageDistribution?: (deviceInfo: DeviceItem) => void; //消息下发
    onHighlightPlay?: (deviceInfo: DeviceItem | null) => void; //凸出播放触发时
    onAlarmVehicle?: (deviceInfo: any) => void; //报警触发时
    onClickSaveMonitorGroup?: (monitorInfo: {
        vehicleList: VehicleListItem[];
        type: 'add' | 'edit';
        monitorFleetId?: string;
        monitorGroupName?: string;
    }) => Promise<boolean | string>;
    onIntercom?: (params: VehicleParams) => void;
    onRecoverMonitorGroup: () => void
};
export type VehiclePlayItem = {
    vehicleId: string;
    fleetId: string;
    deviceIdList?: string[];
};

enum OperateType {
    /**
     * 单设备操作
     */
    single,
    /**
     * 批量多设备操作
     */
    multiple,
}

export type MultiChannelRealTimeVideoRef = {
    playVideo: (
        vehicleList: VehiclePlayItem[],
        playerPlayMode: PlayModeType,
    ) => void;
    getPlayerInfo: () => {
        vehicleList: VehiclePlayItem[];
        playerPlayMode: PlayModeType;
    };
    // 获取操作状态
    getOperateState: () => {
        recordState: boolean;
        talkState: boolean;
    };
};
export type MultiChannelRealTimeVideoShareProps = IntercomType & StarryPlayerCustom;
type MultiChannelRealTimeVideoAllProps = MultiChannelRealTimeVideoShareProps &
    MultiChannelRealTimeVideoProps;

//默认9通道, 因为加载时会触发默认4，用ref存永远都是4, 在第一次以此数量为准，后续以ref为准
const MultiChannelRealTimeVideo: React.ForwardRefRenderFunction<
    MultiChannelRealTimeVideoRef,
    MultiChannelRealTimeVideoAllProps
> = (
    props,
    ref,
) => {
    const {
        vehicleStateConfig,
        onMessageDistribution,
        onClickSaveMonitorGroup,
        onHighlightPlay,
        onAlarmVehicle,
        onBeforeIntercom,
        onIntercom,
        onAfterIntercom,
        onIntercomError,
        onRecoverMonitorGroup,
        getRealtimeVideoRecordModal
    } = props;

    const [vehicleCollection, setVehicleCollection] = useState<VehicleItem[]>(
        [],
    ); //所有的车辆不包括在线车辆和已关闭的车辆
    const vehicleCollectionRef = useRef<VehicleItem[]>([]); //存储车辆，解决并发请求时vehicleCollection未及时更新问题
    const [allVehicleMap, setAllVehicleMap] = useState<
        Record<string, string[]>
    >({}); //所有的车辆用于判断是否是多设备，展示设备号
    const [monitoringGroupVehicleMap, setMonitoringGroupVehicleMap] = useState<
        Record<string, string[]>
    >({}); //存储当前车组树车辆和设备，用于判断是否加入该监控组
    const [layoutCount, setLayoutCount] = useState<number | null>(
        DEFAULT_LAYOUT_COUNT,
    );
    const layoutLoadVehicleRef = useRef<VehicleItem>(null); //处理存储加载完成后去计算该车属于哪页
    const layoutCountRef = useRef<number>(null); //通道布局个数
    const playerRef = useRef<any>(null); //单设备播放
    const multiPlayerRef = useRef<any>(null); //多设备播放
    const switchPlayModeRef = useRef<boolean>(false); //切换凸出播放标志
    const audioRef = useRef<any>(null); //报警播放音量
    const deviceListRef = useRef<any>(null); //设备标签列表
    const contextHandleRef = useRef<any>(null); //提供context中数据
    const [frame, setFrame] = useState<FMPTYPE[]>([]);
    const [volume, setVolume] = useState<number>(DEFAULT_VOLUME);
    const [playerPlayModeAdas, setPlayerPlayModeAdas] = useState<PlayMode>();
    const [deviceList, setDeviceList] = useState<DeviceItem[]>([]); //所有设备列表
    const deviceListDataRef = useRef<DeviceItem[]>([]); //设备列表ref解决更新时序问题
    // const [deviceChannelData, setDeviceChannelData] = useState<ChannelData[]>([]);
    const [closedChannelData, setClosedChannelData] = useState<string[]>([]); //已关闭的通道
    const [playMode, setPlayMode] = useState<PlayModeType | null>(null); //播放模式 常规车辆或监控组
    const [modalVisible, setModalVisible] = useState<boolean>(false); //监控组保存弹框
    const [multiDevice, setMultiDevice] = useUpdateState(true); //是否是多设备模式
    const [currentDevice, setCurrentDevice] = useState<DeviceItem | null>(null); //当前播放设备，用于单设备播放数据
    const [saveGroupLoading, setSaveGroupLoading] = useState<boolean>(false);
    const [groupForm] = Form.useForm();
    const [allowAlarmsPromise] = useAllowAiType();
    const dispatch = useDispatch();
    const intercomParams = useRef<
        VehicleParams & DeviceParams & { optId?: string }
    >({});
    const eliminationAlarmDeviceRef = useRef<string[]>([]);
    const intercomRef = useRef<any>();
    const [intercomReady, setIntercomReady] = useState(false);
    const [playLock, setPlayLock] = useState(false); //播放锁
    const dispatchUPDATE_STATE_TYPE = (payload: any) => {
        dispatch({
            type: UPDATE_STATE_TYPE,
            payload,
        });
    };

    function _onIntercomStart(params = {}) {
        setIntercomReady(true);
        onIntercom?.({
            ...intercomParams.current,
            ...params,
        });
    }
    function _onAfterIntercom(params = {}) {
        setIntercomReady(false);
        onAfterIntercom?.({
            ...intercomParams.current,
            ...params,
        });
    }
    function _onIntercomError(params = {}) {
        setIntercomReady(false);
        onIntercomError?.({
            ...intercomParams.current,
            ...params,
        });
    }

    // 监控的设备是否发生变更，用于控制显示确认播放按钮
    const [playingDeviceList, setPlayingDeviceList] = useState(deviceList);

    // 是否是删除车辆、设备
    const isReduceDevice = deviceList.length < playingDeviceList.length;
    // 是否存在整体替换操作，将一批设备全量替换为另一批设备，这通常是监控组模式下切换组操作
    const [groupPlay, setGroupPlay] = useState(false);
    // 操作类型，区分是单/多设备删除
    const operateType = useRef<OperateType>();
    const getEqual = () => {
        const cloneDeviceList = [...deviceListDataRef.current];
        const clonePlayingDeviceList = [...playingDeviceList];
        // @ts-ignore
        const sortedPlayingDeviceList = clonePlayingDeviceList.sort(
            (a, b) => a.deviceId - b.deviceId,
        );
        // @ts-ignore
        const sortedDeviceList = cloneDeviceList.sort(
            (a, b) => a.deviceId - b.deviceId,
        );
        const isEqual =
            sortedPlayingDeviceList.length === sortedDeviceList.length &&
            sortedPlayingDeviceList.every(
                (item, index) =>
                    item.deviceId === sortedDeviceList[index].deviceId,
            );
        return isEqual;
    };
    useEffect(() => {
        // 监控组播放无需做确认操作，但单个删除需要
        const isMultipleVehicleOperate =
            operateType.current === OperateType.multiple;
        // 自动确认逻辑
        if (
            (playMode?.mode === 'group' &&
                // case 1: 监控组模式下，添加车辆、设备，自动确认
                (!isReduceDevice ||
                    // case 2: 监控组模式下，播放新的监控组，自动确认
                    groupPlay)) ||
            //  case 3: 非监控组模式下，批量删除（点击最右侧的叉叉）车辆，自动确认
            (isReduceDevice && isMultipleVehicleOperate)
        ) {
            confirmVehicleCollectionChanged();
            return;
        }
        const isEqual = getEqual();
        if (!playLock && !isEqual) {
            confirmVehicleCollectionChanged();
        }
    }, [deviceList, playingDeviceList, playMode]);

    useEffect(() => {
        const isEqual = getEqual();
        if(!playLock && !isEqual) {
            confirmVehicleCollectionChanged();
        }
    }, [playLock]);

    const updateVehicleCollectionByRemoveDevice = (
        removedDviceInfos: DeviceItem[],
        leftDevices?: DeviceItem[],
    ) => {
        if (leftDevices?.length === 0) {
            setVehicleCollection([]);
            vehicleCollectionRef.current = [];
            logoutSavePlayerState.reset();
            return;
        }
        if (removedDviceInfos.length === 0) return;
        const cloneVehicleCollection = cloneDeep(vehicleCollection);
        removedDviceInfos.forEach((deviceInfo) => {
            cloneVehicleCollection.forEach((item) => {
                if (item.vehicleId === deviceInfo.vehicleId) {
                    const filterDevices = item.deviceList.filter(
                        (device) => device.deviceId !== deviceInfo.deviceId,
                    );
                    item.deviceList = filterDevices;
                }
            });
        });
        setVehicleCollection(
            cloneVehicleCollection.filter((item) => item.deviceList.length > 0),
        );
        vehicleCollectionRef.current = cloneVehicleCollection.filter((item) => item.deviceList.length > 0);
    };
    const updateVehicleCollectionByRemoveDeviceRef = useRef(
        updateVehicleCollectionByRemoveDevice,
    );
    updateVehicleCollectionByRemoveDeviceRef.current =
        updateVehicleCollectionByRemoveDevice;

    const closeDevices = (
        removedDviceInfos: DeviceItem[],
        leftDevices: DeviceItem[],
    ) => {
        updateVehicleCollectionByRemoveDeviceRef.current(
            removedDviceInfos,
            leftDevices,
        );
        removedDviceInfos.forEach((deviceInfo) => {
            // 关闭所有
            if (leftDevices.length === 0) {
                setPlayLock(false);
                closeTalk();
                setLayoutCount(DEFAULT_LAYOUT_COUNT);
                closePlayer();
                setPlayMode(null);
            }

            // 关闭的凸出模式当前播放的设备
            if (
                !multiDevice &&
                currentDevice?.deviceId === deviceInfo.deviceId
            ) {
                setCurrentDevice(null);
                closePlayer();
                onHighlightPlay?.(null);
                setMultiDevice(true);
                return;
            }
            // 关闭通道 TODO 当前是一个一个销毁 还会 dispath 多次状态
            // 这里无需手动关闭，播放器内部会自行根据数据关闭
            // deviceInfo.deviceChannelList.forEach((item) => {
            //     // setTimeOut才能使state更新4次
            //     setTimeout(() => {
            //         multiPlayerRef.current?.close(deviceInfo.deviceId + '-' + item.channelNo);
            //     }, 0);
            // });
        });
    };

    useEffect(() => {
        if (multiDevice) {
            setCurrentDevice(null);
        }
    }, [multiDevice]);

    const _confirmVehicleCollectionChanged = () => {
        if (deviceListDataRef.current === playingDeviceList) return;
        const removedDevices = playingDeviceList.filter((item) => {
            const removed = !deviceListDataRef.current.find((device) => {
                return device.deviceId === item.deviceId;
            });
            return removed;
        });
        closeDevices(removedDevices, deviceListDataRef.current);
        // @ts-ignore
        playDevices();
        setPlayingDeviceList(deviceListDataRef.current);
        updateVehicleCollectionByRemoveDeviceRef.current(
            removedDevices,
            deviceListDataRef.current,
        );
        setGroupPlay(false);
    };

    const confirmVehicleCollectionChangedRef = useRef(
        _confirmVehicleCollectionChanged,
    );
    confirmVehicleCollectionChangedRef.current =
        _confirmVehicleCollectionChanged;

    const confirmVehicleCollectionChanged = () => {
        if (
            (!multiDevice &&
                // case 1: 突出模式下，增加设备，退出突出播放模式
                !isReduceDevice) ||
            // case 2: 突出模式下，移除当前突出播放设备，退出突出播放模式
            (isReduceDevice && !currentDevice)
        ) {
            setMultiDevice(true); // 退出突出播放模式
        }
        // 这里必须在下一个大循环处理，有以下目的
        // 1. 是为了确保multiDevice在变更后，currentDevice也更新完毕
        // 2. vehicleCollection状态能在closeDevice方法内（实际是其内调用的updateVehicleCollectionByRemoveDevice方法内）获取到最新值
        setTimeout(() => {
            confirmVehicleCollectionChangedRef.current();
        }, 0);
    };



    useEffect(() => {
        if (deviceListDataRef.current.length == 0) {
            setClosedChannelData([]);
            setFrame([]);
            setPlayerPlayModeAdas('normal');
            setVolume(DEFAULT_VOLUME);
            setLayoutCount(DEFAULT_LAYOUT_COUNT);
        }
    }, [deviceList]);
    useEffect(() => {
        contextHandleRef.current.setFrameRateAndBitRate(frame);
        contextHandleRef.current.setPlayerVolume(volume);
        multiDevice && closeTalk();
    }, [multiDevice]);
    useEffect(() => {
        g_emmiter.on('remove.monitor.group', (data) => {
            const newMonitoringGroupVehicleMap = {
                ...monitoringGroupVehicleMap,
            };
            delete newMonitoringGroupVehicleMap[data.vehicleId];
            setMonitoringGroupVehicleMap(newMonitoringGroupVehicleMap);
        });
        return () => {
            g_emmiter.off('remove.monitor.group');
        };
    }, []);
    const offLineMessage = (currentPlayMode?: PlayModeType) => {
        let offlineMessage = i18n.t('message', '车辆不在线');
        if (currentPlayMode?.mode === 'group') {
            offlineMessage = i18n.t('message', '无在线车辆');
        }
        message.warn({
            content: offlineMessage,
            key: 'vehicleOffLine',
        });
    };
    const _playVideo = async (
        vehicleList: VehiclePlayItem[],
        playerPlayMode: PlayModeType,
    ) => {
        if (playerPlayMode.mode === 'group') {
            logoutSavePlayerState.setAllPlayVehicleList(vehicleList as unknown as PlayVehicleListItem[]);
        }
        // todo  监控组播放时，加车，重新登录后新加的车并未被记忆
        if (vehicleList && vehicleList.length > 0) {
            if (recordStateMessage()) return;
            const list: VehicleItem[] = await getRealtimeVideoVehiclesDetail({
                vehicleIds: vehicleList.map((item) => item.vehicleId),
            });

            // 过滤关闭的设备
            const closeDeviceIds = closedDevices.map(item => item.deviceId);
            if (cachedPlayerStateRef.current && closeDeviceIds.length) {
                list.forEach(vehicle => {
                    vehicle.deviceList = vehicle.deviceList.filter(device => !closeDeviceIds.includes(device.deviceId));
                });
            }
            // 如果非重新登录恢复播放状态流程
            // 如果本次要打开的设备在已关闭的设备里，需要将这批设备从已关闭的设备里移除。
            // 这里使用cachedCloseDeviceIds，而不是closeDeviceIds，是因为closeDeviceIds会在恢复播放后就立即重置
            const cachedCloseDeviceIds = (logoutSavePlayerState.closedDevices || []).map(item => item.deviceId);
            if (!cachedPlayerStateRef.current && cachedCloseDeviceIds.length) {
                const reCoverDeviceIds: string[] = [];
                list.forEach(vehicle => {
                    vehicle.deviceList.forEach(device => {
                        if(cachedCloseDeviceIds.includes(device.deviceId)) {
                            reCoverDeviceIds.push(device.deviceId);
                        }
                    });
                });
                logoutSavePlayerState.removeClosedDevices(reCoverDeviceIds);
            };

            if (!list?.length) return;
            if (playerPlayMode.mode === 'group') {
                setGroupPlay(true);
            }
            // 将车组id附加到车辆上，打开车辆详情时需要车组id
            const newAllVehicleMap = { ...allVehicleMap };
            list.forEach((vehicleItem: VehicleItem) => {
                const vehicle = vehicleList.find(
                    (item) => vehicleItem.vehicleId === item.vehicleId,
                );
                // @ts-ignore
                if (vehicle) {
                    vehicleItem.fleetId = vehicle.fleetId;
                    // 存车辆信息，每次都要更新，防止实时移除设备和添加
                    newAllVehicleMap[vehicleItem.vehicleId] =
                        vehicleItem?.deviceList.map((item) => item.deviceId);
                }
                // 过滤已被移除的设备, 监控组才会有此情况
                if (playerPlayMode.mode === 'group') {
                    const filterDeviceList = vehicleItem.deviceList.filter(
                        (deviceItem) =>
                            vehicle?.deviceIdList?.includes(
                                deviceItem.deviceId,
                            ),
                    );
                    vehicleItem.deviceList = filterDeviceList;
                }
            });

            // 保存所有车辆设备
            setAllVehicleMap(newAllVehicleMap);
            let onLineVehicles = [];
            // 如果是报警过来的
            if (eliminationAlarmDeviceRef.current.length) {
                list.forEach((vehicleItem) => {
                    vehicleItem.deviceList = vehicleItem.deviceList?.filter(
                        (deviceItem) =>
                            !eliminationAlarmDeviceRef.current.includes(
                                deviceItem.deviceId,
                            ),
                    );
                });
            }

            eliminationAlarmDeviceRef.current = [];
            if (playerPlayMode.mode === 'group') {
                setPlayerPlayModeAdas('normal');
                contextHandleRef.current.setFrameRateAndBitRate([]);
                contextHandleRef.current.setPlayerVolume(DEFAULT_VOLUME);
                setClosedChannelData([]);
                onLineVehicles = list.filter(
                    (item: VehicleItem) => item.onlineState,
                );
                if (onLineVehicles.length === 0) {
                    offLineMessage(playerPlayMode);
                    return;
                }

                // 如果是凸出模式播放，需要修改为平铺模式后再初始化播放器
                initPlay(list, playerPlayMode);
            } else {
                initPlay(list, playerPlayMode);
            }
        } else {
            offLineMessage(playerPlayMode);
        }
    };
    const { run: playVideo } = useDebounceFn(_playVideo, {
        wait: 300,
    });

    const initPlay = (
        vehicleList: VehicleItem[],
        playerPlayMode: PlayModeType,
    ) => {
        // 不存在播放模式的时候说明是第一次进入，有播放模式就不用再存储了
        if (!playMode?.mode) {
            setPlayMode(playerPlayMode);
            // 第一次如果是常规车辆播放，情况原始监控组数据
            playerPlayMode.mode === 'normal' &&
                setMonitoringGroupVehicleMap({});
        }
        // 常规车辆播放且不是缓存车辆播放
        if (playerPlayMode.mode === 'normal' && vehicleList.length === 1) {
            const vehicle = vehicleList[0];
            const vehicleIndex = vehicleCollection.findIndex(
                (item) => item.vehicleId === vehicle.vehicleId,
            );
            // 如果播放中内没有该车，加入播放器
            if (vehicleIndex < 0 || !diffIncludeDevice(vehicleList)) {
                // 新增单车时清空此车已关闭的通道缓存数据
                const deviceIdList = vehicle.deviceList.map(
                    (item) => item.deviceId,
                );
                setClosedChannelData(
                    closedChannelData.filter(
                        (item) =>
                            !deviceIdList.includes(getDeviceIdByChannel(item)),
                    ),
                );
                logoutSavePlayerState.setAllPlayVehicleList([{
                    ...vehicle,
                    deviceIdList
                }] as unknown as PlayVehicleListItem[]);
                // 如果是监控组状态需要弹框确认加入监控组
                if (playMode?.mode === 'group' && !isInMonitorMap(vehicle)) {
                    let contentMsg = i18n.t(
                        'message',
                        '确定将“{vehicleNumber}”车辆添加到该监控组吗',
                        {
                            vehicleNumber: vehicle.vehicleNumber,
                        },
                    );

                    if (vehicleCollection.length >= 50) {
                        contentMsg = i18n.t(
                            'message',
                            '监控组车辆数已达上限，添加后将自动替换最早添加的车辆，确定将“{vehicleNumber}”车辆添加到该监控组吗？',
                            {
                                vehicleNumber: vehicle.vehicleNumber,
                            },
                        );
                    }
                    addGroupModal(vehicleList, contentMsg, true);
                    return;
                }
                if (!vehicle.onlineState) {
                    offLineMessage();
                    return;
                }
                checkFlowLimit(vehicleList);
                setVehicleDevice([vehicle, ...vehicleCollectionRef.current]);
            } else {
                if (playMode?.mode === 'group' && !isInMonitorMap(vehicle)) {
                    const contentMsg = i18n.t(
                        'message',
                        '确定将“{vehicleNumber}”车辆添加到该监控组吗',
                        {
                            vehicleNumber: vehicle.vehicleNumber,
                        },
                    );
                    addGroupModal(vehicleList, contentMsg, false);
                } else {
                    checkFlowLimit(vehicleList);
                }
                layoutLoadVehicleRef.current = vehicle;
                if (multiDevice === false) {
                    // todo 凸出模式时，选中存在的常规车辆，需重新加载多设备播放器，然后再选中通道
                    setVehicleDevice([...vehicleCollection]);
                    return;
                }
                //如果播放器内有该车，滚动到车辆，并且通道选中第一个
                scrollAndTurnPage(vehicle);
            }
        } else {
            // 流量超额提示
            checkFlowLimit(vehicleList);
            setVehicleDevice([...vehicleList]);
            storeMonitorMap(vehicleList);
            setPlayMode(playerPlayMode);
        }
    };
    const addGroupModal = (
        vehicleList: VehicleItem[],
        contentMsg: any,
        refresh: boolean,
    ) => {
        const vehicle = vehicleList[0];
        // 先提示车辆不在线，再提示操作成功，否则会同时出现
        if (!vehicle.onlineState) {
            offLineMessage();
        }
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '添加确认'),
            content: contentMsg,
            icon: <IconRequest />,
            onOk: async () => {
                // 加入监控组回调
                const deviceIdList = vehicle.deviceList.map(
                    (item) => item.deviceId,
                );
                const success = await onClickSaveMonitorGroup?.({
                    type: 'edit',
                    vehicleList: [
                        {
                            vehicleId: vehicle.vehicleId,
                            deviceIdList: deviceIdList,
                        },
                    ],
                    monitorFleetId: playMode?.monitorFleetId,
                });
                const newVehicleMap = { ...monitoringGroupVehicleMap };
                newVehicleMap[vehicle.vehicleId] = deviceIdList;
                if (success) {
                    modal.destroy();
                    setMonitoringGroupVehicleMap(newVehicleMap);
                    if (!vehicle.onlineState) {
                        return;
                    }
                    checkFlowLimit(vehicleList);
                    if (refresh) {
                        setVehicleDevice([vehicle, ...vehicleCollection]);
                    }
                }
            },
            onCancel: () => {
                logoutSavePlayerState.removePlayVehicleList(vehicle as unknown as PlayVehicleListItem);
            }
        });
    };
    // 判断点击的车是否全部设备都在播放中,
    const diffIncludeDevice = (newVehicleList: VehicleItem[]) => {
        let isSame = true;
        const oldDevices = deviceListDataRef.current.map((device) => device.deviceId);
        newVehicleList.forEach((vehicle) => {
            vehicle.deviceList.forEach((device) => {
                if (
                    device.onlineState &&
                    !oldDevices.includes(device.deviceId)
                ) {
                    isSame = false;
                }
            });
        });
        return isSame;
    };
    const checkFlowLimit = (vehicleList: VehicleItem[]) => {
        vehicleList.forEach((item) => {
            const flowLimit = item.deviceList.some(
                (device) => device.onlineState && device.flowLimit,
            );
            if (flowLimit) {
                message.warn({
                    content: i18n.t('message', '流量使用超额，功能暂停使用'),
                    key: 'flowLimit',
                });
            }
        });
    };
    // 根据当前播放车辆，滚动到对应设备和翻页到通道
    const scrollAndTurnPage = useCallback(
        (vehicleOrDeviceId: VehicleItem | string) => {
            let currentDeviceId: string;
            if (typeof vehicleOrDeviceId == 'string') {
                currentDeviceId = vehicleOrDeviceId;
            } else {
                currentDeviceId = vehicleOrDeviceId.deviceList?.[0].deviceId;
            }
            deviceListRef.current?.scrollToDevice(currentDeviceId);
            // 计算当前设备属于第几页，并选中设备第一个通道
            const currentPage = contextHandleRef.current?.getCurrentPage();
            const page = contextHandleRef.current?.getPage(currentDeviceId);
            if (!page) return;
            // 当播放中的通道存在该设备---选中设备通道
            if (page === currentPage) {
                contextHandleRef.current?.setActiveChannelByDeviceId(
                    currentDeviceId,
                );
            } else {
                contextHandleRef.current?.setPage(page);
                contextHandleRef.current?.setActiveChannelByDeviceId(
                    currentDeviceId,
                );
            }
        },
        [],
    );
    // 保存监控组原始数据,下次点击常规车辆时，判断是否是监控组内的车或设备
    const storeMonitorMap = (vehicleList: VehicleItem[]) => {
        const vehicleMap = {};
        vehicleList.forEach((item) => {
            vehicleMap[item.vehicleId] = item.deviceList.map(
                (item) => item.deviceId,
            );
        });
        setMonitoringGroupVehicleMap(vehicleMap);
    };
    // 判断是否是该监控组的车或设备
    const isInMonitorMap = (vehicle: VehicleItem) => {
        // 判断车是否在内
        if (monitoringGroupVehicleMap[vehicle.vehicleId]) {
            // 判断设备是否全部在内
            // @ts-ignore
            const devicesIn = vehicle.deviceList.every((device: DeviceItem) =>
                monitoringGroupVehicleMap[vehicle.vehicleId].includes(
                    device.deviceId,
                ),
            );
            return devicesIn;
        }
        return false;
    };

    const setVehicleDevice = (vehicleList: VehicleItem[]) => {
        _setVehicleDevice(vehicleList);
    };
    const _setVehicleDevice = (vehicleList: VehicleItem[]) => {
        let storeVehicleList = unionBy(vehicleList, 'vehicleId');
        // 长度超过50时，删除旧的
        if (vehicleList.length > MAX_VEHICLE) {
            storeVehicleList = vehicleList.slice(0, MAX_VEHICLE);
            if (playMode?.mode !== 'group') {
                const modal = StarryModal.confirm({
                    centered: true,
                    title: i18n.t('message', '添加确认'),
                    content: i18n.t(
                        'message',
                        '播放的车辆数已达上限，添加后将自动替换最早添加的车辆，确定将“{vehicleNumber}”车辆添加到播放列表吗？',
                        {
                            vehicleNumber: vehicleList[0]?.vehicleNumber,
                        },
                    ),
                    icon: <IconRequest />,
                    onOk: async () => {
                        // 加入监控组回调
                        addStoreList(storeVehicleList);
                        modal.destroy();
                    },
                });
            } else {
                addStoreList(storeVehicleList);
            }
        } else {
            addStoreList(storeVehicleList);
        }
    };
    const addStoreList = (storeVehicleList: VehicleItem[]) => {
        const parsedDeviceInfo = parseDeviceInfo(storeVehicleList);
        deviceListDataRef.current = parsedDeviceInfo;
        setDeviceList(parsedDeviceInfo);
        storeVehicleList.forEach((device) => {
            device.deviceList = device.deviceList.filter(
                (item) => item.onlineState,
            );
        });
        vehicleCollectionRef.current = storeVehicleList;
        setVehicleCollection(storeVehicleList);
    };
    const _playDevices = async (deviceInfo?: DeviceItem, otherConfig?: any) => {
        let currentPlayer = multiPlayerRef; //当前播放器
        // 单/多设备播放
        const deviceChannelData = parseChannelData(
            deviceInfo ? [deviceInfo] : deviceListDataRef.current,
            'MINOR',
        );
        if (deviceInfo) currentPlayer = playerRef; //当前播放器
        // 过滤已关闭通道
        const realChannelData = deviceChannelData.filter(
            (item) => !closedChannelData.includes(item.channel),
        );
        if(realChannelData.length) {
            setPlayLock(true);
        }
        console.log('start play Promise.all', realChannelData);
        const [mosaic, { allowAlarms }] = await Promise.all([
            mosaicManager.getVideoMosaicConfigValue(MosaicTypeEnum.live),
            allowAlarmsPromise
        ]);
        const videoData = {
            deviceChannelData: realChannelData,
            mediaProtocol: 'HTTP_FLV',
            quality: 'SMOOTH',
            mosaic,
        };
        await currentPlayer.current?.play(
            {
                videoData,
                layoutType: deviceInfo
                    ? LAYOUT_TYPE.highlight
                    : LAYOUT_TYPE.tile,
                layoutCount: deviceInfo
                    ? 4
                    : layoutCount || //第一次播放有值，播放后清空，以ref为准
                      layoutCountRef.current ||
                      DEFAULT_LAYOUT_COUNT, //默认通道布局
                ...otherConfig,
                lastChannelState: cachedPlayerStateRef.current,
                allowAlarms,
            },
            playMode?.mode,
        );
        if (currentPlayer.current && realChannelData.length > 0) {
            // 置空，下次以ref数量为准
            setLayoutCount(null);
        }
        // 播放器初始化完成后，清空用于恢复播放的一些状态值
        resetLogoutSavePlayerStateRef.current();
    };
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.live);
    useUpdateEffect(() => {
        playDevices();
    }, [hasMosaic]);
    const { run: playDevices } = useDebounceFn(_playDevices, {
        wait: 30,
    });
    const onDeviceSelect = (deviceInfo: DeviceItem | null) => {
        // 选中凸出模式
        if (deviceInfo) {
            // 如果选中的设备，并没有开始播放（尚未进行确认播放，仅进行了预选），则忽略选中操作
            const deviceHasPlay = playingDeviceList.find(
                (item) => item.deviceId === deviceInfo.deviceId,
            );
            closeTalk();
            setCurrentDevice(deviceInfo);
            onHighlightPlay?.(deviceInfo);
            // 如果存在currentDevice，说明是切换其他设备的凸出模式，setCurrentDevice是公共的，放在前面不影响后面判断
            setMultiDevice(false, () => {
                deviceHasPlay && playDevices(deviceInfo);
            });
        } else {
            onHighlightPlay?.(null);
            // 取消凸出模式
            setCurrentDevice(null);
            setMultiDevice(true, () => {
                playDevices();
            });
        }
    };
    // 保存播放的通道，下次播放时只播放记忆的通道
    const onChannelChange = (channelData: string[]) => {
        if (channelData.length == 0) return;
        // 多设备
        if (multiDevice) {
            // 计算是否有全部通道都关闭完的设备
            const existDeviceList = [
                ...new Set(getDeviceIdByList(channelData)),
            ];
            // 过滤已不存在的通道
            console.log('trigger', channelData)
            // 间接被移除的设备
            const removedDeviceList: DeviceItem[] = [];
            const filterDevices = deviceListDataRef.current.filter((item) => {
                const flag = existDeviceList.includes(item.deviceId);
                if (!flag) {
                    removedDeviceList.push(item);
                }
                return flag;
            });
            // 当设备通道被关闭完后，移除对应设备，
            if(removedDeviceList.length) {
                logoutSavePlayerState.setClosedDevices([...(logoutSavePlayerState.closedDevices || []), ...removedDeviceList]);
            }
            if (!switchPlayModeRef.current) {
                deviceListDataRef.current = filterDevices;
                setDeviceList(filterDevices);
                setPlayingDeviceList(filterDevices);
            }
            // 存储已关闭的通道数据
            const deviceChannelData = parseChannelData(filterDevices, 'MINOR');
            const newClosedChannelData = deviceChannelData.filter(
                (item: DeviceChannelItem) =>
                    !channelData.includes(item.channel),
            );
            // 多个的 覆盖
            setClosedChannelData(
                newClosedChannelData.map(
                    (item: DeviceChannelItem) => item.channel!,
                ),
            );
            switchPlayModeRef.current = false;
        } else if (channelData.length) {
            // 单设备，
            const device = getDeviceByChannel(channelData[0], deviceListDataRef.current);
            const deviceChannelData = parseChannelData([device], 'MINOR');
            const newClosedChannelData =
                deviceChannelData.filter(
                    (item: DeviceChannelItem) =>
                        !channelData.includes(item.channel),
                ) || [];

            // 单个的增加
            switchPlayModeRef.current = true;
            setClosedChannelData([
                ...closedChannelData,
                ...newClosedChannelData.map(
                    (item: DeviceChannelItem) => item.channel!,
                ),
            ]);
        }
        setPlayLock(false);
    };
    const lastMultiDeviceRef =  useRef(multiDevice);
    // 记录当前通道布局，返回时持续
    const onLayoutCountChange = (count: number) => {
        // 切换播放模式时，播放器会触发卸载动作，在卸载动作中，会重置布局为4宫格布局，此时我们希望忽略这次的回调
        const multiDeviceChange = lastMultiDeviceRef.current !== multiDevice;
        if (multiDevice && !multiDeviceChange) {
            layoutCountRef.current = count;
            // 保存布局以便退出登录后恢复
            // logoutSavePlayerState.updatePlayerState({ ...logoutSavePlayerState.state, layoutCount: count });
        }
        // 保存布局以便退出登录后恢复
        logoutSavePlayerState.updatePlayerState({ ...logoutSavePlayerState.state, layoutCount: count });

        lastMultiDeviceRef.current = multiDevice;
    };
    const getPlayerInfo = () => {
        return {
            playMode: playMode,
            vehicleList: vehicleCollection.map((vehicle) => {
                return {
                    vehicleId: vehicle.vehicleId,
                    fleetId: vehicle.fleetId,
                    deviceIdList: vehicle.deviceList.map(
                        (device) => device.deviceId,
                    ),
                };
            }),
        };
    };
    // 当前激活通道触发时
    useImperativeHandle(ref, () => {
        return {
            playVideo,
            getOperateState,
            getPlayerInfo,
        };
    });
    const getOperateState = () => {
        return {
            recordState:
                playerRef.current?.getRecordState?.() == 'start' ? true : false,
            talkState: intercomReady,
        };
    };
    // 关闭所有
    const onCloseAll = () => {
        operateType.current = OperateType.multiple;
        deviceListDataRef.current = [];
        setDeviceList([]);
        setVehicleCollection([]);
        vehicleCollectionRef.current = [];
        logoutSavePlayerState.reset();
        setTimeout(() => {
            // 关闭全部时，无需手动确认
            confirmVehicleCollectionChangedRef.current();
        }, 0);
    };
    // 保存监控组
    const onSaveMonitorGroup = async () => {
        groupForm.resetFields();
        setModalVisible(true);
    };
    const onSaveMonitorGroupOk = async () => {
        groupForm.validateFields().then(async (values) => {
            setSaveGroupLoading(true);
            const monitorGroupName = values.monitorGroupName.trim();
            const vehicleMap = {};
            const vehicleList = [];
            deviceList.forEach((item) => {
                if (vehicleMap[item.vehicleId]) {
                    vehicleMap[item.vehicleId].push(item.deviceId);
                } else {
                    vehicleMap[item.vehicleId] = [item.deviceId];
                }
            });
            for (const key in vehicleMap) {
                vehicleList.push({
                    vehicleId: key,
                    deviceIdList: vehicleMap[key],
                });
            }
            const success = await onClickSaveMonitorGroup?.({
                type: 'add',
                vehicleList,
                monitorFleetId: playMode?.monitorFleetId,
                monitorGroupName,
            });
            setSaveGroupLoading(false);
            if (success) {
                setPlayMode({
                    mode: 'group',
                    monitorFleetId:
                        (success as string) || playMode?.monitorFleetId,
                });
                setModalVisible(false);
                groupForm.resetFields();
            }
        });
    };

    const [closedDevices, setClosedDevices] = useState<DeviceItem[]>([]);

    // 关闭设备标签
    const onDeviceClose = (deviceInfo: DeviceItem) => {
        operateType.current = OperateType.single;
        const leftDevices = deviceListDataRef.current.filter(
            (item) => item.deviceId !== deviceInfo.deviceId,
        );
        // 移除当前突出播放的设备
        // if (deviceInfo.deviceId == currentDevice?.deviceId) {
        //     setCurrentDevice(null);
        // }
        // 过滤删除无设备的车辆
        deviceListDataRef.current = leftDevices;
        setDeviceList(leftDevices);
        updateVehicleCollectionByRemoveDeviceRef.current(
            [deviceInfo],
            leftDevices,
        );
        logoutSavePlayerState.setClosedDevices([...closedDevices, deviceInfo]);
    };

    // 报警联动策略
    const linkageHandle = async (eventData: any) => {
        const {
            vehicleId,
            voicePrompt,
            mapVehicleLock,
            openVideo,
            deviceId,
            alarmType,
        } = eventData;
        // 发出报警声音
        if (voicePrompt) {
            audioRef.current?.play(eventData);
        }
        // 设备标签无车
        if (mapVehicleLock) {
            // 定位标签
            scrollAndTurnPage(deviceId);
        }
        // 凸出播放和监控组播放场景时，不会触发实时报警的播放
        if (!multiDevice || playMode?.mode === 'group') return;
        const deviceIndex = deviceList.findIndex(
            (item) => item.deviceId === deviceId,
        );
        // 更改获取车辆详情，若接口报错，不进行提示，不进行下一步处理
        let detailListInfo = await getRealtimeVideoVehiclesDetail({
            vehicleIds: [vehicleId],
        });
        // parse设备数据
        const vehicleInfo = detailListInfo?.[0] || {};
        // 存储是否多设备信息

        // 如果传了设备ID则打开指定设备的视频
        let deviceInfo = deviceId
            ? // @ts-ignore
              (vehicleInfo.deviceList || []).find(
                  (item: any) => item.deviceId === deviceId,
              )
            : // @ts-ignore
              vehicleInfo.deviceList?.[0];
        // 处理合并设备数据，将车辆车牌号等数据合并到设备数据中
        deviceInfo = {
            ...deviceInfo,
            // @ts-ignore
            vehicleNumber: vehicleInfo.vehicleNumber,
            vehicleId: vehicleInfo.vehicleId,
            fleetId: vehicleInfo.fleetList?.[0]?.fleetId,
        };
        // 无车辆详情
        if (Object.keys(vehicleInfo).length === 0 || deviceInfo?.flowLimit)
            return;
        // 多设备报警时将没报警的设备提出来，在后面播放的时候，不把没报警的设备加入列表
        // @ts-ignore
        eliminationAlarmDeviceRef.current = (vehicleInfo.deviceList || [])
            .map((item) => {
                if (item.deviceId !== deviceInfo?.deviceId) {
                    return item.deviceId;
                }
                return null;
            })
            .filter((item) => item);

        if (!allVehicleMap[vehicleInfo.vehicleId]) {
            const newAllVehicleMap = { ...allVehicleMap };
            newAllVehicleMap[vehicleInfo.vehicleId] =
                vehicleInfo.deviceList.map((item) => item.deviceId);
            setAllVehicleMap(newAllVehicleMap);
        }
        vehicleInfo.deviceList = [deviceInfo];
        // 打开凸出播放视频
        if (openVideo) {
            onAlarmVehicle?.(eventData);
            if (deviceIndex > -1) {
                // 存在，直接打开
                deviceListRef.current?.checkDevice(deviceInfo);
            } else {
                // 不存在，保存到车辆和设备list中，然后选中打开
                // @ts-ignore
                setVehicleDevice([vehicleInfo, ...vehicleCollection]);
                setTimeout(() => {
                    confirmVehicleCollectionChangedRef.current();
                    deviceListRef.current?.checkDevice(deviceInfo);
                }, 0);
            }
        }
    };

    // 格式化车辆为设备列表
    const parseDeviceInfo = (vehicleList: any) => {
        const deviceList: DeviceItem[] = [];
        vehicleList.forEach((vehicle: VehicleItem) => {
            vehicle.deviceList?.forEach((device) => {
                deviceList.push({
                    ...device,
                    vehicleNumber: vehicle.vehicleNumber,
                    vehicleId: vehicle.vehicleId,
                    fleetId: vehicle.fleetId,
                });
            });
        });
        return deviceList.filter((item) => item.onlineState);
    };
    // 格式化设备为通道列表
    const parseChannelData = (deviceList: DeviceItem[], streamType: string) => {
        const channelData: ChannelData[] = deviceList.reduce(
            (accumulator, currentElement) => {
                const { deviceChannelList, ...rest } = currentElement;
                deviceChannelList.forEach((item) => {
                    // @ts-ignore
                    accumulator.push({
                        ...item,
                        channelAlias: `${rest.vehicleNumber}(${item.channelAlias})`,
                        originChannelAlias: item.channelAlias,
                        channel: currentElement.deviceId + '-' + item.channelNo,
                        ...rest,
                        streamType: streamType,
                    });
                });
                return accumulator;
            },
            [],
        );
        return channelData;
    };
    const recordStateMessage = () => {
        if (playerRef.current?.getRecordState?.() == 'start') {
            message.error({
                content: i18n.t('message', '请先结束录制'),
                key: 'overRecord',
            });
            return true;
        }
        if (intercomReady) {
            message.error({
                content: i18n.t('message', '请先结束对讲'),
                key: 'overRecord',
            });
            return true;
        }
        return false;
    };
    // 关闭对讲
    const closeTalk = () => {
        // 关闭对讲
        if (intercomReady) {
            intercomRef.current?.stopIntercom();
            dispatchUPDATE_STATE_TYPE({
                talking: true,
            });
        }
    };
    useEffect(() => {
        if (!intercomReady) {
            dispatchUPDATE_STATE_TYPE({
                talking: false,
            });
        }
    }, [intercomReady]);
    const closePlayer = () => {
        multiPlayerRef.current?.closeAll?.();
        playerRef.current?.closeAll?.();
    };
    const sendMessage = () => {
        onMessageDistribution?.(currentDevice!);
    };

    const beforeIntercomPromise = async (deviceInfo: any) => {
        if (!currentDevice?.vehicleId)
            return Promise.reject({
                isNext: false,
            });
        if (recordStateMessage())
            return Promise.reject({
                isNext: false,
            });
        const params = {
            vehicleId: currentDevice.vehicleId,
            vehicleNumber: currentDevice.vehicleNumber,
            deviceId: currentDevice.deviceId,
            authId: currentDevice.authId,
        };
        const { optId } = onBeforeIntercom?.(params) || {};
        // @ts-ignore
        params.optId = optId;
        intercomParams.current = params;
        return Promise.resolve({
            isNext: true,
            optId,
        });
    };
    const controlRender = () => {
        return (
            <span className="control-render-warp">
                <IntercomInPlayer
                    key={'intercom'}
                    ref={intercomRef}
                    deviceInfo={currentDevice}
                    vehicleId={currentDevice?.vehicleId}
                    onIntercomClose={_onAfterIntercom}
                    onIntercomError={_onIntercomError}
                    onIntercomStart={_onIntercomStart}
                    beforeIntercomPromise={beforeIntercomPromise}
                />
                <Auth code="@base:@page:realtime.monitoring@action:messagesend">
                    <Tooltip overlay={i18n.t('name', '消息下发')}>
                        <span
                            className="control-render-message-send"
                            onClick={sendMessage}
                        >
                            <IconTextDownload />
                        </span>
                    </Tooltip>
                </Auth>
            </span>
        );
    };
    const renderPlayerRight = (components: any[]) => {
        if (!Array.isArray(components)) return components;
        const newComponents = components.slice(0);
        const index = (newComponents || []).findIndex(
            (item) => item.key == 'LiveVideoMoreSetting',
        );
        if (index > -1) {
            newComponents.splice(
                index,
                1,
                <MultiMoreSetting key={'MultiMoreSetting'} />,
            );
        }
        return newComponents;
    };
    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '监控组名称不能为空'));
    };
    const onFrameRateAndBitRateChange = (frame: FMPTYPE[]) => {
        setFrame(frame);
    };
    const onPlayerModeAdasChange = (playMode: PlayMode) => {
        setPlayerPlayModeAdas(playMode);
    };
    const onPlayerVolumeChange = (volume: number) => {
        setVolume(volume);
    };
    const onPlayerStatusChange = (playerStatus: PlayerStatus) => {
        intercomRef.current?.setDisabledTalkButton(playerStatus !== 'playing');
    };
    const onLayoutLoaded = useCallback(() => {
        if (layoutLoadVehicleRef.current) {
            scrollAndTurnPage(layoutLoadVehicleRef.current);
            layoutLoadVehicleRef.current = null;
        }
        setPlayLock(false);
    }, []);

    const cachedPlayerStateRef = useRef<CachePlayerState | null>();
    const handleChannelLayoutChange = useCallback((state) => {
        state.layoutCount =  layoutCountRef.current;
        // 当前并未使用播放器内部给出的通道信息，而是直接用的closedChannelData里维护的
        logoutSavePlayerState.updatePlayerState({ ...state, currentDevice });
    }, [currentDevice]);

    useEffect(() => {
        if (playingDeviceList.length === 0) {
            logoutSavePlayerState.reset();
        }
    }, [playingDeviceList]);

    useEffect(() => {
        logoutSavePlayerState.updateClosedChannelData(closedChannelData);
    }, [closedChannelData]);

    useEffect(() => {
        // playMode 表示当前正在播放的模式：监控组 、常规播放，它妥善的处理了在播放监控组时，切换到常规播放往监控组加车的场景（仍旧能维持监控组播放状态）
        logoutSavePlayerState.setPlayerPlayMode(playMode);
    }, [playMode]);

    const onDeviceSelectRef = useLatest(onDeviceSelect);
    const playingDeviceListRef = useLatest(playingDeviceList);
    const resetLogoutSavePlayerStateRef = useRef(() => {
        // setClosedChannelData([]);
        setClosedDevices([]);
        cachedPlayerStateRef.current = null;
    });
    useEffect(() => {
        logoutSavePlayerState.getCache().then(cachedPlayerState => {
            if (cachedPlayerState) {
                // 通知tree切换到监控组tab
                setTimeout(() => {
                    onRecoverMonitorGroup?.();
                }, 500);

                logoutSavePlayerState.clearCache();
                const { currentDevice, closedChannelData, closedDevices, allPlayVehicleList, playerPlayMode, ...cachedOtherPlayerState } = cachedPlayerState;
                if (closedChannelData) {
                    setClosedChannelData(closedChannelData);
                }
                if (closedDevices) {
                    setClosedDevices(closedDevices);
                }
                playVideo(allPlayVehicleList, playerPlayMode);
                cachedPlayerStateRef.current = cachedOtherPlayerState;
                // fix: 125267 【SIT-beta】-【监控中心-车辆监控】- 视频模式突出模式播放时，用户退出登录重新登录，取消突出模式，页面显示的默认通道变为4通道
                // 此处本质问题为播放器回调onLayoutCountChange首次执行给出的通道数量异常（和实际渲染的数量不一致，例如实际渲染为9，但这个回调给的是4）导致。这里需要推动播放器修复
                layoutCountRef.current = cachedOtherPlayerState.layoutCount;

                const waiting = (fn, condition) => {
                    const deviceHasPlay = condition();
                    if (deviceHasPlay) {
                        fn();
                    } else {
                        setTimeout(() => {
                            waiting(fn, condition);
                        }, 100);
                    }
                };
                if (currentDevice) {
                    const condition = () => {
                        const deviceHasPlay = playingDeviceListRef.current.find(
                            (item) => item.deviceId === currentDevice.deviceId,
                        );
                        return deviceHasPlay;
                    };
                    waiting(() => {
                        onDeviceSelectRef.current?.(currentDevice);
                    }, condition);
                }
            }
        });
        const doCache = () => {
            // 监控组下，退出登录才记忆视频播放状态
            if (logoutSavePlayerState.playerPlayMode?.mode == 'group' && logoutSavePlayerState.playerPlayMode?.monitorFleetId) {
                return logoutSavePlayerState.cache();
            }
        };
        lifecycle.logout.addStandardStep('before-navigation', async () => {
            return doCache();
        });
        lifecycle.threePartyAuthorizedLogin.addStandardStep('before-navigation', () => {
            return doCache();
        });
        lifecycle.tenant.exchangeTenant.addStandardStep('before-navigation', () => {
            return doCache();
        });
        return () => {
            logoutSavePlayerState.reset();
            lifecycle.logout.removeStandardStep('before-navigation');
            lifecycle.threePartyAuthorizedLogin.removeStandardStep('before-navigation');
            lifecycle.tenant.exchangeTenant.removeStandardStep('before-navigation');
        };
    }, []);

    return (
        <div className="multi-realtime-video-wrap">
            <div className="multi-realtime-video-operate">
                {vehicleCollection.length > 0 && (
                    <DeviceListMenu
                        ref={deviceListRef}
                        onDeviceSelect={onDeviceSelect}
                        onDeviceClose={onDeviceClose}
                        onCloseAll={onCloseAll}
                        allVehicleMap={allVehicleMap}
                        playMode={playMode?.mode}
                        multi={multiDevice}
                        onSaveMonitorGroup={onSaveMonitorGroup}
                        deviceList={deviceList}
                        recordStateMessage={recordStateMessage}
                        vehicleStateConfig={vehicleStateConfig}
                        currentDevice={currentDevice}
                        alarmVehicleList={[]}
                    />
                )}
            </div>
            {playingDeviceList.length > 0 ? (
                <div>
                    <div className="multi-realtime-video-player">
                        {multiDevice ? (
                            <MultiPlayer
                                multi={multiDevice}
                                isLive={true}
                                showPlaceHolder={false}
                                defaultPlayMode={playerPlayModeAdas}
                                ref={multiPlayerRef}
                                pageCode="@base:@page:realtime.monitoring"
                                authWidgets={authWidgets}
                                openMode="monitorVideoCloseTime"
                                moveInRefresh={true}
                                renderControlBarRight
                                onLayoutLoaded={onLayoutLoaded}
                                onChannelLayoutChange={handleChannelLayoutChange}
                            />
                        ) : (
                            <WebPlayer
                                isLive={true}
                                needReconnect={false}
                                defaultPlayMode={playerPlayModeAdas}
                                ref={playerRef}
                                showPlaceHolder={false}
                                pageCode="@base:@page:realtime.monitoring"
                                authWidgets={authWidgets}
                                openMode="monitorVideoCloseTime"
                                moveInRefresh={true}
                                renderControlBarRight
                                controlRender={controlRender()}
                                renderPlayerRight={renderPlayerRight}
                                // @ts-ignore
                                disabledWidgets={
                                    intercomReady ? disabledWidgets : []
                                }
                                hiddenWidgets={
                                    intercomReady ? hiddenWidgets : []
                                }
                                emptyText={i18n.t('message', '无数据')}
                                onPlayerStatusChange={onPlayerStatusChange}
                                onChannelLayoutChange={handleChannelLayoutChange}
                                getRealtimeVideoRecordModal={getRealtimeVideoRecordModal}
                            />
                        )}
                    </div>
                </div>
            ) : (
                <div className="multi-placeholder-wrapper">
                    {defaultPlaceholder}
                    <span className="placeholder-text">
                        {i18n.t('name', '请选择车辆或监控组进行视频监控')}
                    </span>
                </div>
            )}

            <VideoAlarmHandle handleEfficient={linkageHandle} />

            <StarryModal
                destroyOnClose
                maskClosable={false}
                centered
                title={i18n.t('name', '保存监控组')}
                visible={modalVisible}
                onCancel={() => {
                    setModalVisible(false);
                }}
                onOk={onSaveMonitorGroupOk}
                width={420}
                confirmLoading={saveGroupLoading}
                wrapClassName="add-monitor-group-modal"
                focusTriggerAfterClose={false}
            >
                <div className="check-add-type-wrapper">
                    <Form form={groupForm} layout="vertical">
                        <Form.Item
                            key="monitorGroupName"
                            label={i18n.t('name', '监控组名称')}
                            name="monitorGroupName"
                            className="form-item"
                            rules={[
                                {
                                    required: true,
                                    validator: checkSpace,
                                },
                                { validator: groupIllegalCharacter },
                            ]}
                        >
                            <Input
                                title={i18n.t('message', '请输入监控组名称')}
                                placeholder={i18n.t(
                                    'message',
                                    '请输入监控组名称',
                                )}
                                maxLength={50}
                                allowClear
                            />
                        </Form.Item>
                    </Form>
                </div>
            </StarryModal>

            <AlarmAudio ref={audioRef} />
            <PlayerContextProvider>
                <ContextHandle
                    ref={contextHandleRef}
                    onChannelChange={onChannelChange}
                    onLayoutCountChange={onLayoutCountChange}
                    onPlayerVolumeChange={onPlayerVolumeChange}
                    onPlayerModeAdasChange={onPlayerModeAdasChange}
                    onFrameRateAndBitRateChange={onFrameRateAndBitRateChange}
                />
            </PlayerContextProvider>
        </div>
    );
};
export default withSharePropsHOC<
    MultiChannelRealTimeVideoProps,
    MultiChannelRealTimeVideoShareProps
>(forwardRef(MultiChannelRealTimeVideo));
