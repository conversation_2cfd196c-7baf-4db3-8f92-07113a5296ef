/*
 * @LastEditTime: 2025-07-23 13:23:06
 */
// @ts-nocheck
import { registerTemplate, HighRiskOperationConfigManager, request } from '@base-app/runtime-lib';
import { NotificationTemplate, MessageItem } from '../MessageTemplate';

const messageTypes = {
    alarm: 1,
}
registerTemplate(messageTypes.alarm, 1, () => {
    return{
        // NotificationTemplate,
        MessageItem
    }
});
/**行业层注册高风险操作**/ 
HighRiskOperationConfigManager.registerHighRiskConfig(1, () => {
    return [];
});

function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : { default: obj };
}

export const getRoutes = () => {
    const context = require.context('./routes', false, /\.ts$/);
    const routes = context
        .keys()
        .reduce((res, id) => [...res, ..._interopRequireDefault(context(id)).default], []);
    return routes;
};

export const cusTomMiddlegroundRoutes = (routes, appId) => {
    console.log('hook cusTomMiddlegroundRoutes===' ,appId, routes);
    if (appId == 1) {
        routes.forEach(item => {
            item.testlayout = '';
        });
        console.log('hook cusTomMiddlegroundRoutes===222' , routes);
    }
};

export * from './auto-export';


// @ts-ignore
// import alarm from './routes/alarm';
// import evidence from './routes/evidence';
// import fence from './routes/fence';
// import generalSettings from './routes/general-settings';
// import monitoringCenter from './routes/monitoring-center';
// import permission from './routes/permission';
// import strategy from './routes/strategy';
// import trackPlayback from './routes/track-playback';
// import tenant from './routes/tenant';
// import user from './routes/user';
// import videoLibrary from './routes/video-library';

// export const getRoutes = () => [
//     ...alarm,
//     ...evidence,
//     ...fence,
//     ...generalSettings,
//     ...permission,
//     ...strategy,
//     ...trackPlayback,
//     ...user,
//     ...videoLibrary,
// ];
