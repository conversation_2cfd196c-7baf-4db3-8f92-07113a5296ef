import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
import React, { useRef, useState, useEffect } from 'react';
import classNames from 'classnames';
import { Dispatch, useDispatch, getDvaApp, useSelector } from '@base-app/runtime-lib/core';
import {
    Auth,
    getAppGlobalData,
    i18n,
    useConstructor,
    useUrlSearchStore,
    utils,
} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import { Button, Select, Input, Space, message, Form, Tooltip } from '@streamax/poppy';
import { StarryTable, StarryModal, Action } from '@base-app/runtime-lib';
import { IconDelete, IconRequest, IconKey } from '@streamax/poppy-icons';
import RoleManageModel from '../../model';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { getCustomItems, getCustomJsx, getTableIconBtns, runCustomFun } from '@/utils/pageReuse';
import { Instances, ListPageQueryForm, ListPageTableBase } from '@/types/pageReuse/pageReuseBase';
import { authorizeRoleToUser, getToAuthorizeUserList } from '@/service/authority';
import CustomModal from './customModal';
import { useAsyncEffect } from '@streamax/hooks';
import { getUserStateConfig } from '@/utils/commonFun';
import {USER_DORMANCY, USER_EXPIRE} from '@/utils/constant';

interface User {
    userId: number;
    account: string;
    state: number;
}

interface AuthorizedUserProps {
    roleId: number;
    roleName: string | undefined;
}
export type AuthorizedUserShareProps = ListPageTableBase & Instances & ListPageQueryForm;

const AuthorizedUser: React.FC<AuthorizedUserProps & AuthorizedUserShareProps> = (props) => {
    /**定制***/
    const { getTableLeftRender, getInstances, getColumns, getIconBtns, getColumnSetting, getQueryForm, injectSearchList } = props;
    /**定制***/
    const { roleId, roleName } = props;
    // @ts-ignore
    const {
        query: { tab },
    } = useLocation();
    const searchStore = useUrlSearchStore();
    const [form] = Form.useForm();

    const dispatch: Dispatch = useDispatch();
    const tableRef = useRef<any>();
    const searchQuery = useRef({});

    const [initState, setInitState] = useState<any>(undefined);
    const [modalVisible, setModalVisible] = useState<any>(false);
    const [disabledKeys, setDisabledKeys] = useState<string[]>([]);
    const [dataSource, setDataSource] = useState([]);
    const [userState, setUserState] = useState([
        { label: i18n.t('state', '启用'), value: 1 },
        { label: i18n.t('state', '停用'), value: 2 }
    ])

    const history: any = useHistory();
    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });
    const { page, pageSize, state, account } = searchStore.get();

    useAsyncEffect(async () => {
        const {isSleep,isExpired} = await getUserStateConfig();
        const newState = [
            ...userState
        ];
        if(isSleep){
            newState.push({
                label: i18n.t('state', '休眠'),
                value: USER_DORMANCY,
            });
        }
        if (isExpired){
            newState.push({
                label: i18n.t('state', '到期'),
                value: USER_EXPIRE,
            });
        }
        setUserState(newState);
    },[]);

    useEffect(() => {
        if (tab === 'authorizedUser') {
            setInitState(1);
            form.setFieldsValue({
                state: [undefined, null, ''].includes(state) ? null : Number(state),
                account,
            });
        } else {
            setInitState(undefined);
        }
    }, []);

    useEffect(() => {
        if (tab === 'authorizedUser') {
            searchStore.set(searchQuery.current);
        }
    }, [tab]);

    const queryFormItems = [
        {
            label: i18n.t('name', '状态'),
            name: 'state',
            field: Select,
            itemProps: {
                initialValue: initState,
            },
            fieldProps: {
                options: userState,
                allowClear: true,
                placeholder: i18n.t('message', '请选择用户状态'),
            },
        },
        {
            label: i18n.t('name', '用户'),
            name: 'account',
            field: Input,
            itemProps: {
                rules: [
                    {
                        max: 50,
                        message: i18n.t('message', '超过最长长度{num}个字符', { num: 50 }),
                    },
                ],
            },
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入用户名'),
                maxLength: 50,
            },
        },
    ];

    const handleDelete = (record: User) => {
        const { userId, account } = record;
        const modal = StarryModal.confirm({
            centered: true,
            size:"small",
            title: i18n.t('message', '删除确认'),
            content: i18n.t('message', '确认要删除“{name}”授权吗？', { name: account }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                dispatch({
                    type: 'roleManage/deleteAuthorizedUser',
                    payload: {
                        roleId,
                        userIds: [userId],
                    },
                }).then(() => {
                    message.success(i18n.t('message', '删除成功'));
                    modal.destroy();
                    // @ts-ignore
                    tableRef.current.reload();
                });
            },
        });
    };

    const columns = [
        {
            title: i18n.t('name', '用户名称'),
            dataIndex: 'account',
            ellipsis: true,
            render: (text: string, record: User) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            <Action
                                code="@base:@page:role.manage:detail@action:tab.auth.user:detail"
                                url={'/user-manage/user-detail'}
                                params={{
                                    userId: `${record.userId}`,
                                }}
                                fellback={text}
                            >
                                {text}
                            </Action>
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '用户状态'),
            dataIndex: 'state',
            render: (text: number) => {
                const targetState = userState.find((item) => item.value === text);
                return (
                    <Space size={8}>
                        <div
                            className={classNames(
                                'state-sign',
                                `state-sign-${text === 1 ? 'green' : 'grey'}`,
                            )}
                        />
                        <span className="state-text">
                            {targetState?.label ?? '-'}
                        </span>
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            render: (text: any, record: User) => {
                return (
                    <Auth code="@base:@page:role.manage:detail@action:tab.auth.user:delete">
                        <Space size={24}>
                            <Tooltip title={i18n.t('action', '删除')}>
                                <IconDelete
                                    onClick={() => handleDelete(record)}
                                    className="operate-btn"
                                />
                            </Tooltip>
                        </Space>
                    </Auth>
                );
            },
        },
    ];

    const fetchFunc = (params: any) => {
        const query = {
            ...searchStore.get(),
            ...params,
        };
        searchStore.set(query);
        searchQuery.current = query;
        if (injectSearchList) {
            return injectSearchList(params);
        }
        return dispatch({
            type: 'roleManage/fetchRoleAuthorizedUsers',
            payload: {
                ...params,
                roleId,
                account: (params?.account || '').replace(/(^\s*)|(\s*$)/g, ''),
            },
        }).then((rs: any) => {
            setDisabledKeys(rs.list.map((item) => item.userId));
            return {
                list: rs.list,
                total: rs.total,
            };
        });
    };
    const onUserAuthorizeModalOpen = async () => {
        const dataList = await getToAuthorizeUserList({
            sort: 'desc',
            limit: *********,
            state: 1,
            appId: getAppGlobalData('APP_ID'),
        });
        (dataList || []).forEach((item: any) => {
            item.roleNames = item.roleName;
            item.key = item.userId;
        });
        setDataSource(dataList);
        setModalVisible(true);
    };
    const userAuthorize = (
        <Auth key="userAuthorize" code="@base:@page:role.manage:detail@action:tab.auth.user:add">
            <Button type="primary" onClick={onUserAuthorizeModalOpen}>
                <Space size={10}>
                    <IconKey />
                    {i18n.t('action', '用户授权')}
                </Space>
            </Button>
        </Auth>
    );
    const toolbar = {
        leftRender: () => <>{getCustomJsx(getTableLeftRender, [userAuthorize])}</>,
        iconBtns: getTableIconBtns(getIconBtns, []),
        ...getColumnSetting?.(),
    };

    const handleReset = () => {
        if (initState) {
            setInitState(undefined);
        }
        setTimeout(() => {
            // @ts-ignore
            tableRef.current.reset();
        }, 100);
        return false;
    };

    const handleSelectedUser = (selectedList: any[]) => {
        const userIds = selectedList.map((item) => item.userId);
        const logParams = selectedList.map((item) => ({
            id: item.userId,
            data: roleName,
        }));
        const params = {
            userId: userIds,
            roleId: roleId,
            // 从用户详情授权角色，增加记录日志字段
            logParams,
            operationModelCode: 'userRolePermissions',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'create',
        };
        authorizeRoleToUser(params).then(() => {
            setDisabledKeys(userIds);
            setModalVisible(false);
            tableRef.current?.reload();
        });
    };
    runCustomFun(getInstances, {
        table: tableRef.current,
    });

    return (
        <div className="authorized-user-page-container">
            <StarryTable
                fetchDataAfterMount
                aroundBordered
                fetchDataFunc={fetchFunc as any}
                pagination={{
                    defaultCurrent: Number(page) || 1,
                    defaultPageSize: Number(pageSize) || 20,
                }}
                ref={tableRef as any}
                queryProps={{
                    form,
                    items: getCustomItems(getQueryForm, queryFormItems, undefined) ,
                    onReset: handleReset,
                }}
                columns={getCustomItems(getColumns, columns)}
                toolbar={toolbar}
                rowKey="userId"
            />
            <CustomModal
                dataSource={dataSource}
                visible={modalVisible}
                onOk={handleSelectedUser}
                disabledKeys={disabledKeys}
                onCancel={() => {
                    setModalVisible(false);
                }}
            />
        </div>
    );
};

export default withSharePropsHOC<AuthorizedUserProps, AuthorizedUserShareProps>(AuthorizedUser);
