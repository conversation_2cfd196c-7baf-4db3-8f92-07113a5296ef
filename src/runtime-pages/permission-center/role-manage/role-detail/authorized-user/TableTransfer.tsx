/*
 * @LastEditTime: 2024-03-22 10:05:46
 */
import { i18n } from '@base-app/runtime-lib';
import { Table } from '@streamax/poppy';
import Transfer from '@/components/custom-transfer';
import difference from 'lodash/difference';
import type { ColumnsType } from '@streamax/poppy/lib/table';

interface TableTransferProps {
    leftColumns?: ColumnsType<{ key: string; disabled: boolean }>;
    rowKey?: string | (() => string);
    [key: string]: any;
}

function TableTransfer(props: TableTransferProps) {
    const { leftColumns, rowKey, disabledKeys, ...otherProps } = props;
    return (
        <Transfer
            {...otherProps}
            isTableTransfer
            disabledKeys={disabledKeys || []}
            searchPlaceHolder={i18n.t('message', '请输入用户名称')}
            contentKeys={['account']}
            titleKeys={['account', 'roleName']}
        >
            {({
                direction,
                filteredItems,
                selectedKeys,
                disabled: listDisabled,
                onSelect: onItemSelect,
                onSelectAll: onItemSelectAll,
            }: any) => {
                const allSelectedKeys = [...new Set([...selectedKeys, ...(disabledKeys || [])])];
                if (direction === 'SOURCE') {
                    const rowSelection = {
                        getCheckboxProps: (item: any) => ({
                            disabled: disabledKeys?.includes(item.userId),
                        }),
                        onSelectAll(selected: boolean, selectedRows: any) {
                            const treeSelectedKeys = selectedRows
                                .filter((item: any) => item && !item.disabled)
                                .map((p: any) => p.key);
                            const diffKeys = selected
                                ? difference(treeSelectedKeys, selectedKeys)
                                : difference(selectedKeys, treeSelectedKeys);
                            onItemSelectAll(diffKeys, selected);
                        },
                        onChange(key: string[]) {
                            onItemSelect(key);
                        },
                        selectedRowKeys: allSelectedKeys,
                    };

                    return (
                        <Table
                            // @ts-ignore
                            rowSelection={rowSelection}
                            columns={leftColumns}
                            dataSource={filteredItems}
                            size="small"
                            bordered={false}
                            rowKey={rowKey}
                            style={{
                                pointerEvents: listDisabled ? 'none' : undefined,
                            }}
                            onRow={(item: any) => {
                                const key = item.key;
                                return {
                                    onClick: () => {
                                        if (disabledKeys?.includes(item.userId)) return;
                                        const diffKeys = !selectedKeys.includes(key)
                                            ? [...selectedKeys, key]
                                            : difference(selectedKeys, [key]);
                                        onItemSelect(diffKeys);
                                    },
                                };
                            }}
                            pagination={{
                                pageSize: 10,
                                showQuickJumper: false,
                                showSizeChanger: false,
                            }}
                        />
                    );
                } else {
                    return null;
                }
            }}
        </Transfer>
    );
}

export default TableTransfer;
