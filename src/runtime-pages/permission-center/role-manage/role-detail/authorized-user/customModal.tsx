import { StarryAbroadOverflowEllipsisContainer } from "@base-app/runtime-lib";
/*
 * @LastEditTime: 2024-03-22 10:26:00
 */
import React, { useState } from 'react';
import { Tooltip } from '@streamax/poppy';
import { i18n, utils } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { CustomTransferModal } from '@/components/custom-transfer';
import TableTransfer from './TableTransfer';
import { xor } from 'lodash';
import { ChooseUserItem } from '@/runtime-pages/permission-center/model';
const zeroTimeStampToFormatTime = utils.formator.zeroTimeStampToFormatTime;
interface UserItem {
    userName?: string;
    userId?: number;
    index?: number;
}
type CustomMoadalProps = {
    visible: boolean;
    onOk: (ids: string[]) => void;
    onCancel: () => void;
    disabledKeys: string[];
    dataSource: any[];
}
const CustomMoadal: React.FC<CustomMoadalProps> = (props) => {
    const { onOk, visible, onCancel, disabledKeys, dataSource } = props;
    const [targetKeys, setTargetKeys] = useState<any>([]);
    const handleSelecedUser = () => {
        const differenceKey = xor(targetKeys, disabledKeys);
        const newList: UserItem[] = [];
        const selectedKeys = [...new Set([...differenceKey, ...disabledKeys])];
        selectedKeys.forEach((key: string) => {
            const item = dataSource?.find((p: any) => p.userId === key);
            if (item) {
                newList.push({
                    userName: item.account,
                    userId: item.userId,
                });
            }
        });
        onOk?.(newList);
        setTargetKeys([]);
        // const userIds = newList.map(item => item.userId);
        // const logParams = newList.map((item) => ({
        //     id: item.userId,
        //     data: roleName,
        // }));
        // const params = {
        //     userId: userIds,
        //     roleId: roleId,
        //     // 从用户详情授权角色，增加记录日志字段
        //     logParams,
        //     operationModelCode: 'userRolePermissions',
        //     operationTypeCode: 'edit',
        //     operationDetailTypeCode: 'create',
        // };
        // authorizeRoleToUser(params).then(res => {
        //     setDisabledKeys(userIds);
        //     setModalVisible(false);
        //     tableRef.current?.reload()
        // });
        // @ts-ignore
        // console.log(newList)
    };
    const handleTargetKeysChange = (nextTargetKeys: string[]) => {
        // @ts-ignore
        setTargetKeys(nextTargetKeys);
    };
    const tableTransferColumns = [
        {
            title: i18n.t('name', '用户名'),
            dataIndex: 'account',
            ellipsis: { showTitle: false },
            width: '20%',
            render: (text: any) => {
                return (
                    <Tooltip title={text || '-'}>
                        <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '绑定角色数'),
            dataIndex: 'roleNum',
            width: '24%',
            ellipsis: { showTitle: false },
            sorter: (a: any, b: any) => a.roleNum - b.roleNum,
            render: (text: any) => {
                return (
                    <Tooltip title={text || '-'}>
                        <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '绑定角色'),
            dataIndex: 'roleName',
            ellipsis: { showTitle: false },
            width: '21%',
            render: (text: any, record: any) => {
                const texts = (text || '').split(',');
                const renderText = record.roleId
                    ? record.roleId
                          .split(',')
                          .map((roleId: string, index: number) => {
                              return i18n.t(`@i18n:@role__${roleId}`, texts[index] || '');
                          })
                          .join(',')
                    : text || '-';
                return (
                    <Tooltip title={renderText || '-'}>
                        <StarryAbroadOverflowEllipsisContainer>{renderText}</StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '创建时间'),
            width: '35%',
            ellipsis: { showTitle: false },
            dataIndex: 'createTime',
            sorter: (a: any, b: any) => a.createTime - b.createTime,
            render: (text: number) => {
                return (
                    <Tooltip title={text ? zeroTimeStampToFormatTime(text) : '-'}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {text ? zeroTimeStampToFormatTime(text) : '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
    ];
    return (
        <CustomTransferModal
            destroyOnClose
            centered
            maskClosable={false}
            visible={visible}
            bodyStyle={{
                height: '700px',
            }}
            title={i18n.t('message', '添加授权用户')}
            onOk={handleSelecedUser}
            onCancel={() => {
                setTargetKeys([]);
                onCancel?.();
            }}
            wrapClassName="add-authorize-user-modal"
            focusTriggerAfterClose={false}
        >
            <TableTransfer
                //@ts-ignore
                showClearAll
                showSearch
                dataSource={dataSource}
                selectedKeys={targetKeys}
                disabledKeys={disabledKeys}
                onSelectChange={handleTargetKeysChange}
                filterOption={(inputValue: string, item: ChooseUserItem) => {
                    const searchValue = (inputValue || '').replace(/(^\s*)|(\s*$)/g, '');
                    return item.account.toLowerCase().indexOf(searchValue.toLowerCase()) !== -1;
                }}
                leftColumns={tableTransferColumns}
                rowKey="userId"
                contentKeys={['account']}
                titleKeys={['account', 'roleNames']}
            />
        </CustomTransferModal>
    );
};

export default CustomMoadal;
