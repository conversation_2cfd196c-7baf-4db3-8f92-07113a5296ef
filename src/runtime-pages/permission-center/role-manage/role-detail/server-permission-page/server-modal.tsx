import { useState, useEffect, useRef } from 'react';
import { Table, Input, Modal, message } from '@streamax/poppy';
import {
    ListDataContainer,
} from '@streamax/starry-components';
import { getAppGlobalData, i18n } from '@base-app/runtime-lib';
import { authServer,getServerList } from '../../../../../service/server';

import './index.less';

export default function ServerModal(props: any) {
    const { serverModalVisiable, setServerModalVisiable, reloadTab,roleId,appId } = props;
    const listDataContainerRef = useRef(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

    const handleOk = () => {
        /***
         *
         * 给角色授权服务
         *
         * ***/

         authServer({
            roleId,
            appId,
            resourcegroupIds:selectedRowKeys.join(',')
         }).then(res=>{
             message.success(i18n.t('name','操作成功'))
            reloadTab({page:1})
            setServerModalVisiable(false);
         })
        
    };


    const columns = [
        {
            title: i18n.t('name', '服务名称'),
            dataIndex: 'resourcegroupName',
            key: 'resourcegroupName',
            ellipsis: true,
            width:250,
            render(text:any,record:any){
                return i18n.t(`@i18n:@resourcegroup__${record.resourcegroupCode}`,record.resourcegroupName)
            }
        },
        {
            title: i18n.t('name', '描述'),
            key: 'resourcegroupDescription',
            ellipsis: true,
            dataIndex: 'resourcegroupDescription',
        },
    ];

    const rowSelection = {
        selectedRowKeys,
        onChange: (keys: any[], selectedRows: any[]) => {
            setSelectedRowKeys(keys);
        },
        getCheckboxProps: (record: any) => ({
            disabled: record.authority, // Column configuration not to be checked
            name: record.resourcegroupName,
          }),
        onSelectAll: (
            selected: boolean,
            selectedRows: any[],
            changeRows: any[],
        ) => {

        },
    };

    const fetchList = (params: any) => {
        const { page, pageSize, serverName } = params;
        return getServerList({
            page,
            pageSize,
            appId,
            keyword: serverName,
            roleId
        }).then((res) => {
            res.list = res.list.map((item) => {
                return {
                ...item,
                key: item.id,
                title: item.applicationName,
                }
            });
            return res;
        });
    };

    useEffect(() => {
        listDataContainerRef.current?.loadDataSource()
    }, [serverModalVisiable])

    return (
        <>
            <Modal
                visible={serverModalVisiable}
                onOk={handleOk}
                title={i18n.t('name','授权服务')}
                width="500"
                onCancel={() => setServerModalVisiable(false)}
                destroyOnClose
            >
                <div className="detail-role-server-modal">
                    <ListDataContainer
                        ref={listDataContainerRef}
                        getDataSource={(params) => {
                            return fetchList(params);
                        }}
                        toolbar={{ hasReload: false }}
                        pagination={{
                            affix:false,
                            pageSize:8
                        }}
                        listRender={(data) => {
                            return (
                                <Table
                                    size="middle"
                                    rowSelection={rowSelection}
                                    columns={columns}
                                    dataSource={data}
                                    pagination={false}
                                />
                            );
                        }}
                        queryForm={{
                            items: [
                                {
                                    label: i18n.t('name', '服务名称'),
                                    name: 'serverName',
                                    field: Input,
                                    fieldProps: {
                                        allowClear: true,
                                        maxLength: 50,
                                        placeholder: i18n.t(
                                            'message',
                                            '请输入服务名称',
                                        ),
                                    },
                                },
                            ],
                        }}
                    />
                </div>
            </Modal>
        </>
    );
}
