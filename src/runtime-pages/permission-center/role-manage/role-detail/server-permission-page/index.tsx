import { toTreeByRecursion } from '@/utils/commonFun';
import { Auth, i18n } from '@base-app/runtime-lib';
import { useLockFn } from '@streamax/hooks';
import { message, Space } from '@streamax/poppy';
import { ListDataContainer } from '@streamax/starry-components';
import React, { useEffect, useRef, useState } from 'react';
import {
    gethasAuthServerList,
    RoleAuthServer,
} from '../../../../../service/server';
import ServerTreeTable from '../../../server-manage/ServerTreeTable';
import Search from '../../components/Search';
import { findAllParentKeys, getAllKeys, getFormatList } from '../../util';
import './index.less';
interface AppServerProps {
    appId: string;
    roleId: any;
    activeTab: string;
    openIsWhen: (flag: boolean) => void;
}
enum ServiceType {
    PRESET_SERVER = 1, //预设服务
    CUSTOM_SERVER = 2, //自定义服务
}
type DataRow = {
    resourcegroupType: ServiceType;
    parentId: string;
    sortValue: number;
    children: [];
};
enum TreeNodeType {
    root = 1, //根节点只有一个
    child = 2, //不是根节点多个
}
const ServerPermissionPage: React.FC<AppServerProps> = (props) => {
    const { appId, roleId, activeTab, openIsWhen } = props;
    const listDataContainerRef = useRef<any>(null);
    const [resourceList, setResourceList] = useState([]);
    const [checkedKeys, setCheckedKeys] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState<any>([]);
    const [editDisabled, seteditDisabled] = useState(true);
    const [formatList, setFormatList] = useState<
        { value: string; label: string; path: string; key: string }[]
    >([]);
    const [filteredOptions, setFilteredOptions] = useState<any[]>([]);
    const [treeData, setTreeData] = useState<any[]>([]);
    const treeRef = useRef<any>();
    const [selectKey, setSelectKey] = useState(''); // 选中节点key
    const [highlightedKey, setHighlightedKey] = useState('');
    useEffect(() => {
        seteditDisabled(true);
        listDataContainerRef.current?.loadDataSource();
        openIsWhen && openIsWhen(false);
    }, [activeTab]);
    useEffect(() => {
        const allKeys = getAllKeys(treeData);
        setExpandedKeys(allKeys);
    }, [treeData]);
    const parentIds: any = [];
    const checkParent = (key: any) => {
        const page = resourceList.find((p: any) => p.resourceId === key);

        if (page) {
            // @ts-ignore
            const parentId = page.parentId;
            if (parentId) {
                parentIds.push(parentId);
                checkParent(parentId);
            }
        }
        return parentIds;
    };
    // @ts-ignore
    const handleCheck = ({ checked: checkedKey }, { node, checked }) => {
        let newCheckedKeys = [...checkedKey];
        const checkedAllId: number[] = [];
        const checkChildren = (Node: any) => {
            Node.forEach((element: any) => {
                element.resourceId && checkedAllId.push(element.resourceId);
                if (element.children && element.children.length) {
                    checkChildren(element.children);
                }
            });
        };
        const parentIds = checkParent(node.resourceId);
        checkChildren([node]);
        if (checked) {
            newCheckedKeys = newCheckedKeys
                .concat(checkedAllId)
                .concat(parentIds);
        } else {
            newCheckedKeys = checkedKey.filter(
                (p: any) => checkedAllId.indexOf(p) == -1,
            );
        }
        // @ts-ignore
        setExpandedKeys(expandedKeys.concat([node.resourceId]));
        // @ts-ignore
        setCheckedKeys(Array.from(new Set(newCheckedKeys)));
    };

    const columns = [
        {
            title: i18n.t('name', '服务名称'),
            dataIndex: 'resourcegroupName',
            colSize: 16,
            render: (text: any, record: any) => {
                return i18n.t(
                    `@i18n:@resourcegroup__${record.resourcegroupCode}`,
                    record.resourcegroupName,
                );
            },
        },
        {
            title: i18n.t('name', '描述'),
            dataIndex: 'resourcegroupDescription',
            colSize: 8,
            render: (text: any) => {
                return text || '-';
            },
        },
    ];

    const saveAuthorityServer = useLockFn(async () => {
        openIsWhen && openIsWhen(false);
        //保存数据
        await RoleAuthServer({
            roleId,
            resourcegroupIds: checkedKeys.join(','),
            appId,
        });
        message.success(i18n.t('message', '操作成功'));
        listDataContainerRef.current?.loadDataSource();
        seteditDisabled(true);
    });
    /**
     * @description:
     * @param {DataRow} data
     * @param {1} flag 是否为根节点1 根节点， 2不是根节点
     * @return {*}
     */
    const sortTreeData = (data: DataRow[], flag: TreeNodeType) => {
        let child = [];
        //是否为根节点
        if (flag === TreeNodeType.root) {
            child = data[0].children;
        } else {
            child = data;
        }
        child.sort(function (a, b) {
            return a.sortValue - b.sortValue;
        });
        child.forEach((item) => {
            if (item.children) {
                item.children = sortTreeData(item.children, 2);
            }
        });
        if (flag === TreeNodeType.root) {
            data[0].children = child;
            return data;
        } else {
            return child;
        }
    };

    const getRoleServer = (params: any) => {
        const { page } = params;
        return gethasAuthServerList({
            page,
            pageSize: 100000000,
            appId,
            roleId,
        }).then((res) => {
            setResourceList(res.list);
            setCheckedKeys(
                res.list
                    .map((item: any) => {
                        if (item.authority) {
                            return item.id;
                        }
                        return undefined;
                    })
                    .filter((id: any) => typeof id !== 'undefined'),
            );
            const treeOption = {
                enable: true, // 是否开启转tree插件数据
                keyField: 'key', // 标识字段名称
                valueField: 'value', // 值字段名称
                titleField: 'resourceId', // 标题字段名称
                // item.disableCheckbox = disabled;

                keyFieldBind: 'id', // 标识字段绑定字段名称
                valueFieldBind: 'id', // 值字段名称绑定字段名称
                titleFieldBind: 'id', // 标题字段名称绑定字段名称
            };
            const list = toTreeByRecursion(
                res.list,
                'id',
                'parentId',
                null,
                'children',
                treeOption,
            );
            // @ts-ignore
            let resourcegroupType1: DataRow[] = []; //预设服务
            let resourcegroupType2: DataRow[] = []; //自定义服务
            list.forEach((item: DataRow) => {
                if (item.resourcegroupType === ServiceType.PRESET_SERVER) {
                    resourcegroupType1.push(item);
                } else {
                    resourcegroupType2.push(item);
                }
            });
            resourcegroupType1 = sortTreeData(
                resourcegroupType1,
                TreeNodeType.child,
            );
            resourcegroupType2 = sortTreeData(
                resourcegroupType2,
                TreeNodeType.child,
            );
            setTreeData([...resourcegroupType1, ...resourcegroupType2]);
            setFormatList(getFormatList([...resourcegroupType1, ...resourcegroupType2]));
            return Promise.resolve({
                ...res,
                list: [...resourcegroupType1, ...resourcegroupType2],
            });
        });
    };

    const onExpand = (expandedKeysValue: React.Key[]) => {
        setExpandedKeys(expandedKeysValue);
    };

    const getEdit = () => {
        const resourceCode = '@base:@page:role.manage:detail@action:tab.auth.sever:edit';
        return(
            <Space>
                {editDisabled && (
                    <Auth code={resourceCode}>
                        <a
                            onClick={() => {
                                openIsWhen && openIsWhen(true);
                                seteditDisabled(false);
                            }}
                        >
                            {i18n.t('name', '编辑')}
                        </a>
                    </Auth>
                )}
                {!editDisabled && (
                    <Auth code={resourceCode}>
                        <a
                            onClick={() => {
                                listDataContainerRef.current?.loadDataSource();
                                openIsWhen && openIsWhen(false);
                                seteditDisabled(true);
                            }}
                        >
                            {i18n.t('name', '取消')}
                        </a>
                    </Auth>
                )}
                {!editDisabled && (
                    <Auth code={resourceCode}>
                        <a onClick={saveAuthorityServer}>
                            {i18n.t('name', '保存')}
                        </a>
                    </Auth>

                )}
            </Space>
        );
    };

    const handleSelect = (value, option) => {
        setSelectKey(option.value);
    };
    const handleReset = () => {
        setSelectKey('');
        setHighlightedKey('');
        setFilteredOptions([]);
    };
    const handleSearch = () => {
        if (selectKey) {
            const parents = findAllParentKeys(
                treeData,
                selectKey,
            );
            setExpandedKeys(Array.from(new Set(expandedKeys.concat(parents))));
            setTimeout(() => {
                treeRef.current?.scrollTo({
                    key: selectKey,
                    align: 'top',
                    offset: 200,
                });
                // 判断元素是否在可视区域
                const element = document.querySelector(`[data-key="${selectKey}"]`);
                if (element) {
                    const elementRect = element.getBoundingClientRect();
                    const isInViewport = (
                        elementRect.top >= 0 &&
                        elementRect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
                    );

                    if (!isInViewport) {
                        const offset = elementRect.top + window.scrollY - 200;
                        window.scrollTo({ top: offset, behavior: 'smooth' });
                    }
                }
            }, 100);
            setHighlightedKey(selectKey);
        }
    };
    const items: any = [
        {
            label: i18n.t('name', '服务名称'),
            name: 'taskName',
            field: Search,
            fieldProps: {
                formatList: formatList,
                handleSelect: handleSelect,
                filteredOptions: filteredOptions,
                setFilteredOptions: setFilteredOptions,
                handleReset:handleReset
            },
        },
    ];
    return (
        <div className="server-permission-wraper">
            <ListDataContainer
                pagination={undefined}
                ref={listDataContainerRef}
                getDataSource={getRoleServer}
                queryForm={{
                    items: items,
                    onSearch: handleSearch,
                    onReset: handleReset,
                }}
                toolbar={{
                    extraRight: getEdit(),
                }}
                listRender={(data) => {
                    return (
                        <ServerTreeTable
                            checkedKeys={checkedKeys}
                            columns={columns}
                            treeData={data}
                            loading={false}
                            disabled={editDisabled}
                            onCheck={handleCheck}
                            onExpand={onExpand}
                            selectable={true}
                            treeRef={treeRef}
                            searchKey={highlightedKey}
                            expandedKeys={expandedKeys}
                        />
                    );
                }}
            />
        </div>
    );
};
export default ServerPermissionPage;
