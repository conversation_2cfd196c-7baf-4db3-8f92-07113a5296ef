import { InfoPanel } from '@streamax/starry-components';
import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import type { Dispatch, Loading } from '@base-app/runtime-lib/core';
import { useDispatch, useSelector, getDvaApp } from '@base-app/runtime-lib/core';
import { useLocation, useHistory } from '@base-app/runtime-lib/core';
import { StarryBreadcrumb, StarryCard, StarryModal } from '@base-app/runtime-lib';
import {
    Auth,
    i18n,
    useConstructor,
    useUrlSearchStore,
    RouterPrompt,
} from '@base-app/runtime-lib';
import { IconRequestFill } from '@streamax/poppy-icons';
import { Spin, Tabs, Button, Space, message, Tag, Container } from '@streamax/poppy';
import RoleInfo from './components/RoleInfo';
import ServerPermissionPage from './server-permission-page';
import UserAuthorized from './authorized-user';
import type { RoleManageModelState } from '../model';
import RoleManageModel from '../model';
import './index.less';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { getCustomItems, getCustomJsx } from '@/utils/pageReuse';
import { DetailPageRightButtons, DetailPageTitle, PageTabs } from '@/types/pageReuse/pageReuseBase';
import { ROLE_APPROVAL_ADMIN_CODE } from '../const';

interface TabListItem {
    title: string;
    key: string;
    component: React.ReactElement;
}
export type RoleDetailShareProps = PageTabs & DetailPageTitle & DetailPageRightButtons;

const { TabPane } = Tabs;

const RoleDetail = (props: RoleDetailShareProps) => {
    /** 定制 */
    const { getPageTabsConfigItems, getDetailTitle, defaultCollapsed } = props;
    const {
        // @ts-ignore
        query: { roleId },
    } = useLocation();
    const dispatch: Dispatch = useDispatch();
    const searchStore = useUrlSearchStore();
    const history: any = useHistory();
    const [activeTab, setActiveTab] = useState(searchStore.get()?.tab);
    // 标记是否已点击过tab，未点击时选中第一个tab
    const [clickedTab, setClickedTab] = useState(false);
    const [when, setWhen] = useState(false);
    const openIsWhen = (flag: boolean) => {
        setWhen(flag);
    };
    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });

    const { roleDetail, loading } =
        useSelector((state: { roleManage: RoleManageModelState; loading: Loading }) => ({
            roleDetail: state.roleManage?.roleDetail || {},
            loading: !!state.loading?.effects['roleManage/fetchRoleDetail'],
        })) || {};

    const fetchDetailData = () => {
        dispatch({
            type: 'roleManage/fetchRoleDetail',
            payload: {
                roleId,
            },
        });
    };

    useEffect(() => {
        fetchDetailData();
    }, []);
    const handleDeleteRole = () => {
        const roleName = i18n.t(`@i18n:@role__${roleDetail?.roleId}`, roleDetail?.roleName);
        const modal = StarryModal.confirm({
            size:'small',
            centered: true,
            title: i18n.t('message', '删除确认'),
            content: i18n.t('message', '确认删除"{roleName}"角色吗?', { roleName }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                dispatch({
                    type: 'roleManage/deleteRole',
                    payload: [roleDetail?.roleId],
                }).then(() => {
                    message.success(i18n.t('message', '删除成功'));
                    modal.destroy();
                    history.goBack();
                });
            },
        });
    };

    const handleLockRole = (check: boolean, record: any) => {
        const modal = StarryModal.confirm({
            size:'small',
            centered: true,
            title: i18n.t('message', '{operateType}确认', {
                operateType: check ? i18n.t('state', '启用') : i18n.t('state', '停用'),
            }),
            content: i18n.t('message', '确定要{operateType}"{roleName}"角色吗?', {
                operateType: check ? i18n.t('action', '启用') : i18n.t('action', '停用'),
                roleName: i18n.t(`@i18n:@role__${record.roleId}`, record.roleName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                dispatch({
                    type: 'roleManage/lockRole',
                    payload: {
                        roleId: record.roleId,
                        state: check ? 1 : 2,
                    },
                }).then(() => {
                    modal.destroy();
                    message.success(i18n.t('message', '操作成功'));
                    fetchDetailData();
                });
            },
        });
    };
    const getRoleName = () => {
        return (
            <span className="role-name">
                {i18n.t(`@i18n:@role__${roleDetail?.roleId}`, roleDetail?.roleName)}
            </span>
        );
    };
    const getRoleCode = () => {
        return <span className="role-code">{roleDetail?.roleCode}</span>;
    };
    const getRoleStatus = () => {
        return (
            <Tag
                className={classNames('role-status', {
                    active: roleDetail?.state === 1,
                })}
            >
                {roleDetail?.state === 1 ? i18n.t('state', '启用') : i18n.t('state', '停用')}
            </Tag>
        );
    };
    const getCardTitle = () => {
        return (
            <span className='custom-card-title'>
                {getCustomJsx(
                    getDetailTitle,
                    [getRoleName(), getRoleCode(), getRoleStatus()],
                    roleDetail,
                )}
            </span>
        );
    };

    const getCardOperate = () => {
        const active = roleDetail?.state === 1;
        return (
            <>
                {roleDetail?.roleType === 3 && (
                    <Space size={12}>
                        <Auth code="@base:@page:role.manage:detail@action:enable.unable">
                            <Button onClick={() => handleLockRole(!active, roleDetail)}>
                                {roleDetail.state === 1
                                    ? i18n.t('action', '停用')
                                    : i18n.t('action', '启用')}
                            </Button>
                        </Auth>
                        {roleDetail?.state === 2 && roleDetail.roleCode !== ROLE_APPROVAL_ADMIN_CODE && (
                            <Auth code="@base:@page:role.manage:detail@action:delete">
                                <Button danger onClick={handleDeleteRole}>
                                    {i18n.t('action', '删除')}
                                </Button>
                            </Auth>
                        )}
                    </Space>
                )}
            </>
        );
    };

    const generateTabList = () => {
        const baseTabList: TabListItem[] = [
            Auth.check('@base:@page:role.manage:detail@action:tab.auth.user') && {
                title: i18n.t('name', '授权用户'),
                key: 'authorizedUser',
                component: (
                    <UserAuthorized
                        // @ts-ignore
                        activeTab={activeTab}
                        roleId={roleId}
                        roleType={roleDetail?.roleType}
                        roleName={roleDetail?.roleName}
                    />
                ),
            },
        ].filter((i) => i);
        const serverTab = [
            Auth.check('@base:@page:role.manage:detail@action:tab.auth.sever') && roleDetail.roleCode !== ROLE_APPROVAL_ADMIN_CODE && {
                title: i18n.t('name', '授权服务'),
                key: 'serverPermission',
                component: (
                    <ServerPermissionPage
                        activeTab={activeTab}
                        roleId={roleId}
                        // @ts-ignore
                        appId={roleDetail?.appId}
                        openIsWhen={openIsWhen}
                    />
                ),
            },
        ].filter((i) => i);
        // 如果是功能管理员，还有菜单权限和页面权限标签页
        if (roleDetail?.roleType === 3) {
            // 原本是3
            baseTabList.splice(1, 0, ...serverTab);
        }
        return baseTabList;
    };
    const tabs = getCustomItems(getPageTabsConfigItems, generateTabList());
    const keys = tabs.map((i) => i.key);
    // 有权限第一次进入没有activeTab时，就走默认第一个
    const tabKey = tabs.length && (!activeTab || !keys.includes(activeTab)) ? keys[0] : '';

    function tabsChange(val: any) {
        if (when) {
            setWhen(false);
            StarryModal.confirm({
                size:'small',
                centered: true,
                title: i18n.t('name', '提示'),
                content: i18n.t('message', '页面编辑信息未保存，确认离开？'),
                onOk() {
                    setWhen(false);
                    setActiveTab(val);
                    searchStore.set({
                        ...searchStore.get(),
                        tab: val,
                    });
                },
                onCancel() {
                    setWhen(true);
                    setActiveTab(activeTab);
                },
            });
        } else {
            setActiveTab(val);
            searchStore.set({
                ...searchStore.get(),
                tab: val,
            });
        }
        setClickedTab(true);
    }

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard className="role-detail-card-layout">
                <div className="role-detail-container">
                    <Spin spinning={loading}>
                        <InfoPanel
                            showCollapseBtn
                            defaultCollapsed={defaultCollapsed ?? true}
                            title={getCardTitle()}
                            extraRight={getCardOperate()}
                            className="role-base-info"
                            operationWrap={true}
                        >
                            <RoleInfo />
                        </InfoPanel>
                        {tabs?.length ? (
                            <Container>
                                <Tabs
                                    activeKey={tabKey || activeTab || 'authorizedUser'}
                                    onChange={tabsChange}
                                    className="role-detail-tabs"
                                >
                                    {tabs.map((item) => {
                                        return (
                                            <TabPane
                                                tab={item.title}
                                                key={item.key}
                                                tabKey={item.key}
                                            >
                                                {item.component}
                                            </TabPane>
                                        );
                                    })}
                                </Tabs>
                            </Container>
                        ) : null}
                    </Spin>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(RoleDetail);
