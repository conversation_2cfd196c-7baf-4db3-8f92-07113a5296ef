@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.role-info-container {
    .starry-info-block {
        // padding: 8px 0 8px;
    }
    .role-state {
        &::before {
            position: relative;
            top: -1px;
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 8px;
            background:  @starry-text-color-disabled;
            border-radius: 6px;
            content: '';
        }
        &.active {
            &::before {
                background: @success-color;
            }
        }
    }
    .role-info-detail-item {
        .poppy-form-item {
            margin: 0 0 4px;
        }
    }
    .poppy-descriptions-custom-item-label {
        white-space: nowrap;
    }
    .poppy-descriptions-custom-item-content {
        display: contents;
        padding-right: 10px;
        word-break: break-all;
    }
    .poppy-form-item-control-input-content {
        word-wrap: break-word;
    }
    .starry-info-block {
        border-bottom: none !important;
    }
    .poppy-descriptions-custom-item {
        margin-bottom: 18px;
    }
}
