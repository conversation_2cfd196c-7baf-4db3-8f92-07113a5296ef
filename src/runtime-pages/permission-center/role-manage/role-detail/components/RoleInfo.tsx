import { useSelector, getDvaApp } from '@base-app/runtime-lib/core';
import { useHistory } from '@base-app/runtime-lib/core';
import { Auth, i18n, utils, getAppGlobalData, useConstructor } from '@base-app/runtime-lib';
import classNames from 'classnames';
import { StarryInfoBlock, Action } from '@base-app/runtime-lib';
import RoleManageModel, { RoleManageModelState } from '../../model';
import './RoleInfo.less';
import { OverflowEllipsisContainer,Descriptions } from '@streamax/starry-components';
import { getCustomItems } from '@/utils/pageReuse';
import { DetailPageItems } from '@/types/pageReuse/pageReuseBase';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { ROLE_APPROVAL_ADMIN_CODE } from '../../const';

export type RoleDetailInfoShareProps = DetailPageItems;

const FUNCTION_ROLE_TYPE = 3;

const zeroTimeStampToFormatTime = utils.formator.zeroTimeStampToFormatTime;

const BasicInfo = (props: RoleDetailInfoShareProps) => {
    /**定制项 */
    const { getDescriptionItems } = props;
    /**end */

    const roleTypeMap = {
        1: i18n.t('name', '租户管理员'),
        2: i18n.t('name', '应用管理员'),
        3: i18n.t('name', '功能管理员'),
    };

    const history: any = useHistory();
    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });
    const { roleDetail } = useSelector((state: { roleManage: RoleManageModelState }) => ({
        roleDetail: state.roleManage?.roleDetail || {},
    })) || { roleDetail: {} };

    const handleEdit = () => {
        Action.openActionUrl({
            code: '@base:@page:role.manage:detail@action:edit',
            history,
            url: '/role-manage/detail',
            params: {
                roleId: `${roleDetail.roleId}`,
                operateType: 'edit',
            },
        });
    };
    const {
        roleId,
        roleName,
        roleCode,
        roleType,
        state,
        roleDesc,
        createUserName,
        createTime,
        appId,
        applicationName,
    } = roleDetail;
    const getState = () => {
        const active = state === 1;
        return (
            <span className={classNames('role-state', { active })}>
                {active ? i18n.t('state', '启用') : i18n.t('state', '停用')}
            </span>
        );
    };
    const items = [
        {
            label: i18n.t('name', '角色名称'),
            content: i18n.t(`@i18n:@role__${roleId}`, roleName),
            key: 'roleName',
        },
        {
            label: i18n.t('name', '角色编码'),
            content: roleCode,
            key: 'roleCode',
        },
        {
            label: i18n.t('name', '角色类型'),
            // @ts-ignore
            content: roleTypeMap[roleType],
            key: 'roleType',
        },
        {
            label: i18n.t('name', '归属应用'),
            content: i18n.t(`@i18n:@app__${appId}`, applicationName) || '-',
            key: 'appId',
        },
        {
            label: i18n.t('name', '角色状态'),
            content: getState(),
            key: 'state',
        },
        {
            label: i18n.t('name', '创建人'),
            content: createUserName || '-',
            key: 'createUserName',
        },
        {
            label: i18n.t('name', '创建时间'),
            content: createTime ? zeroTimeStampToFormatTime(createTime) : '',
            key: 'createTime',
        },
        {
            label: i18n.t('name', '角色描述'),
            content: roleDesc || '-',
            span: 16,
            key: 'roleDesc',
        },
    ];
    if (getAppGlobalData('APP_ID')) {
        items.splice(3, 1);
    }

    const basicInfoItems = getCustomItems(getDescriptionItems, items, roleDetail);

    return (
        <div className="role-info-container">
            <StarryInfoBlock
                title={i18n.t('name', '基本信息')}
                border="bottom"
                operation={
                    roleDetail.roleType === FUNCTION_ROLE_TYPE &&
                    roleDetail.roleCode !== ROLE_APPROVAL_ADMIN_CODE ? (
                        <Auth code="@base:@page:role.manage:detail@action:edit">
                            <a onClick={handleEdit}>
                                {i18n.t('action', '编辑')}
                            </a>
                        </Auth>
                    ) : null
                }
            >
                <Descriptions>
                    {basicInfoItems.map((item, index) => (
                        <Descriptions.Item
                            label={item.label}
                            key={item.label}
                            ellipsis={item.key !== 'roleDesc'}
                        >
                            {item.content}
                        </Descriptions.Item>
                    ))}
                </Descriptions>
            </StarryInfoBlock>
        </div>
    );
};

export default withSharePropsHOC<any, RoleDetailInfoShareProps>(BasicInfo);
