@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.role-detail-card-layout {
    .role-name {
        font-weight: 700;
        font-size: 20px;
    }
    .role-code {
        margin: 0 12px;
        font-weight: 400;
        font-size: 14px;
        opacity: 0.65;
    }
    .role-status {
        display: inline-block;
        height: 22px;
        padding: 0 8px;
        color: @starry-text-color-inverse !important;
        font-size: 12px;
        border: unset  !important;
        background: @starry-text-color-disabled !important;
        &.active {
            background: @success-color !important;
        }
    }
    .role-base-info {
        padding: 0;
        border-bottom: none;
        background-color: unset;
        .starry-info-block {
            border-bottom: none;
        }
        .starry-info-panel-header {
            padding-bottom: 24px;
        }
    }
    .fold-tree-table-container
        .fold-tree-table-content-wrapper
        .poppy-tree-treenode.poppy-tree-treenode-leaf-last {
        height: initial;
        padding-bottom: 8px !important;
    }
    .role-detail-tabs {
        margin-top: 4px;
        .poppy-tabs-tab {
            padding-top: 0;
        }
    }
    .custom-card-title{
        display: inline-flex;
        align-items: center;
    }
}

.role-detail-card-layout::abroad{
    .poppy-container:has(.role-base-info)::abroad{
        padding: 0 !important;
        box-shadow: unset;
        background-color: unset;
        .poppy-container::abroad{
            background-color: @starry-bg-color-container;
            border-radius: 16px;
            box-shadow: 0 1px 2px 0 @starry-card-box-shadow-color;
            margin-bottom: 24px;
            padding:24px;
            .starry-info-block::abroad{
                padding-bottom: 6px !important;
            }
        }
        .starry-info-panel-header::abroad{
            padding: 24px 0 0;
        }
        .starry-info-panel-body::abroad{
            margin-top: 24px;
        }
    }

}