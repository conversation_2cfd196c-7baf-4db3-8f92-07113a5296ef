import { useRef, useEffect, useState } from 'react';
import classNames from 'classnames';
import {
    Input,
    Select,
    Switch,
    Button,
    message,
    Space,
    Form,
    Tooltip,
    Badge,
} from '@streamax/poppy';
import type { ColumnsType } from '@streamax/poppy/lib/table';
import {
    IconCopyFill,
    IconDeleteFill,
    IconRequestFill,
    IconAddFill,
    IcListEditFill,
} from '@streamax/poppy-icons';
import {
    utils,
    getAppGlobalData,
    i18n,
    Auth,
    useConstructor,
    useUrlSearchStore,
    StarryAbroadIcon,
    StarryAbroadOverflowEllipsisContainer
} from '@base-app/runtime-lib';
import {
    StarryModal,
    StarryCard,
    StarryBreadcrumb,
    StarryTable,
    Action,
    // @ts-ignore
} from '@base-app/runtime-lib';
import type { Dispatch } from '@base-app/runtime-lib/core';
import { useDispatch, getDvaApp } from '@base-app/runtime-lib/core';
import { useHistory } from '@base-app/runtime-lib/core';
import RoleManageModel from './model';
import { getRoleListByPage } from '../../../service/role';
import { ListPageQueryForm, ListPageTableBase, PageBase } from '@/types/pageReuse/pageReuseBase';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { getCustomItems, getTableIconBtns } from '@/utils/pageReuse';
import { getCustomJsx } from '@/utils/pageReuse';
import './index.less';
import { ROLE_APPROVAL_ADMIN_CODE } from './const';

/**列表页table定制复用 */
export type RoleManageProps = ListPageTableBase & ListPageQueryForm;

const zeroTimeStampToFormatTime = utils.formator.zeroTimeStampToFormatTime;

interface ReqParams {
    page: number;
    pageSize: number;
    roleName?: string;
    roleCode?: string;
    state?: number;
    roleType?: number;
    appId?: string | number;
    userSearch?: string;
    orderBy?: string;
    sort?: string;
    userId: number;
}

const ROLT_TYPE_FEATURE = 3;

const RoleManage = (props: RoleManageProps) => {
    /** 定制项 */
    const {
        getColumnSetting,
        getColumns,
        getTableLeftRender,
        getIconBtns,
        injectSearchList,

        getQueryForm,
    } = props;

    const roleTypeMap = {
        1: i18n.t('name', '租户管理员'),
        2: i18n.t('name', '应用管理员'),
        3: i18n.t('name', '功能管理员'),
    };
    const [form] = Form.useForm();
    const [formItems, setFormItems] = useState([]);
    const [appList, setAppList] = useState([]);
    const tableRef = useRef<any>();
    const dispatch: Dispatch = useDispatch();
    const searchStore = useUrlSearchStore();

    const history: any = useHistory();

    const AppId = getAppGlobalData('APP_ID');
    const userInfo = getAppGlobalData('APP_USER_INFO') || {};
    const { pageSize, page, state, roleType, appId, ...formQuery } = searchStore.get();

    useEffect(() => {
        form.setFieldsValue({
            ...formQuery,
            state: state ? Number(state) : null,
            roleType: roleType ? Number(roleType) : null,
            appId: appId ? Number(appId) : null,
        });
        tableRef.current?.loadDataSource({
            ...formQuery,
            state: state ? Number(state) : null,
            roleType: roleType ? Number(roleType) : null,
            appId: appId ? Number(appId) : null,
        });
    }, []);

    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });

    const handleDelete = (record: any) => {
        const roleName = i18n.t(`@i18n:@role__${record.roleId}`, record.roleName);
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            size:'small',
            content: i18n.t('message', '确认要删除“{roleName}”角色吗？', { roleName }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: () => {
                dispatch({
                    type: 'roleManage/deleteRole',
                    payload: [record.roleId],
                }).then(() => {
                    message.success(i18n.t('message', '删除成功'));
                    modal.destroy();
                    // @ts-ignore
                    tableRef.current.reload();
                });
            },
        });
    };

    const handleLockRole = (check: boolean, record: any) => {
        const modal = StarryModal.confirm({
            centered: true,
            size:'small',
            title: i18n.t('message', '{operateType}确认', {
                operateType: check ? i18n.t('state', '启用') : i18n.t('state', '停用'),
            }),
            content: i18n.t('message', '确认要{operateType}“{roleName}”角色吗？', {
                operateType: check ? i18n.t('action', '启用') : i18n.t('action', '停用'),
                roleName: i18n.t(`@i18n:@role__${record.roleId}`, record.roleName),
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequestFill />,
            onOk: async () => {
                try {
                    await dispatch({
                        type: 'roleManage/lockRole',
                        payload: {
                            roleId: record.roleId,
                            state: check ? 1 : 2,
                        },
                    });
                    modal.destroy();
                    message.success(i18n.t('message', '操作成功'));
                    // @ts-ignore
                    tableRef.current.reload();
                    // eslint-disable-next-line no-empty
                } catch (error) {
                    // @ts-ignore
                    tableRef.current.reload();
                }
            },
        });
    };

    const generateColumns = () => {
        const columns: ColumnsType<any> = [
            {
                title: i18n.t('name', '角色名称'),
                dataIndex: 'roleName',
                ellipsis: { showTitle: false },
                fixed: 'left',
                width: 200,
                render: (text: string, record: any) => {
                    const newText = i18n.t(`@i18n:@role__${record.roleId}`, record.roleName);
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                        <StarryAbroadOverflowEllipsisContainer>
                            <Action
                                code="@base:@page:role.manage@action:detail"
                                url="/role-manage/detail"
                                fellback={newText}
                                params={{
                                    roleId: record.roleId,
                                }}
                            >
                                {newText}
                            </Action>
                        </StarryAbroadOverflowEllipsisContainer>
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                },
                calcWidth(text: string, record, cellValueWidths, measureText) {
                    const newText = i18n.t(`@i18n:@role__${record.roleId}`, record.roleName);
                    return measureText(newText).width;
                },
            },
            {
                title: i18n.t('name', '角色编码'),
                dataIndex: 'roleCode',
                ellipsis: true,
                width: 180,
                render: (text: string) => {
                    return (
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            },
            {
                title: i18n.t('name', '角色状态'),
                dataIndex: 'state',
                width: 180,
                render: (text: number, row: any) => {
                    const status = text === 1 ? 'success' : 'default';
                    const showText = text === 1 ? i18n.t('state', '启用') : i18n.t('state', '停用');
                    return (
                        <Space>
                            <Badge status={status} text={showText} />

                            {row.roleType === ROLT_TYPE_FEATURE && (
                                <span style={{ display: 'flex' }}>
                                    <Auth code="@base:@page:role.manage@action:enable.unable">
                                        <Switch
                                            size="small"
                                            checked={text === 1}
                                            onChange={(check) => handleLockRole(check, row)}
                                        />
                                    </Auth>
                                </span>
                            )}
                        </Space>
                    );
                },
            },
            {
                title: i18n.t('name', '归属应用'),
                dataIndex: 'applicationName',
                ellipsis: { showTitle: false },
                width: 180,
                render: (text: any, record: any) => {
                    let params = text;
                    if (text) {
                        params = i18n.t(`@i18n:@app__${record.appId}`, record.applicationName);
                    }
                    return (
                        <Tooltip title={params}>
                            <StarryAbroadOverflowEllipsisContainer>{params || '-'}</StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            },
            {
                title: i18n.t('name', '角色类型'),
                dataIndex: 'roleType',
                ellipsis: { showTitle: false },
                render: (text: number) => {
                    // @ts-ignore
                    return (
                        <Tooltip title={roleTypeMap[text]}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {roleTypeMap[text]}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
                width: 180,
            },
            {
                title: i18n.t('name', '创建人'),
                dataIndex: 'createUserName',
                ellipsis: true,
                render: (text: any) => {
                    return (
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
                width: 180,
            },
            {
                title: i18n.t('name', '创建时间'),
                dataIndex: 'createTime',
                ellipsis: true,
                sorter: true,
                render: (text: number) => {
                    return (
                        <Tooltip title={zeroTimeStampToFormatTime(text)}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {text ? zeroTimeStampToFormatTime(text) : '-'}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
                width: 200,
            },
            {
                title: i18n.t('name', '操作人'),
                dataIndex: 'updateUserName',
                ellipsis: true,
                width: 180,
                render: (text: any) => {
                    return (
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>{text || '-'}</StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
            },
            {
                title: i18n.t('name', '操作时间'),
                dataIndex: 'updateTime',
                ellipsis: true,
                sorter: true,
                render: (text: number) => {
                    return (
                        <Tooltip title={zeroTimeStampToFormatTime(text)}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {text ? zeroTimeStampToFormatTime(text) : '-'}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    );
                },
                width: 200,
            },
            {
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                fixed: 'right',
                width: 200,
                render: (text: any, row: any) => {
                    return (
                        <div className="table-operate-col">
                            {row.roleType === ROLT_TYPE_FEATURE && (
                                <Space
                                    key="operateSpace"
                                    size={
                                        utils.constant
                                            .BASE_TABLE_OPERATE_COLUMN_SIZE
                                    }
                                >
                                    <Action
                                        code="@base:@page:role.manage@action:copy"
                                        url="/role-manage/copy"
                                        fellback={''}
                                        params={{
                                            roleId: row.roleId,
                                            operateType: 'copy',
                                        }}
                                    >
                                        <Tooltip
                                            title={i18n.t('action', '复制')}
                                        >
                                            <IconCopyFill className="opertate-icon" />
                                        </Tooltip>
                                    </Action>
                                    {row.roleCode !==
                                        ROLE_APPROVAL_ADMIN_CODE && (
                                        <Action
                                            code="@base:@page:role.manage@action:edit"
                                            url="/role-manage/edit"
                                            fellback={''}
                                            params={{
                                                roleId: row.roleId,
                                                operateType: 'edit',
                                            }}
                                        >
                                            <Tooltip
                                                title={i18n.t('action', '编辑')}
                                            >
                                                <IcListEditFill className="opertate-icon" />
                                            </Tooltip>
                                        </Action>
                                    )}

                                    {row.state === 2 &&
                                        row.roleCode !==
                                            ROLE_APPROVAL_ADMIN_CODE && (
                                            <Auth code="@base:@page:role.manage@action:delete">
                                                <Tooltip
                                                    placement="top"
                                                    title={i18n.t(
                                                        'action',
                                                        '删除',
                                                    )}
                                                >
                                                    <IconDeleteFill
                                                        className="delete-role-btn operate-btn"
                                                        onClick={() =>
                                                            handleDelete(row)
                                                        }
                                                    />
                                                </Tooltip>
                                            </Auth>
                                        )}
                                </Space>
                            )}
                        </div>
                    );
                },
                calcWidth(text, record, cellValueWidths, measureText) {
                    // 按钮间隔距离，请确保和实际（render函数中配置）的间隔距离一致
                    const gap = 24;
                    // 当前展示的按钮数量
                    let btns = 0;
                    if (record.roleType === ROLT_TYPE_FEATURE) {
                        if (record.state === 2) {
                            btns = 3;
                        } else {
                            btns = 2;
                        }
                    }
                    return btns * 14 + (btns - 1) * gap;
                },
            },
        ];
        if (AppId) {
            columns.splice(3, 1);
        }
        return columns;
    };

    const columns = generateColumns();

    const getQueryFormItems = (apps: any[]) => {
        const queryFormItems = [
            {
                label: i18n.t('name', '角色状态'),
                name: 'state',
                field: Select,
                fieldProps: {
                    options: [
                        { label: i18n.t('state', '启用'), value: 1 },
                        { label: i18n.t('state', '停用'), value: 2 },
                    ],
                    placeholder: i18n.t('message', '请选择角色状态'),
                },
            },
            {
                label: i18n.t('name', '角色类型'),
                name: 'roleType',
                field: Select,
                fieldProps: {
                    options: [
                        {
                            label: i18n.t('name', '租户管理员'),
                            value: 1,
                        },
                        {
                            label: i18n.t('name', '应用管理员'),
                            value: 2,
                        },
                        {
                            label: i18n.t('name', '功能管理员'),
                            value: 3,
                        },
                    ],
                    placeholder: i18n.t('message', '请选择角色类型'),
                },
            },
            {
                label: i18n.t('name', '归属应用'),
                name: 'appId',
                field: Select,
                itemProps: {
                    initialValue: null,
                },
                fieldProps: {
                    options: apps,
                    placeholder: i18n.t('message', '请选择归属应用'),
                },
            },
            {
                label: i18n.t('name', '角色'),
                name: 'role',
                field: Input,
                itemProps: {
                    rules: [
                        {
                            max: 50,
                            message: i18n.t('message', '超过最长长度{num}个字符', { num: 50 }),
                        },
                    ],
                },
                fieldProps: {
                    allowClear: true,
                    placeholder: i18n.t('message', '请输入角色编码或角色名称'),
                    maxLength: 50,
                },
            },
        ];
        if (AppId) {
            // 非中台，所以不展示归属应用
            queryFormItems.splice(2, 1);
            queryFormItems.splice(1, 1);
        }

        return queryFormItems;
    };

    const handleAddRole = () => {
        history.push({
            pathname: '/role-manage/add',
            query: {
                operateType: 'add',
            },
        });
    };

    const batchAddButton = () => {
        return (
            <Action
                code="@base:@page:role.manage@action:add"
                url="/role-manage/add"
                fellback={''}
                params={{
                    operateType: 'add',
                }}
            >
                <Button type="primary" className="add-role-btn">
                    <Space>
                        <IconAddFill />
                        {i18n.t('action', '添加')}
                    </Space>
                </Button>
            </Action>
        );
    };
    const toolbar = {
        iconBtns: getTableIconBtns(getIconBtns, ['reload', 'column-setting']),
        leftRender: () => <>{getCustomJsx(getTableLeftRender, [batchAddButton()])}</>,
        columnSetting: {
            storageKey: '@base:@page:role.manage',
            disabledKeys: ['roleName', 'operate'],
        },
        ...getColumnSetting?.(),
    };

    const fetchPageList = (params: any) => {
        searchStore.set({
            ...searchStore.get(),
            ...params,
        });

        const reqParams: ReqParams = { ...params, isOwn: false };
        if (params.role) {
            const s = params.role.replace(/(^\s*)|(\s*$)/g, '');
            if (s) {
                reqParams.userSearch = s;
            }
            // @ts-ignore
            delete reqParams.role;
        }
        // 如果是非中台集成的这个页面，则以集成的平台的id去查
        if (AppId) {
            reqParams.appId = AppId;
        }
        reqParams.userId = userInfo.userId;
        if (injectSearchList) {
            return injectSearchList(reqParams);
        }
        return getRoleListByPage(reqParams).then((rs: any) => {
            return {
                list: rs.list,
                total: rs.total,
            };
        });
    };

    useEffect(() => {
        dispatch({
            type: 'roleManage/fetchAppList',
            payload: {
                page: 1,
                pageSize: 999999999, // 需要查询全部，接口只支持分页查询，所以pageSize需要很大
                tenantId: userInfo.tenantId,
                states: '1',
            },
        }).then((rs: any) => {
            const list = (rs.list || [])
                .map((item: any) => ({
                    label: i18n.t(`@i18n:@app__${item.applicationId}`, item.applicationName),
                    value: item.applicationId,
                }))
                .filter((item: any) => ![66666].includes(item.value));
            setAppList(list);
        });
    }, []);

    useEffect(() => {
        const items = getQueryFormItems(appList);
        setFormItems(items as any);
    }, [appList]);

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="role-manage-page-container">
                    <StarryTable
                        scroll={{x:'100%'}}
                        fetchDataAfterMount={false}
                        aroundBordered
                        fetchDataFunc={fetchPageList as any}
                        pagination={{
                            defaultCurrent: Number(page) || 1,
                            defaultPageSize: Number(pageSize) || 20,
                        }}
                        ref={tableRef as any}
                        queryProps={{
                            // items: formItems,
                            items: getCustomItems(getQueryForm, formItems, undefined),
                            form,
                            collapseCacheKey: '@base:@page:role.manage',
                        }}
                        rowKey="roleId"
                        toolbar={toolbar}
                        columns={getCustomItems(getColumns, columns)}
                    />
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(RoleManage);
