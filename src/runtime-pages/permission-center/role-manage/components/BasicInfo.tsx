import { Form, Input, Select } from '@streamax/poppy';
import type { FormInstance } from '@streamax/poppy/lib/form';
import { getAppGlobalData, i18n, useConstructor, utils, StarryAbroadFormItem } from '@base-app/runtime-lib';
import type { Dispatch} from '@base-app/runtime-lib/core';
import { getDvaApp, useDispatch, useSelector } from '@base-app/runtime-lib/core';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import InternationalInput from '../../../../components/InternationalInput';
import { getRoleListByPage } from '../../../../service/role';
import type { RoleManageModelState } from '../model';
import RoleManageModel from '../model';

import './BasicInfo.less';
import { getCustomItems } from '@/utils/pageReuse';
import type { ListPageQueryForm } from '@/types/pageReuse/pageReuseBase';
import { FormEdit } from '@/types/pageReuse/pageReuseBase';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { RspBasicLayout, RspFormLayout } from '@streamax/responsive-layout';

const validatorIllegalCharacter = utils.validator.illegalCharacter;

type Mode = 'modify' | 'create';

interface BasicInfoProps extends ListPageQueryForm {
    mode: Mode;
    roleType?: string;
    OperateType?: string;
}

interface RefBasicInfoProps {
    formInstance: FormInstance<any>;
    internationalValues: any;
}

const { TextArea } = Input;
const { ItemGroup, Item } = Form;
const emptyObj = {};

const BasicInfo: React.ForwardRefRenderFunction<RefBasicInfoProps, BasicInfoProps> = (
    props,
    ref,
) => {
    /**页面复用 */
    const { getQueryForm } = props;

    const roleTypeMap = {
        1: i18n.t('name', '租户管理员'),
        2: i18n.t('name', '应用管理员'),
        3: i18n.t('name', '功能管理员'),
    };
    const { mode, roleType, OperateType } = props;
    const { userId } = getAppGlobalData('APP_USER_INFO');
    const [appId, setappId] = useState<any>(undefined);
    const [singleColumn, setSingleColumn] = useState<boolean>(false);
    const [appList, setAppList] = useState();
    const [roleList, setRoleList] = useState<any[]>([]);
    const [internationalValues, setInternationalValues] = useState<any>();
    const [cursorPosition, setCursorPosition] = useState(0); // 光标位置
    const [form] = Form.useForm();
    const internationalRef = useRef<any>();
    const dispatch: Dispatch = useDispatch();

    const userInfo = getAppGlobalData('APP_USER_INFO') || {};

    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });

    useImperativeHandle(ref, () => ({
        formInstance: form,
        internationalValues,
        handleSetInternationalValues,
    }));

    const { basicInfoForm } =
        useSelector((state: { roleManage: RoleManageModelState }) => ({
            basicInfoForm: state.roleManage?.basicInfoForm,
        })) || emptyObj;
    useEffect(() => {
        dispatch({
            type: 'roleManage/fetchAppList',
            payload: {
                page: 1,
                pageSize: 999999999, // 需要查询全部，接口只支持分页查询，所以pageSize需要很大
                tenantId: userInfo.tenantId,
                states: '1',
            },
        }).then((rs: any) => {
            setAppList(
                (rs.list || [])
                    .map((item: any) => ({
                        label: i18n.t(`@i18n:@app__${item.applicationId}`, item.applicationName),
                        value: item.applicationId,
                    }))
                    .filter((item: any) => ![66666].includes(item.value)),
            );
        });
    }, []);

    useEffect(() => {
        if (basicInfoForm && basicInfoForm.roleName) {
            setappId(basicInfoForm.appId);
            form.setFieldsValue({
                ...basicInfoForm,
                roleCode: OperateType === 'copy' ? '' : `${basicInfoForm.roleCode}`,
                roleType: `${basicInfoForm.roleType}`,
                // @ts-ignore
                roleName:
                    OperateType === 'copy'
                        ? ''
                        : i18n.t(`@i18n:@role__${basicInfoForm.roleId}`, basicInfoForm.roleName), // 多语言数据回填
            });
        }
    }, [basicInfoForm]);

    // 在请求回归属应用列表后重新设置一次归属应用的值，防止下拉框显示value而不显示label
    useEffect(() => {
        if (basicInfoForm && basicInfoForm.appId) {
            form.setFieldsValue({ appId: basicInfoForm.appId });
        }
    }, [appList, basicInfoForm]);

    const fetchRoleList = (reqParams: any) => {
        return getRoleListByPage(reqParams).then((rs: any) => {
            setRoleList(rs.list);
        });
    };

    useEffect(() => {
        fetchRoleList({
            pageSize: 999999999,
            page: 1,
            isOwn: false,
            userId,
        });
    }, []);

    const handleSetInternationalValues = () => {
        const fieldsValue: any = {};
        (basicInfoForm?.translationList || []).forEach((p: any) => {
            fieldsValue[p.langType] = p.translationValue;
        });
        internationalRef.current.form.setFieldsValue(fieldsValue);
    };
    const handleAppChange = (appId: any) => {
        setappId(appId);
        form.validateFields();
    };
    const handleInternationalInputSave = (values: any) => {
        setInternationalValues(values);
    };

    const generateFormItems = () => {
        const items = [
            {
                label: i18n.t('name', '角色名称'),
                name: 'roleName',
                field: InternationalInput,
                itemProps: {
                    rules: [
                        {
                            required: true,
                            message: i18n.t('message', '请输入角色名称'),
                        },
                        {
                            validator: validatorIllegalCharacter,
                        },
                        {
                            whitespace: true,
                        },
                        {
                            validator: (rule: any, value: string) => {
                                if (mode === 'modify' && basicInfoForm?.roleName === value) {
                                    return Promise.resolve();
                                }
                                if (
                                    typeof appId !== 'undefined' &&
                                    roleList.findIndex(
                                        (p: any) => p.appId === appId && p.roleName === value,
                                    ) !== -1
                                ) {
                                    return Promise.reject(i18n.t('message', '角色名称已存在'));
                                }
                                return Promise.resolve();
                            },
                        },
                    ],
                },
                fieldProps: {
                    allowClear: true,
                    placeholder: i18n.t('message', '请输入角色名称'),
                    maxLength: 50,
                    modalType: mode === 'create' ? 'add' : 'edit',
                    internationalType: 'role',
                    entryKey: (basicInfoForm || {}).roleName,
                    //@ts-ignore
                    entryIdOrCode: (basicInfoForm || {}).roleId,
                    ref: internationalRef as any,
                    onSave: handleInternationalInputSave,
                },
            },
            {
                label: i18n.t('name', '角色编码'),
                name: 'roleCode',
                field: Input,
                itemProps: {
                    rules:
                        mode === 'create'
                            ? [
                                  {
                                      required: true,
                                      message: i18n.t('message', '请输入角色编码'),
                                  },
                                  {
                                      min: 2,
                                      message: i18n.t('message', '至少输入2个字符'),
                                  },
                                  {
                                      validator: (rule: any, value: string) => {
                                          if (/^[0-9a-zA-Z]*$/g.test(value)) {
                                              return Promise.resolve();
                                          }
                                          return Promise.reject(
                                              i18n.t('message', '只能输入数字和字母'),
                                          );
                                      },
                                  },
                                  {
                                      validator: validatorIllegalCharacter,
                                  },
                                  {
                                      whitespace: true,
                                  },
                                  {
                                      validator: (rule: any, value: string) => {
                                          //@ts-ignore
                                          if (mode === 'modify') {
                                              return Promise.resolve();
                                          }
                                          if (
                                              roleList?.findIndex(
                                                  (p: any) => p.roleCode == value,
                                              ) !== -1
                                          ) {
                                              return Promise.reject(
                                                  i18n.t('message', '角色编码已存在'),
                                              );
                                          }
                                          return Promise.resolve();
                                      },
                                  },
                              ]
                            : [],
                },
                fieldProps: {
                    allowClear: true,
                    maxLength: 10,
                    minLength: 2,
                    placeholder: i18n.t('message', '请输入角色编码'),
                    disabled: mode === 'modify',
                    onChange: (e: MouseEvent) => {
                        // @ts-ignore
                        const value = e.target.value;
                        if (value && value.length > 10) return;
                        if (value) {
                            const selectionStart = e?.target?.selectionStart;
                            form.setFieldsValue({
                                roleCode: value.toUpperCase(),
                            });
                            setTimeout(() => {
                                e?.target?.setSelectionRange(selectionStart, selectionStart);
                            });
                        }
                    },
                },
            },
            {
                label: i18n.t('name', '角色类型'),
                name: 'roleType',
                field: Select,
                itemProps: {
                    initialValue: roleType || '3',
                    rules: [
                        {
                            required: true,
                            message: i18n.t('message', '请选择角色类型'),
                        },
                    ],
                },
                fieldProps: {
                    placeholder: i18n.t('message', '请选择角色类型'),
                    disabled: true,
                    options: Object.keys(roleTypeMap).map((key) => ({
                        // @ts-ignore
                        label: roleTypeMap[key],
                        value: key,
                    })),
                },
            },
            {
                label: i18n.t('name', '归属应用'),
                name: 'appId',
                field: Select,
                itemProps: {
                    rules: [
                        {
                            required: true,
                            message: i18n.t('message', '请选择归属应用'),
                        },
                    ],
                },
                fieldProps: {
                    placeholder: i18n.t('message', '请选择应用'),
                    options: appList,
                    disabled: mode === 'modify' || OperateType === 'copy',
                    onChange: handleAppChange,
                },
            },
            {
                label: i18n.t('name', '角色描述'),
                name: 'roleDesc',
                field: TextArea,
                itemProps: {
                    rules: [{ required: false }],
                },
                fieldProps: {
                    allowClear: true,
                    placeholder: i18n.t('message', '不多于500字'),
                    maxLength: 500,
                    showCount: true,
                    // autoSize: { minRows: 5, maxRows: 5 },
                },
            },
        ];

        // 非中台不显示归属应用
        if (getAppGlobalData('APP_ID')) {
            items.splice(3, 1);
            // setappId(getAppGlobalData('APP_ID'));
        }
        return items;
    };

    const formItems = getCustomItems(getQueryForm, generateFormItems(), undefined);
    const handleSingleColumn = (single) => {
        setSingleColumn(single);
    };
    return (
        <div className="basic-info-form-container">
            <Form layout="vertical" form={form}>
                <RspFormLayout layoutType='auto' onSingleColumn={handleSingleColumn}>
                {formItems.map((item) => {
                        const Field = item.field;
                    return (item.name == 'roleDesc' ?
                            <RspFormLayout.SingleRow>
                                <StarryAbroadFormItem
                                key={item.name}
                                label={item.label}
                                name={item.name}
                                {...item.itemProps}
                            >
                                {Field && (
                                    // @ts-ignore
                                    <Field {...item.fieldProps} style={{ width: '100%' }} />
                                )}
                            </StarryAbroadFormItem>
                            </RspFormLayout.SingleRow>:
                            <StarryAbroadFormItem
                                key={item.name}
                                label={item.label}
                                name={item.name}
                            {...item.itemProps}
                            style={{paddingRight:singleColumn&&item.name=='roleName'?20:0}}
                            >
                                {Field && (
                                    // @ts-ignore
                                    <Field {...item.fieldProps} style={{ width: '100%' }} />
                                )}
                            </StarryAbroadFormItem>
                        );
                    })}
                </RspFormLayout>
            </Form>
        </div>
    );
};

const BasicInfoWrapper = React.forwardRef<RefBasicInfoProps, BasicInfoProps>(BasicInfo) as (
    props: React.PropsWithChildren<BasicInfoProps> & {
        ref?: React.Ref<RefBasicInfoProps>;
    },
) => React.ReactElement;

export default withSharePropsHOC(BasicInfoWrapper);
