import React, { useState, useRef, useImper<PERSON><PERSON><PERSON><PERSON>, useEffect } from 'react';
import { Select, Button, Empty, Tooltip } from '@streamax/poppy';
import { ListDataContainer } from '@streamax/starry-components';
import { i18n } from '@base-app/runtime-lib';
import ServerTreeTable from '../../server-manage/ServerTreeTable';
import { getNewServerList, gethasAuthServerList } from '../../../../service/server';
import { toTreeByRecursion } from '@/utils/commonFun';
import './ServerPermission.less';
import Search from './Search';
import { getFormatList, findAllParentKeys, getAllKeys } from '../util';
// import _ from 'lodash'

type OperateType = 'add' | 'edit' | 'copy';
interface ServerProps {
    appId?: any;
    OperateType: OperateType;
    roleId?: string;
}
enum ServiceType {
    PRESET_SERVER = 1, //预设服务
    CUSTOM_SERVER = 2, //自定义服务
}
type DataRow = {
    resourcegroupType: ServiceType;
    parentId: string;
    sortValue: number;
    children: [];
};
enum TreeNodeType {
    root = 1, //根节点只有一个
    child = 2, //不是根节点多个
}
interface RefPagePermissionProps {
    serverdata: () => any[];
}

const { Option } = Select;
const ServerPermission: React.ForwardRefRenderFunction<RefPagePermissionProps, ServerProps> = (
    props,
    ref,
) => {
    const { appId, OperateType, roleId } = props;
    const listDataContainerRef = useRef(null);
    const [resourceList, setResourceList] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState<any>([]);
    const [checkedKeys, setCheckedKeys] = useState([]);
    const [formatList, setFormatList] = useState<
        { value: string; label: string; path: string; key: string }[]
    >([]);
    const [filteredOptions, setFilteredOptions] = useState<any[]>([]);
    const [treeData, setTreeData] = useState<any[]>([]);
    const treeRef = useRef<any>();
    const [selectKey, setSelectKey] = useState(''); // 选中节点key
    const [highlightedKey,setHighlightedKey] = useState('');
    useEffect(() => {
        const allKeys = getAllKeys(treeData);
        setExpandedKeys(allKeys);
    }, [treeData]);
    const columns = [
        {
            title: i18n.t('name', '服务名称'),
            dataIndex: 'resourcegroupName',
            colSize: 16,
            render: (text: any, record: any) => {
                return i18n.t(`@i18n:@resourcegroup__${record.resourcegroupCode}`, record.resourcegroupName);
            },
        },
        {
            title: i18n.t('name', '描述'),
            dataIndex: 'resourcegroupDescription',
            colSize: 8,
            render: (text: any) => text || '-',
        },
    ];

    useImperativeHandle(ref, () => ({
        serverdata: () => {
            return checkedKeys;
        },
    }));

    const parentIds: any = [];
    const checkParent = (key: any) => {
        const page = resourceList.find((p: any) => p.resourceId === key);

        if (page) {
            // @ts-ignore
            const parentId = page.parentId;
            if (parentId) {
                parentIds.push(parentId);
                checkParent(parentId);
            }
        }
        return parentIds;
    };

    const handleCheck = ({ checked: checkedKey }, { node, checked }) => {
        let newCheckedKeys = [...checkedKey];
        const checkedAllId: number[] = [];
        const checkChildren = (nodeArr: any) => {
            nodeArr.forEach((element: any) => {
                element.resourceId && checkedAllId.push(element.resourceId);
                if (element.children && element.children.length) {
                    checkChildren(element.children);
                }
            });
        };
        const parentIds = checkParent(node.resourceId);
        checkChildren([node]);
        if (checked) {
            newCheckedKeys = newCheckedKeys.concat(checkedAllId).concat(parentIds);
        } else {
            newCheckedKeys = checkedKey.filter((p) => checkedAllId.indexOf(p) == -1);
        }
        // @ts-ignore
        setExpandedKeys(expandedKeys.concat([node.resourceId]));
        setCheckedKeys(Array.from(new Set(newCheckedKeys)));
    };
    /**
     * @description:
     * @param {DataRow} data
     * @param {1} flag 是否为根节点1 根节点， 2不是根节点
     * @return {*}
     */
    const sortTreeData = (data: DataRow[], flag: TreeNodeType) => {
        let child = [];
        //是否为根节点
        if (flag === TreeNodeType.root) {
            child = data[0].children;
        } else {
            child = data;
        }
        child.sort(function (a, b) {
            return a.sortValue - b.sortValue;
        });
        child.forEach((item) => {
            if (item.children) {
                item.children = sortTreeData(item.children, 2);
            }
        });
        if (flag === TreeNodeType.root) {
            data[0].children = child;
            return data;
        } else {
            return child;
        }
    };
    const localGetList = async (param: any) => {
        // 若是复制，加载角色授权的服务列表，设置复制的角色勾选的服务
        let res = {
            list: [],
        };
        if (OperateType === 'copy') {
            res = await gethasAuthServerList({
                page: 1,
                pageSize: 100000000,
                appId,
                roleId,
            });
        } else {
            res = await getNewServerList({
                page: 1,
                pageSize: 100000000,
                appId,
                state: 1, //查询启用的服务
            });
        }
        setResourceList(res?.list || []);
        if (OperateType === 'copy') {
            setCheckedKeys(
                (res?.list || [])
                    .map((item: any) => {
                        if (item.authority) {
                            return item.id;
                        }
                        return undefined;
                    })
                    .filter((id: any) => typeof id !== 'undefined'),
            );
        }
        const treeOption = {
            enable: true,
            keyField: 'key',
            valueField: 'value',
            titleField: 'resourceId',

            keyFieldBind: 'id',
            valueFieldBind: 'id',
            titleFieldBind: 'id', // 标题字段名称绑定字段名称
        };
        const list = toTreeByRecursion(res.list, 'id', 'parentId', null, 'children', treeOption);
        // @ts-ignore
        let resourcegroupType1: DataRow[] = []; //预设服务
        let resourcegroupType2: DataRow[] = []; //自定义服务
        list.forEach((item: DataRow) => {
            if (item.resourcegroupType === ServiceType.PRESET_SERVER) {
                resourcegroupType1.push(item);
            } else {
                resourcegroupType2.push(item);
            }
        });
        resourcegroupType1 = sortTreeData(resourcegroupType1, TreeNodeType.child);
        resourcegroupType2 = sortTreeData(resourcegroupType2, TreeNodeType.child);
        setTreeData([...resourcegroupType1, ...resourcegroupType2]);
        setFormatList(getFormatList([...resourcegroupType1, ...resourcegroupType2]));
        return Promise.resolve({
            ...res,
            list: [...resourcegroupType1, ...resourcegroupType2],
        });
    };

    const onExpand = (expandedKeysValue: React.Key[], info: any) => {
        const { node, expanded } = info;
        if (expanded) {
            // 如果是展开操作，直接使用expandedKeysValue
            setExpandedKeys(expandedKeysValue);
        } else {
            // 如果是折叠操作，从expandedKeys中移除当前节点的key
            setExpandedKeys(expandedKeys.filter((key: number) => key !== node.key));
        }
    };

    const handleSelect = (value, option) => {
        setSelectKey(option.value);
    };
    const handleReset = () => {
        setSelectKey('');
        setHighlightedKey('');
        setFilteredOptions([]);
    };
    const handleSearch = () => {
        if (selectKey) {
            const parents = findAllParentKeys(
                treeData,
                selectKey,
            );
            setExpandedKeys(Array.from(new Set(expandedKeys.concat(parents))));
            setTimeout(() => {
                treeRef.current?.scrollTo({
                    key: selectKey,
                    align: 'top',
                    offset: 200,
                });
                // 判断元素是否在可视区域
                const element = document.querySelector(`[data-key="${selectKey}"]`);
                if (element) {
                    const elementRect = element.getBoundingClientRect();
                    const isInViewport = (
                        elementRect.top >= 0 &&
                        elementRect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
                    );

                    if (!isInViewport) {
                        const offset = elementRect.top + window.scrollY - 200;
                        window.scrollTo({ top: offset, behavior: 'smooth' });
                    }
                }
            }, 100);
            setHighlightedKey(selectKey);
        }
    };
    const items: any = [
        {
            label: i18n.t('name', '服务名称'),
            name: 'taskName',
            field: Search,
            fieldProps: {
                formatList: formatList,
                handleSelect: handleSelect,
                filteredOptions: filteredOptions,
                setFilteredOptions: setFilteredOptions,
                handleReset: handleReset
            },
        },
    ];
    return (
        <div className="role-add-server-permission">
            <ListDataContainer
                pagination={undefined}
                getDataSource={(param: any) => {
                    return localGetList(param);
                }}
                queryForm={{
                    items: items,
                    onSearch: handleSearch,
                    onReset: handleReset,
                }}
                ref={listDataContainerRef}
                listRender={(data) => {
                    return (
                        <ServerTreeTable
                            columns={columns}
                            treeData={data}
                            loading={false}
                            expandedKeys={expandedKeys}
                            checkedKeys={checkedKeys}
                            onCheck={handleCheck}
                            onExpand={onExpand}
                            searchKey={highlightedKey}
                            treeRef={treeRef}
                        />
                    );
                }}
            />
        </div>
    );
};

const ServerPermissionWraper = React.forwardRef<RefPagePermissionProps, ServerProps>(
    ServerPermission,
) as (
    props: React.PropsWithChildren<ServerProps> & {
        ref?: React.Ref<RefPagePermissionProps>;
    },
) => React.ReactElement;
export default ServerPermissionWraper;
