import { Tooltip } from '@streamax/poppy';
import { TooltipProps } from '@streamax/poppy/lib/tooltip';
import React, { forwardRef, useState, useImperativeHandle } from 'react';

export interface ControlTooltipRef{
    setTooltipVisible: (visible: boolean) => void;
}

const ControlTooltip = forwardRef<ControlTooltipRef,TooltipProps>((props, myRef) => {
    const [tooltipVisible, setTooltipVisible] = useState<boolean>(false);
    useImperativeHandle(myRef, () => ({
        setTooltipVisible
    }));

    return (
        <Tooltip
            {...props}
            visible={tooltipVisible}
            onVisibleChange={(visible) => setTooltipVisible(visible)}
        />
    );
});

export default ControlTooltip;