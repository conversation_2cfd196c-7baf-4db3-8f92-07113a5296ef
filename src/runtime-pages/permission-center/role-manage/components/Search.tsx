import { i18n } from '@base-app/runtime-lib';
import { Select } from '@streamax/poppy';
import { useRef, useState } from 'react';
import type { ControlTooltipRef } from './ControlTooltip';
import ControlTooltip from './ControlTooltip';
import './Search.less';

type FormList = { value: string; label: string; path: string; key: string }

type SearchProps = {
    formatList: FormList[];
    handleSelect: (value: any, option: any) => void;
    filteredOptions: FormList[];
    setFilteredOptions: (value: any) => void,
    handleReset: () => void
}
export default function Search(props: SearchProps) {
    const { formatList, handleSelect, filteredOptions, setFilteredOptions,handleReset } = props;
    const [searchValue, setSearchValue] = useState<string | undefined>(undefined);// 输入框文本
    const [value, setValue] = useState<string | undefined>(undefined);// 下拉框选中值
    const tooltipRef = useRef<ControlTooltipRef | null>(null);

    return (<div className="server-permission-wraper-search">
        <div className="server-permission-wraper-search-left">
            <Select
                showSearch
                allowClear={true}
                placeholder={i18n.t('message', '请输入服务名称')}
                optionLabelProp='desc'
                suffixIcon={null}
                style={{ width: '100%' }}
                options={filteredOptions.map((item, index) => ({
                    value: item.key,
                    label: (
                        <ControlTooltip
                            key={item.key}
                            title={item.path}
                            ref={tooltipRef}
                        >
                            <div
                                className='title'
                                style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                            >
                                {item.label}
                            </div>
                        </ControlTooltip>
                    ),
                    desc: item.value,
                }))}
                filterOption={(input, option) => {
                    return true;
                }}
                onSearch={(value) => {
                    setSearchValue(value.slice(0, 50));
                    const filteredOptions = formatList.filter(item =>
                        item.value.toLowerCase().includes(value.toLowerCase().trim())
                    );
                    setFilteredOptions(filteredOptions.slice(0, 50));
                }}
                onSelect={(value, option) => {
                    handleSelect(value, option);
                    setValue(value);
                }}
                searchValue={searchValue === '' ? undefined : searchValue}
                value={value}
                onClear={() => {
                    handleReset();
                    setValue(undefined);
                    setSearchValue(undefined);
                }}
                onBlur={() => {
                    setSearchValue(searchValue ? searchValue : undefined);
                }}
                onDropdownVisibleChange={(open) => {
                    !open && tooltipRef.current?.setTooltipVisible(false);
                }}
            />
        </div>
    </div>);
};
