import { Effect, Reducer } from '@base-app/runtime-lib/core';
import { fetchApplicationPageList } from '../../../service/application';
import { getResourceList } from '../../../service/resource';
import {
    changeRoleState,
    createRole,
    deleteRole,
    deleteRoleAuthorizedUser,
    editRole,
    getRoleAuthorizedUser,
    getRoleDetail,
} from '../../../service/role';

export interface RoleDetail {
    roleId?: number;
    roleName?: string;
    roleCode?: string;
    roleDesc?: string;
    state?: number;
    appId?: number;
    applicationName?: string;
    roleType?: number;
    creatorId?: number;
    creatorName?: string;
    createTime?: number;
    updaterId?: number;
    updaterName?: string;
    updateTime?: number;
}

export interface BasicInfoForm {
    roleName: string | null;
    roleCode: string | null;
    roleType: string | number | null;
    appId: number | null;
    roleDesc?: string;
    translationList?: any
}

export interface RoleManageModelState {
    roleDetail: RoleDetail;
    basicInfoForm: BasicInfoForm; // 基本信息表单数据， 主要用于回填数据
}

export interface RoleManageModel {
    namespace: 'roleManage';
    state: RoleManageModelState;
    effects: {
        fetchAppList: Effect;
        fetchRoleDetail: Effect;
        fetchResource: Effect; // 查询全量的资源数据
        deleteRole: Effect;
        editRole: Effect;
        lockRole: Effect;
        createRoleBasicInfo: Effect;

        queryRoleInfoById: Effect;
        deleteAuthorizedUser: Effect;
        fetchRoleAuthorizedUsers: Effect;
    };
    reducers: {
        save: Reducer<any>;
        saveRoleDetail: Reducer<any>;
        saveBasicInfoForm: Reducer<any>;
        clearBackFillData: Reducer<any>;
    };
}

const backFillInitData = {
    basicInfoForm: {
        roleName: null,
        roleCode: null,
        roleType: '3',
        appId: null,
        translationList: null
    },
};

const model: RoleManageModel = {
    namespace: 'roleManage',
    state: {
        roleDetail: {},
        ...backFillInitData,
    },
    effects: {
        *fetchAppList({ payload }, { call }) {
            try {
                const list = yield call(
                    fetchApplicationPageList,
                    payload
                );
                return list;
            } catch (error) {
                // console.log(error);
                return Promise.reject(error);
            }
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        *deleteRole({ payload }, { call }) {
            try {
                const result = yield call(
                    deleteRole,
                    payload
                );
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *editRole({ payload }, { call }) {
            try {
                const result = yield call(
                    editRole,
                    payload,
                );
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *lockRole({ payload }, { call }) {
            try {
                const result = yield call(
                    changeRoleState,
                    payload,
                );
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *fetchRoleDetail({ payload }, { call, put }) {
            try {
                const result = yield call(
                    getRoleDetail,
                    payload,
                );
                yield put({
                    type: 'saveRoleDetail',
                    payload: result,
                });
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *fetchResource({ payload }, { call }) {
            try {
                const resource = yield call(
                    getResourceList,
                    payload,
                );
                return resource;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *createRoleBasicInfo({ payload }, { call }) {
            try {
                const data = yield call(
                    createRole,
                    payload
                );
                return data;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *queryRoleInfoById({ payload }, { call }) {
            const result = yield call(
                getRoleDetail,
                payload,
            );
            return result;
        },
        *deleteAuthorizedUser({ payload }, { call }) {
            try {
                const result = yield call(
                    deleteRoleAuthorizedUser,
                    payload,
                );
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
        *fetchRoleAuthorizedUsers({ payload }, { call }) {
            try {
                const result = yield call(
                    getRoleAuthorizedUser,
                    payload,
                );
                return result;
            } catch (error) {
                return Promise.reject(error);
            }
        },
    },
    reducers: {
        save(state, action) {
            return {
                ...state,
                list: action.payload,
            };
        },
        saveRoleDetail(state, { payload }) {
            return {
                ...state,
                roleDetail: payload,
            };
        },
        saveBasicInfoForm(state, { payload }) {
            return {
                ...state,
                basicInfoForm: payload,
            };
        },
        clearBackFillData(state) {
            return {
                ...state,
                ...backFillInitData,
            };
        },
    },
};

export default model;
