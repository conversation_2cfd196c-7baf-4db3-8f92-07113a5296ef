/**
 * 角色新增编辑页面
 * 入口来源：
 * 1. 直接新增，不需要携带任何参数
 * 2. 复制新增，需要携带被复制角色ID
 * 3. 详情编辑，需要携带编辑角色ID
 *
 * 来源角色：
 * 1. 直接新增，固定功能管理员，需展示3个步骤
 * 2. 复制新增，可能租户、应用、功能管理员（需求未明确），只有功能管理员展示3个步骤，其余只展示基本信息
 * 3. 详情编辑，可能租户、应用、功能管理员（需求未明确），只展示基本信息
 */

import { Button, message, Space, Steps, Container } from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    reLoadLanguage,
    useConstructor,
    RouterPrompt,
    StarryAbroadLRLayout,
} from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard, Action } from '@base-app/runtime-lib';
import type { Dispatch } from '@base-app/runtime-lib/core';
import {
    getDvaApp,
    useDispatch,
    useHistory,
    useLocation,
    useSelector,
} from '@base-app/runtime-lib/core';
import { useEffect, useRef, useState } from 'react';
import { RoleAuthServer } from '../../../../service/server';
import { useComponentWillMount } from '../../server-manage/server-add/foldTreeTable/hooks';
import BasicInfo from '../components/BasicInfo';
import ServerPermission from '../components/ServerPermission';
import type { RoleManageModelState } from '../model';
import RoleManageModel from '../model';
import './index.less';
import { useSubmitFn } from '@streamax/hooks';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { FormEdit } from '@/types/pageReuse/pageReuseBase';

export type RoleAddEditShareProps = FormEdit;

// const TYPE_ROLE_TENANT = '1';
// const TYPE_ROLE_APPLICATION = '2';
// const TYPE_ROLE_OPERATE = '3';

type OperateType = 'add' | 'edit' | 'copy';

type RoleType = '1' | '2' | '3';

interface Query {
    operateType: OperateType;
    roleId?: string;
    roleType?: RoleType;
}

interface Location {
    query: Query;
}

// const TYPE_RESOURCE_MENU = 1;
// const TYPE_RESOURCE_PAGE = 2;
// const TYPE_RESOURCE_OPERATE = 3;

const { Step } = Steps;

const RoleAddEdit = (props: RoleAddEditShareProps) => {
    /** 定制 */
    const { onFormSubmit, injectFormSubmit } = props;

    const [currentStep, setCurrentStep] = useState<number>(0);
    const basicInfoRef: any = useRef<any>();
    const serverPermissionRef = useRef();
    const [when, setWhen] = useState(true);
    const dispatch: Dispatch = useDispatch();
    const history: any = useHistory();

    // @ts-ignore
    const { query }: { query: Query } = useLocation<Location>();
    const { operateType, roleId, roleType } = query;

    // 是否需要渲染步骤条
    const showStepComponent = operateType === 'add' || operateType === 'copy';


    const appId = getAppGlobalData('APP_ID');

    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(RoleManageModel);
    });

    const { basicInfoForm } =
        useSelector((state: { roleManage: RoleManageModelState }) => ({
            basicInfoForm: state.roleManage?.basicInfoForm,
        })) || {};

    const saveBasicInfoFormData = (values: any) => {
        dispatch({
            type: 'roleManage/saveBasicInfoForm',
            payload: values,
        });
    };

    const fetchRoleDataById = (id: string) => {
        dispatch({
            type: 'roleManage/queryRoleInfoById',
            payload: {
                roleId: id,
            },
        }).then((rs: any) => {
            // 回填的数据统一由redux管理
            saveBasicInfoFormData(rs);
        });
    };

    // 在渲染页面之前先清空redux中的回填数据，防止污染
    useComponentWillMount(() => {
        dispatch({
            type: 'roleManage/clearBackFillData',
        });
    });

    useEffect(() => {
        // 根据不同的入口方式，决定不同的数据回填
        if (operateType === 'edit') {
            if (!roleId) return;
            fetchRoleDataById(roleId);
        } else if (operateType === 'copy') {
            if (!roleId) return;
            fetchRoleDataById(roleId);
        }
    }, []);

    const submitEditBasicInfo = (values: any) => {
        const { roleName, roleDesc, ...others } = values;
        const editParams = {
            roleId,
            roleName,
            roleDesc,
            ...others,
        };
        if (injectFormSubmit) {
            injectFormSubmit(editParams);
            return;
        }
        dispatch({
            type: 'roleManage/editRole',
            payload: editParams,
        }).then(async () => {
            message.success(i18n.t('message', '保存成功'));
            setWhen(false);
            reLoadLanguage && (await reLoadLanguage(true, true));
            history.goBack();
        });
    };

    const createRoleBasicInfo = () => {
        const info = {
            ...(basicInfoForm || {}),
            roleName: (basicInfoForm?.roleName || '').replace(/(^\s*) | (\s*$)/g, ''),
            roleCode: (basicInfoForm?.roleCode || '').replace(/(^\s*) | (\s*$)/g, ''),
            roleDesc: (basicInfoForm?.roleDesc || '').replace(/(^\s*) | (\s*$)/g, ''),
        };
        if (appId) {
            // @ts-ignore
            info.appId = appId;
        }
        return dispatch({
            type: 'roleManage/createRoleBasicInfo',
            payload: info,
        });
    };
    const customCreateRoleBasicInfo = async () => {
        const info = {
            ...(basicInfoForm || {}),
            roleName: (basicInfoForm?.roleName || '').replace(/(^\s*) | (\s*$)/g, ''),
            roleCode: (basicInfoForm?.roleCode || '').replace(/(^\s*) | (\s*$)/g, ''),
            roleDesc: (basicInfoForm?.roleDesc || '').replace(/(^\s*) | (\s*$)/g, ''),
        };
        if (injectFormSubmit) {
            return await injectFormSubmit(info);
        }
        return Promise.reject('');
    };

    const goToRoleDetail = (id: any) => {
        Action.openActionUrl({
            code: '@base:@page:role.manage:add@action:detail',
            history,
            url: '/role-manage/detail',
            params: {
                roleId: id,
            },
        });
    };

    const handlePrevStep = () => {
        const step = currentStep - 1;
        if (step < 0 || !showStepComponent) {
            setWhen(false);
            // 返回上一页
            history.goBack();
            return;
        }
        setCurrentStep(step);
        setTimeout(() => {
            basicInfoRef.current?.handleSetInternationalValues();
        }, 0);
    };
    const [handleNextStep, nextStepLoading] = useSubmitFn(async () => {
        const step = currentStep + 1;
        if (!showStepComponent) {
            // 编辑模式， 直接请求后端保存数据
            // @ts-ignore
            const form = basicInfoRef.current.formInstance;
            const values = await form.validateFields();
            // @ts-ignore
            const internationalValues = basicInfoRef.current.internationalValues;
            const obj: any = {};
            // 没有编辑国际化，需要判断当前是编辑的对象名称还是国际化词条
            if (!internationalValues) {
                // @ts-ignore
                if (i18n.exists(`@i18n:@role__${basicInfoForm?.roleId}`)) {
                    // 存在国际化词条，说明编辑的是国际化词条
                    obj.roleName = basicInfoForm?.roleName;
                    const lang = getAppGlobalData('APP_LANG');
                    obj[lang] = values.roleName;
                } else {
                    // 不存在国际化词条，说明编辑的是对象名称
                    obj.roleName = values.roleName;
                }
            } else {
                // @ts-ignore
                // if (i18n.exists(`@i18n:@role__${basicInfoForm.roleId}`)) {
                //     if (internationalValues.translationList && internationalValues.translationList.length > 0) {
                //         obj.roleName = internationalValues.objectName;
                //     } else {
                //         obj.roleName = values.roleName;
                //     }
                // } else {
                //     obj.roleName = values.roleName;
                // }
                obj.roleName = internationalValues.objectName;
                internationalValues.entryId = internationalValues.langId;
                delete internationalValues.objectName;
                delete internationalValues.langId;
                Object.assign(obj, internationalValues);
            }
            const { roleDesc } = values;
            obj.roleName = obj.roleName.replace(/(^\s*) | (\s*$)/g, '');
            obj.roleDesc = roleDesc?.replace(/(^\s*) | (\s*$)/g, '');
            submitEditBasicInfo(obj);
            return;
        }

        if (step === 1) {
            // 新增(复制), 缓存基础信息数据到redux，并进入下一步
            // @ts-ignore
            const form = basicInfoRef.current.formInstance;
            const values = await form.validateFields();
            if (appId) {
                values.appId = appId;
            }
            // @ts-ignore
            const internationalValues = basicInfoRef.current.internationalValues;
            if (internationalValues) {
                values.translationList = internationalValues.translationList;
            }
            saveBasicInfoFormData(values);
            setCurrentStep(step);
            return;
        }

        if (step > 1) {
            // 最后一步，创建角色
            const rs = injectFormSubmit
                ? await customCreateRoleBasicInfo()
                : await createRoleBasicInfo();
            const newRoleId = rs; // 拿到创建成功的角色ID，接着保存权限资源
            // @ts-ignore
            const resourceIds = serverPermissionRef.current.serverdata();
            // 如果有选择设置权限，则发送请求
            setWhen(false);
            if (resourceIds.length > 0) {
                await RoleAuthServer({
                    roleId: newRoleId,
                    appId: basicInfoForm?.appId,
                    resourcegroupIds: resourceIds.join(','),
                });
                message.success(i18n.t('message', '创建成功'));
                reLoadLanguage && (await reLoadLanguage(true, true));
                goToRoleDetail(newRoleId);
            } else {
                message.success(i18n.t('message', '创建成功'));
                reLoadLanguage && (await reLoadLanguage(true, true));
                goToRoleDetail(newRoleId);
            }
        }
    });

    // 生成步骤条组件
    const generateStepsCom = () => {
        if (!showStepComponent) return null;
        return (
            <div className="steps-com-wrapper">
                <Steps current={currentStep}>
                    <Step title={i18n.t('name', '基本信息')} />
                    <Step title={i18n.t('name', '授权服务')} />
                </Steps>
            </div>
        );
    };

    const generateContentCom = () => {
        let com;
        switch (currentStep) {
            case 0:
                com = (
                    <BasicInfo
                        mode={operateType === 'edit' ? 'modify' : 'create'}
                        roleType={roleType}
                        OperateType={operateType}
                        ref={basicInfoRef as any}
                    />
                );
                break;
            case 1:
                com = (
                    <ServerPermission
                        ref={serverPermissionRef as any}
                        appId={basicInfoForm?.appId as any}
                        OperateType={operateType}
                        roleId={roleId}
                    />
                );
                break;
            default:
                break;
        }

        return (
            <div className="content-com-wrapper">
                {com}
            </div>
        );
    };

    // 生成底部操作按钮
    const generateOperateCom = () => {
        let prevText = i18n.t('action', '取消');
        let nextText = i18n.t('action', '保存');
        if (currentStep !== 0) {
            prevText = i18n.t('action', '上一步');
        }
        if (currentStep !== 1 && operateType !== 'edit') {
            nextText = i18n.t('action', '保存并下一步');
        }

        return (
            <div className="operate-com-wrapper">
                <StarryAbroadLRLayout>
                    <Button onClick={handlePrevStep}>{prevText}</Button>
                    <Button type="primary" onClick={handleNextStep} loading={nextStepLoading}>
                        {nextText}
                    </Button>
                </StarryAbroadLRLayout>
            </div>
        );
    };

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard className="role-manage-detail-edit">
                <div className="role-add-edit-page-container">
                    {generateStepsCom()}
                    <Container>
                        {
                            <>
                                {operateType === 'edit' && (
                                    <div className="role-add-edit-page-container-title">
                                        {i18n.t('name', '基本信息')}
                                    </div>
                                )}
                                {generateContentCom()}
                            </>
                        }
                    </Container>
                    {generateOperateCom()}
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(RoleAddEdit);
