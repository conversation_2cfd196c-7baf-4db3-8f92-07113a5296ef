@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.role-manage-detail-edit {
    .role-add-edit-page-container {
        .role-add-edit-page-container-title {
            padding-bottom: 24px;
            color: @starry-text-color-primary;
            font-weight: 700;
            font-size: 20px;
        }
        .steps-com-wrapper::abroad {
            margin-top: 24px;
        }
        .steps-com-wrapper {
            width: 100%;
            margin: 16px 0 20px 0;
        }
        .content-com-wrapper {
            width: 100%;
            margin: 0 0 24px 0;
        }
        .fold-tree-table-container
            .fold-tree-table-content-wrapper
            .poppy-tree-treenode.poppy-tree-treenode-leaf-last {
            height: auto;
            padding-bottom: 12px;
        }
    }
    .poppy-form-item-group {
        display: flex;
        /* flex-wrap: unset; */
        flex-flow: wrap;
        justify-content: space-between;
    }
}

.role-manage-detail-edit::abroad {
    .role-add-edit-page-container::abroad {
        .starry-responsive-basic-layout:last-child::abroad {
            .poppy-form-item::abroad{
                margin-bottom: 0;
            }
        }
    }
}
