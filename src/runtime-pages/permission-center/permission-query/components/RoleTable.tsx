import { useState, useEffect, useRef } from 'react';
import { i18n, getAppGlobalData, Auth, useUrlSearchStore } from '@base-app/runtime-lib';
import { StarryTable } from '@base-app/runtime-lib';
import { Tooltip, TreeSelect, Select, message, Form } from '@streamax/poppy';
import { useHistory } from '@base-app/runtime-lib/core';
import { fetchApplicationPageList } from '../../../../service/application';
import { getResourceList } from '../../../../service/resource';
import { getRoleAuthorizedUserCount, getRoleListByResource } from '../../../../service/role';
import type { ColumnType } from '@streamax/poppy/lib/table';
import type { FormItemProps } from '@streamax/poppy/lib/form';

interface ItemConfig {
    label: string;
    name: string;
    field: any;
    colSize?: number;
    fieldProps?: Record<string, any>;
    itemProps?: FormItemProps;
}

export interface RoleTableProps {
    type: 'menu' | 'page';
}

const findChildren = (data: any[], id: number): any[] => {
    const children = data.filter((item1: any) => {
        if (item1.parentId === id && item1.resourceId !== item1.parentId) {
            // 把该条数据标记为已push，后续不再遍历
            item1.hasPush = true;
            item1.children = findChildren(data, item1.resourceId);
            if (!item1.children || item1.children.length === 0) {
                item1.isLeaf = true;
            }
            // 过滤掉不需要进行管理的
            const newList = (item1.children || []).filter((p: any) => p.managementSwitch === 0);
            // 如果父节点为不需要管理但是子节点存在需要管理的，则父节点禁用
            if (item1.managementSwitch === 1 && newList.length > 0) {
                item1.disableCheckbox = true;
            } else {
                item1.children = newList;
            }
            return true;
        }
        return false;
    });
    children.sort((a: any, b: any) => a.sortValue - b.sortValue);
    return children;
};

const translateDataToTree = (data: any[], type: 'menu' | 'page'): any[] => {
    if (!data) return [];
    data.sort((a: any, b: any) => a.parentId - b.parentId);
    const treeDataList: any[] = [];
    data.forEach((item: any) => {
        const key = item.resourceCode + '';
        item.key = key;
        item.value = key;
        item.disableCheckbox = false;
        if (type === 'menu') {
            item.title = i18n.t(`@i18n:@menu__${item.resourceCode}`, item.resourceName);
        } else {
            item.title = i18n.t(`@i18n:@page__${item.resourceCode}`, item.resourceName);
        }
        if (!item.hasPush) {
            const newData = { ...item };
            const children = findChildren(data, item.resourceId);
            if (children.length) {
                newData.children = children;
            } else {
                newData.isLeaf = true;
            }
            const newList = (newData.children || []).filter((p: any) => p.managementSwitch === 0);
            if (newData.managementSwitch === 1 && newList.length > 0) {
                newData.disableCheckbox = true;
            } else {
                newData.children = newList;
            }
            if (
                !(
                    newData.parentId === null &&
                    newData.managementSwitch === 1 &&
                    (newData.children || []).length === 0
                )
            ) {
                treeDataList.push(newData);
            }
        }
    });
    treeDataList.sort((a: any, b: any) => a.sortValue - b.sortValue);
    return treeDataList;
};

const RoleTable = (props: RoleTableProps) => {
    const { type } = props;
    const searchStore = useUrlSearchStore();

    const [form] = Form.useForm();
    const [selectedApp, setSelectedApp] = useState<any>();
    const [appList, setAppList] = useState<any[]>([]);
    const [resourceTreeData, setResourceTreeData] = useState<any[]>([]);

    const tableRef = useRef<any>();
    const history = useHistory<any>();

    const [appDataReady, setAppDataReady] = useState(false);
    const [resourceDataReady, setResourceDataReady] = useState(false);

    const isClear = useRef(true);
    const requestIndex = useRef<number>(0);
    const APP_ID = getAppGlobalData('APP_ID');

    useEffect(() => {
        if (appDataReady) {
            const query = searchStore.get();
            const appId =
                query[`${type}AppId`] !== undefined ? Number(query[`${type}AppId`]) : null;
            const ids = query[`${query.tab}Ids`];
            form.setFieldsValue({
                appId,
                resourceCodes: ids ? ids.split(',') : [],
            });
            setSelectedApp(appId);
        }
    }, [appDataReady, type]);

    useEffect(() => {
        if (resourceDataReady) {
            const { page, pageSize, tab, ...formQuery } = searchStore.get();
            const ids = formQuery[`${tab}Ids`];
            if (ids && typeof ids === 'string') {
                const resourceCodes = ids.split(',') || [];
                form.setFieldsValue({
                    resourceCodes,
                });
            }
            tableRef.current?.loadDataSource &&
                tableRef.current?.loadDataSource({
                    ...form.getFieldsValue(),
                    page: Number(page) || 1,
                    pageSize,
                });
        }
    }, [resourceDataReady, resourceTreeData, type]);

    useEffect(() => {
        if (APP_ID) {
            setSelectedApp(APP_ID);
        }
        setAppDataReady(false);
        // 获取应用列表
        fetchApplicationPageList({
            page: 1,
            pageSize: 99999999,
            tenantId: (getAppGlobalData('APP_USER_INFO') || {})['tenantId'],
            states: '1',
        }).then((rs) => {
            setAppList(rs.list || []);
            setAppDataReady(true);
        });
    }, []);

    useEffect(() => {
        if (APP_ID || (selectedApp !== null && selectedApp !== undefined)) {
            if (isClear.current) {
                form.setFieldsValue({ resourceCodes: [] });
            } else {
                isClear.current = true;
            }
            getResource(requestIndex.current);
        }
    }, [selectedApp, type]);

    function getResource(index: number) {
        setResourceDataReady(false);
        setResourceTreeData([]);
        getResourceList({
            limit: 1e8,
            resourceType: type === 'menu' ? 1 : 2,
            state: 1,
            appId: selectedApp,
        }).then((rs) => {
            if (index + 1 === requestIndex.current) {
                const data = translateDataToTree(rs, type);
                setResourceTreeData(data);
                setResourceDataReady(true);
            }
        });
        requestIndex.current = index + 1;
    }

    const handleClickRole = (roleId: string | number, tab: string) => {
        // TODO 跳转路由的前缀怎么处理
        history.push({
            pathname: '/role-manage/detail',
            search: `?roleId=${roleId}&tab=${tab}`,
        });
    };

    const columns: ColumnType<any>[] = [
        {
            title: i18n.t('name', '角色编码'),
            dataIndex: 'roleCode',
            ellipsis: { showTitle: false },
            render: (text) => (
                <Tooltip placement="top" title={text}>
                    {text}
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '角色名称'),
            dataIndex: 'roleName',
            ellipsis: { showTitle: false },
            render: (text, record) => {
                return (
                    <Auth code="@base:@page:permission.query@action:role.name" fellback={text}>
                        <Tooltip
                            placement="top"
                            title={i18n.t(`@i18n:@role__${record.roleId}`, text)}
                        >
                            <a onClick={() => handleClickRole(record.roleId, 'authorizedUser')}>
                                {i18n.t(`@i18n:@role__${record.roleId}`, text)}
                            </a>
                        </Tooltip>
                    </Auth>
                );
            },
        },
        {
            title: i18n.t('name', '归属应用'),
            dataIndex: 'appId',
            ellipsis: { showTitle: false },
            render: (text, record) => (
                <Tooltip
                    placement="top"
                    title={i18n.t(`@i18n:@app__${text}`, record.applicationName)}
                >
                    {i18n.t(`@i18n:@app__${text}`, record.applicationName)}
                </Tooltip>
            ),
        },
        {
            title: i18n.t('name', '授权用户数'),
            dataIndex: 'roleAuthorityNumber',
            render: (text, record) => (
                <Auth
                    code="@base:@page:permission.query@action:authority.user"
                    fellback={text || 0}
                >
                    <a onClick={() => handleClickRole(record.roleId, 'authorizedUser')}>
                        {text || 0}
                    </a>
                </Auth>
            ),
        },
        {
            title: i18n.t('name', '角色描述'),
            dataIndex: 'roleDescription',
            ellipsis: { showTitle: false },
            render: (text) => (
                <Tooltip placement="top" title={text}>
                    {text || '-'}
                </Tooltip>
            ),
        },
    ];

    // 存在APP_ID，说明是引用的该页面，不展示所属应用
    if (APP_ID) {
        columns.splice(2, 1);
    }

    const generateQueryFormItems = (): ItemConfig[] => {
        const items: ItemConfig[] = [];
        // 非引用页面，需要展示所属应用查询条件
        if (!APP_ID) {
            items.push({
                label: i18n.t('name', '归属应用'),
                name: 'appId',
                field: Select,
                fieldProps: {
                    placeholder: i18n.t('message', '请选择归属应用'),
                    options: appList
                        .map((item) => ({
                            label: i18n.t(
                                `@i18n:@app__${item.applicationId}`,
                                item.applicationName,
                            ),
                            value: item.applicationId,
                        }))
                        .filter((item: any) => ![66666].includes(item.value)),
                    onChange: (value: any) => {
                        const { tab, ...formQuery } = searchStore.get();
                        const query = { ...formQuery };
                        tab && (query[`${tab}Ids`] = '');
                        searchStore.set(query);
                        setSelectedApp(value);
                    },
                },
            });
        }
        items.push({
            label: type === 'menu' ? i18n.t('name', '选择菜单') : i18n.t('name', '选择页面'),
            name: 'resourceCodes',
            field: TreeSelect,
            fieldProps: {
                disabled: !APP_ID && (selectedApp === null || selectedApp === undefined),
                placeholder: type === 'menu' ? i18n.t('name', '请选择菜单') : i18n.t('name', '请选择页面'),
                treeData: resourceTreeData,
                treeCheckable: true,
                treeCheckStrictly: true,
                maxTagCount: 1,
                showCheckedStrategy: TreeSelect.SHOW_ALL,
                treeNodeFilterProp: 'title',
            },
        });

        return items;
    };

    const handleFetchData = async (values: any) => {
        const { resourceCodes = [], page, pageSize } = values || {};
        if (!appDataReady || !resourceDataReady || resourceCodes.length === 0) {
            return Promise.resolve({
                list: [],
            });
        }
        const resourceCodeStr = (resourceCodes || [])
            .map((item: any) => {
                return typeof item === 'string' ? item : item.value;
            })
            .join(',');
        const query = {
            ...searchStore.get(),
            ...values,
            tab: values.tab || 'menu',
        };
        if (resourceCodeStr) {
            query.resourceCodes = resourceCodeStr;
        }
        searchStore.set({
            ...query,
            tab: type,
            [`${type}Ids`]: query.resourceCodes,
            [`${type}AppId`]: query.appId,
        });
        const result = await getRoleListByResource({
            appId: selectedApp,
            resourceCodes: resourceCodeStr,
            page,
            pageSize,
            resourceType: type === 'menu' ? 1 : 2,
        });
        if (!result || !result.list) {
            return Promise.reject();
        }
        const roleList = result.list;
        const countRes = await getRoleAuthorizedUserCount({
            roleIds: roleList.map((item: any) => item.roleId).join(','),
            page,
            pageSize,
            appId: selectedApp,
        });
        if (!countRes || !countRes.list) {
            return Promise.reject();
        }
        countRes.list.forEach((item: { roleId: number; roleAuthorityUserNumber: number }) => {
            const findItem = roleList.find((p: any) => p.roleId === item.roleId);
            if (findItem) {
                findItem.roleAuthorityNumber = item.roleAuthorityUserNumber;
            }
        });
        return Promise.resolve({
            list: roleList,
            total: result.total,
        });
    };

    const handleReset = (): boolean => {
        // if (!APP_ID) {
        setSelectedApp(null);
        const query = searchStore.get();
        delete query[`${type}AppId`];
        delete query[`${type}Ids`];
        searchStore.set(query);
        // }

        return true;
    };

    const handleSearch = (values: any): boolean => {
        if (!APP_ID && (selectedApp === null || selectedApp === undefined)) return false;
        const { resourceCodes } = values || {};
        if (!resourceCodes || resourceCodes.length === 0) {
            message.warning(
                type === 'menu' ? i18n.t('message', '请选择菜单') : i18n.t('message', '请选择页面'),
            );
            return false;
        }
        return true;
    };

    const { page, pageSize } = searchStore.get();

    return (
        <StarryTable
            columns={columns}
            rowKey="roleId"
            ref={tableRef}
            aroundBordered
            queryProps={{
                items: generateQueryFormItems(),
                onSearch: handleSearch,
                onReset: handleReset,
                form,
            }}
            pagination={{
                defaultCurrent: Number(page) || 1,
                defaultPageSize: Number(pageSize) || 20,
            }}
            fetchDataFunc={handleFetchData as any}
            fetchDataAfterMount={true}
            toolbar={{
                iconBtns: ['reload'],
            }}
        />
    );
};

export default RoleTable;
