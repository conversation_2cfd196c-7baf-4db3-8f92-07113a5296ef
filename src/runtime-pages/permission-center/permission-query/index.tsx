import { useState } from 'react';
import { 
    StarryBreadcrumb, 
    StarryCard
} from '@base-app/runtime-lib';
import { Radio } from '@streamax/poppy';
import { i18n, useUrlSearchStore } from '@base-app/runtime-lib';
import RoleTable from './components/RoleTable';
import './index.less';

type QueryType = 'menu' | 'page';

export default () => {

    const searchStore = useUrlSearchStore();
    const [queryType, setQueryType] = useState<QueryType>(searchStore.get().tab || 'menu');

    const handleRadioChange = (e: any) => {
        const value = e.target.value;
        searchStore.set({
            ...searchStore.get(),
            tab: value
        });
        setQueryType(value);
    };

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <div className="permission-query-page-contianer">
                    <div className="radio-items-wrapper">
                        <Radio.Group onChange={handleRadioChange} value={queryType}>
                            <Radio.Button 
                                value="menu"
                            >
                                <span 
                                    title={i18n.t('message', '根据菜单查询角色和用户')}
                                >
                                    {i18n.t('message', '根据菜单查询角色和用户')}
                                </span>
                            </Radio.Button>
                            <Radio.Button 
                                value="page"
                                
                            >
                                <span 
                                    title={i18n.t('message', '根据页面查询角色和用户')}
                                >
                                    {i18n.t('message', '根据页面查询角色和用户')}
                                </span>
                            </Radio.Button>
                        </Radio.Group>
                    </div>
                </div>
                <div className="content-wrapper">
                    <RoleTable type={queryType} />
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};