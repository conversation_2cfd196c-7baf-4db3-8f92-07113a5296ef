import { useSubmitFn } from '@streamax/hooks';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { Form, Steps, Button, message } from '@streamax/poppy';
import {i18n, RouterPrompt, StarryAbroadLRLayout} from '@base-app/runtime-lib';
// @ts-ignore
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { useLocation, useHistory } from '@base-app/runtime-lib/core';
import { StarryAbroadFormItem as AFormItem } from "@base-app/runtime-lib";
import { flatten } from 'lodash';
import { useState, useEffect, useImperativeHandle, useRef } from 'react';
import React from 'react';
import { authorizeRoleToUser } from '../../../service/authority';
import AuthorizeUser from '../components/AuthorizeUserCom';
import Result from '../components/Result';
import AuthorizeRoleCom from '@/components/AuthorizeRoleCom';
import { getLanguageList } from '@/service/language';
import { getRoleAuthorizedUser } from '@/service/role';
import type { RoleAuthorizeItem } from '@/types/pageReuse/addUser';
import './index.less';
import { runCustomFun } from '@/utils/pageReuse';
import type { Instances } from '@/types/pageReuse/pageReuseBase';

const { Step } = Steps;

export type RoleAuthorizeShareProps = RoleAuthorizeItem & Instances;

export interface RoleAuthorizeRefProps {
    setSelectRoles?: (role: any[]) => void;
}

const RoleAuthorize = (props: RoleAuthorizeShareProps) => {
    /** 定制start */
    const { getRoleColumns, clickAddRole, getInstances } = props;
    /** 定制end */

    const [current, setCurrent] = useState<number>(0);
    const [userJumpId, setUserJumpId] = useState<string>();
    const [isSuccess, setIsSuccess] = useState<boolean>(false);
    const [selectRoles, setSelectRoles] = useState<any[]>([]);
    const [selectUsers, setSelectUsers] = useState<any[]>([]);
    const [langugelist, setLangugeList] = useState<any>();
    const [selectedApp, setSelectedApp] = useState<number | string>();
    const [form] = Form.useForm();
    const [roleList, setRoleList] = useState<any[]>([]);
    const [when, setWhen] = useState(false);
    const history: any = useHistory();

    runCustomFun(getInstances, {
        setSelectRoles: (role: any[]) => {
            form.setFieldsValue({ authorizeRole: role });
        },
    });

    const {
        // @ts-ignore
        query: { userId, roleId, roleName },
    } = useLocation();

    useEffect(() => {
        if (selectRoles.length) {
            (selectRoles || []).forEach(async (item) => {
                const data = await getLanguageList({ langKey: `@i18n:@role__${item.roleId}` });
                const list: any = [];
                list.push(data?.languageList);
                setLangugeList(list);
            });
        }
        if (selectUsers.length && roleId) {
            (selectUsers || []).forEach(async () => {
                const data = await getLanguageList({ langKey: `@i18n:@role__${roleId}` });
                const list: any = [];
                list.push(data?.languageList);
                setLangugeList(list);
            });
        }
    }, [selectRoles, selectUsers]);

    useEffect(() => {
        // 从用户详情进入此页面
        if (userId) {
            setCurrent(1);
            setUserJumpId(userId);
            form.setFieldsValue({
                authorizeUser: [{ userId }],
            });
            // axios.get(
            //     '/base-server-service/api/v1/user/authority/role/',
            //     {
            //         userId,
            //         page: 1,
            //         pageSize: 999999999
            //     }
            // ).then((rs: any) => {
            //     form.setFieldsValue({
            //         authorizeUser: [{ userId }],
            //         authorizeRole: (rs.list || []).map((p: any) => {
            //             p.appName = p.applicationName;
            //             p.roleType = roleMap[p.roleType];
            //             return p;
            //         })
            //     });
            // });
        } else if (roleId) {
            getRoleAuthorizedUser({
                page: 1,
                pageSize: 1e8,
                roleId,
            }).then((res) => {
                setRoleList(res?.list || []);
            });
            // 从角色详情进入此页面
            setCurrent(0);
            form.setFieldsValue({
                authorizeRole: [{ roleId }],
            });
        }
    }, []);
    const onChange = (values: any) => {
        if (values && values.length) {
            setSelectUsers(values);
            setWhen(true);
        } else {
            setWhen(false);
        }
    };
    const onRoleChange = (values: any) => {
        setSelectRoles(values);
    };
    const steps = [
        {
            title: i18n.t('name', '授权用户'),
            key: 'user',
        },
        {
            title: i18n.t('name', '授权角色'),
            key: 'role',
        },
        {
            title: i18n.t('name', '完成'),
            key: 'done',
        },
    ];

    const prevStep = () => {
        setCurrent(current - 1);
    };

    const nextStep = () => {
        if (current === 0) {
            const values = form.getFieldsValue();
            const { authorizeUser = [] } = values;
            if (authorizeUser.length === 0) {
                return message.warning(i18n.t('message', '请至少选择一个授权用户'));
            }
        }
        setCurrent(current + 1);
        return false;
    };

    // eslint-disable-next-line consistent-return
    const [submit, submitLoading] = useSubmitFn(async () => {
        const values = form.getFieldsValue();
        const { authorizeUser = [], authorizeRole = [] } = values;
        if (authorizeRole.length === 0) {
            return message.warning(i18n.t('message', '请至少选择一个授权角色'));
        }
        const logParams = selectRoles.length
            ? selectRoles.map((item) => ({
                  id: item.roleId,
                  data: i18n.t(`@i18n:@role__${item.roleId}`, item.roleName),
                  translationList: flatten(langugelist),
              }))
            : selectUsers.map((item) => ({
                  id: item.roleId,
                  data: roleName,
                  translationList: flatten(langugelist),
              }));

        const params = {
            userId: authorizeUser.map((p: any) => p.userId),
            roleId: authorizeRole.map((p: any) => p.roleId),
            // 从用户详情授权角色，增加记录日志字段
            logParams,
            operationModelCode: 'userRolePermissions',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'create',
        };
        await authorizeRoleToUser(params);
        setWhen(false);
        // 如果是从用户详情或角色详情跳转过来的，则回到上一页
        /***********已经没有从用户详情和角色详情跳转过来了，这个逻辑中台可以不要了，页面结构判断展示也可以不用了，行业层不确认************/
        if (userId || roleId) {
            message.success(i18n.t('message', '保存成功'));
            history.goBack();
        } else if (userJumpId) {
            // 如果从用户详情跳转过来又重新点击角色授权菜单
            message.success(i18n.t('message', '保存成功'));
            history.push(`/user-manage/user-detail?tab=role&userId=${userJumpId}`);
        /***********已经没有从用户详情和角色详情跳转过来了，这个逻辑中台可以不要了，页面结构判断展示也可以不用了，行业层不确认************/
        } else {
            setCurrent(2);
            setIsSuccess(true);
        }
    });

    const goBackPage = () => {
        setWhen(false);
        history.go(-1);
    };

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <div className="role-authorize-page-container">
                    <div
                        className="steps-wrapper"
                        style={{
                            display: `${userId || roleId || userJumpId ? 'none' : 'flex'}`,
                        }}
                    >
                        <Steps current={current}>
                            {steps.map((item) => (
                                <Step title={item.title} key={item.key} />
                            ))}
                        </Steps>
                    </div>
                    <div
                        className={`${
                            userId || roleId || userJumpId
                                ? 'steps-content-role-manager'
                                : 'steps-content'
                        }`}
                    >
                        <Form form={form as any}>
                            <AFormItem name="authorizeUser" hidden={current !== 0 || userId}>
                                <AuthorizeUser
                                    onChange={onChange}
                                    onSelectAppId={(value) => setSelectedApp(value)}
                                    disableRoleList={roleList}
                                />
                            </AFormItem>
                            <AFormItem name="authorizeRole" hidden={current !== 1 || roleId} style={{marginBottom:'0px'}}>
                                <AuthorizeRoleCom
                                    showOperate
                                    onChange={onRoleChange}
                                    selectedAppId={selectedApp}
                                    getRoleColumns={getRoleColumns}
                                    clickAddRole={clickAddRole}
                                />
                            </AFormItem>
                        </Form>
                    </div>
                    <div className='steps-operate'>
                        <StarryAbroadLRLayout>
                            <Button
                                // type="primary"
                                hidden={!userJumpId}
                                onClick={goBackPage}
                            >
                                {i18n.t('action', '返回')}
                            </Button>
                            <Button
                                hidden={current === 0 || isSuccess || userId || roleId || userJumpId}
                                onClick={prevStep}
                            >
                                {i18n.t('action', '上一步')}
                            </Button>
                            <Button
                                type="primary"
                                hidden={current === 1 || isSuccess || userId || roleId}
                                onClick={nextStep}
                            >
                                {i18n.t('action', '下一步')}
                            </Button>
                            <Button
                                type="primary"
                                hidden={current !== 1 && !userId && !roleId}
                                onClick={submit}
                                loading={submitLoading}
                            >
                                {i18n.t('action', '提交')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </div>
                    {isSuccess && (
                        <Result
                            title={i18n.t('message', '授权成功')}
                            extra={
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        setCurrent(0);
                                        setIsSuccess(false);
                                        setWhen(true);
                                        form.resetFields();
                                    }}
                                >
                                    {i18n.t('action', '再次授权')}
                                </Button>
                            }
                        />
                    )}
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(RoleAuthorize);
