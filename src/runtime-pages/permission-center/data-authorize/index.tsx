import { useLockFn } from '@streamax/hooks';
import { Button, Form, message, Modal, Space, Steps } from '@streamax/poppy';
import {i18n, RouterPrompt, StarryAbroadLRLayout} from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard, StarryModal } from '@base-app/runtime-lib';
import { StarryAbroadFormItem as AFormItem } from "@base-app/runtime-lib";
import _, { isNil } from 'lodash';
import { useRef, useState } from 'react';
import { authorizeScopeToUser } from '../../../service/authority';
import AuthorizeUser from '../components/AuthorizeUserCom';
import Result from '../components/Result';
import useDataAuthorizeTabs from '@/hooks/useDataAuthorizeTabs';
import './index.less';

const { Step } = Steps;

const DataAuthorize = () => {
    const [current, setCurrent] = useState<number>(0);
    const [isSuccess, setIsSuccess] = useState<boolean>(false);
    // const [appOptionsList, setAppOptionsList] = useState<any[]>([]);
    const [authorizeAppId, setAuthorizeAppId] = useState<number | undefined>();
    const [when, setWhen] = useState(false);
    const [form] = Form.useForm();
    const { formItems, getDataAuthorizeParams } = useDataAuthorizeTabs({ authorizeAppId });
    const authorizeUserRef = useRef<any>(null);
    // const appId = getAppGlobalData('APP_ID');

    const steps = [
        {
            title: i18n.t('name', '授权用户'),
            key: 'user',
        },
        {
            title: i18n.t('name', '授权范围'),
            key: 'scope',
        },
        {
            title: i18n.t('name', '完成'),
            key: 'done',
        },
    ];

    // useEffect(() => {
    //     if (appId) {
    //         setAuthorizeAppId(Number(appId));
    //     } else {
    //         const params = {
    //             page: 1,
    //             pageSize: 1e8,
    //             states: '1',
    //         };
    //         fetchApplicationPageList(params).then((rs: any) => {
    //             const options = (rs.list || [])
    //                 .map((p: any) => ({
    //                     label: i18n.t(`@i18n:@app__${p.applicationId}`, p.applicationName),
    //                     value: p.applicationId,
    //                 }))
    //                 .filter((item: any) => ![66666].includes(item.value));
    //             if (options.length > 0) {
    //                 setAuthorizeAppId(options[0].value);
    //             }
    //             // setAppOptionsList(options);
    //         });
    //     }
    // }, []);

    const onUserChange = (values: any) => {
        if (values && values.length) {
            setWhen(true);
        } else {
            setWhen(false);
        }
    };

    const prevStep = () => {
        setCurrent(current - 1);
    };

    const nextStep = () => {
        if (current === 0) {
            const values = form.getFieldsValue();
            const { authorizeUser = [] } = values;
            if (authorizeUser.length === 0) {
                return message.warning(i18n.t('message', '请至少选择一个授权用户'));
            }
        }
        setCurrent(current + 1);
        return true;
    };
    const onOk = useLockFn(async (params, modal) => {
        setWhen(false);
        await authorizeScopeToUser(params);
        setCurrent(2);
        setIsSuccess(true);
        modal.destroy();
    });

    const submit = () => {
        const values = form.getFieldsValue();
        const { authorizeUser = [], ...rest } = values;
        const params = {
            userId: authorizeUser.map((p: any) => p.userId),
            appId: authorizeAppId,
            ...getDataAuthorizeParams(rest),
        };
        const modal = StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '提交确认'),
            content: i18n.t('message', '授权数据权限范围会覆盖用户当前数据权限范围，确认保存?'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () => {
                onOk(params, modal);
            },
        });
    };

    /**
     *  切换应用时判断是否存在选择的数据
     * @param value : 选择的应用
     */
    const checkExistSelectedData = (value: number | undefined) => {
        const fieldsValues = form.getFieldsValue();
        const filterAuthorizeUser = _.omit(fieldsValues, ['authorizeUser']);
        const filterValueEmpty = Object.keys(filterAuthorizeUser)
            .filter((key) => !isNil(filterAuthorizeUser[key]))
            .reduce((acc, key) => ({ ...acc, [key]: filterAuthorizeUser[key] }), {});
        const isStore =
            Object.keys(filterValueEmpty).length !== 0
                ? Object.values(filterValueEmpty)
                      .map((item: any) => Object.values(item))
                      .flat(2).length > 0
                : false;
        if (isStore) {
            StarryModal.confirm({
                centered: true,
                title: i18n.t('name', '切换应用确认'),
                content: i18n.t('message', '切换应用会清空当前已选数据，确认是否切换？'),
                onOk() {
                    setAuthorizeAppId(value);
                    authorizeUserRef.current?.clear();
                    authorizeUserRef.current?.setAppId(value);
                    // 切换应用的时候清掉已经选中的数据范围
                    form.setFieldsValue({ authorizeUser: undefined });
                    form.resetFields([
                        'companyScope',
                        'vehicleScope',
                        'driverScope',
                        'deviceScope',
                        'userScope',
                        'channelScope',
                        'lineScope',
                        'stationScope',
                    ]);
                },
            });
        } else {
            setAuthorizeAppId(value);
            authorizeUserRef.current?.setAppId(value);
        }
    };

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
                <StarryCard>
                    <div className="data-authorize-page-container">
                        <div className="steps-wrapper">
                            <Steps current={current}>
                                {steps.map((item) => (
                                    <Step title={item.title} key={item.key} />
                                ))}
                            </Steps>
                        </div>
                        <div className="steps-content">
                            <Form form={form as any}>
                                <AFormItem name="authorizeUser" hidden={current !== 0}>
                                <AuthorizeUser
                                    ref={authorizeUserRef}
                                        onChange={onUserChange}
                                        onSelectAppId={(value) => {
                                            // @ts-ignore
                                            // setAuthorizeAppId(value);
                                            // @ts-ignore
                                            checkExistSelectedData(value);
                                        }}
                                    />
                                </AFormItem>
                                <AFormItem hidden={current !== 1} className="authorize-data-form-item">
                                    {/* <Form.Item
                                    label={i18n.t('message', '授权应用')}
                                    required
                                    colon={false}
                                    hidden={appId as any}
                                >
                                    <Select
                                        showArrow
                                        value={authorizeAppId as any}
                                        options={appOptionsList}
                                        style={{
                                            width: '280px',
                                        }}
                                        onChange={(value: any) => {
                                            checkExistSelectedData(value);
                                        }}
                                    />
                                </Form.Item> */}
                                    {formItems}
                                </AFormItem>
                            </Form>
                        </div>
                        <div className="steps-operate">
                            <StarryAbroadLRLayout
                                className={`${current === 1 || isSuccess ? '' : 'btn-control'}`}
                            >
                                <Button hidden={current === 0 || isSuccess} onClick={prevStep}>
                                    {i18n.t('action', '上一步')}
                                </Button>
                                <Button
                                    type="primary"
                                    hidden={current === 1 || isSuccess}
                                    onClick={nextStep}
                                >
                                    {i18n.t('action', '下一步')}
                                </Button>
                                <Button type="primary" hidden={current !== 1} onClick={submit}>
                                    {i18n.t('action', '提交')}
                                </Button>
                            </StarryAbroadLRLayout>
                        </div>
                        {isSuccess && (
                            <Result
                                title={i18n.t('message', '授权成功')}
                                extra={[
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            setCurrent(0);
                                            setIsSuccess(false);
                                            setWhen(true);
                                            form.resetFields();
                                        }}
                                    >
                                        {i18n.t('action', '再次授权')}
                                    </Button>,
                                ]}
                            />
                        )}
                    </div>
                </StarryCard>
        </StarryBreadcrumb>
    );
};

export default DataAuthorize;
