@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.server-manage-wraper {
    .starry-list-data-container-pagnition-wrapper {
        display: none;
    }
    .sort-info-icon {
        margin-left: -2px;
        color: @starry-text-color-secondary;
        font-size: 16px;
        cursor: pointer;
        &::abroad {
            font-size: 20px;
        }
        &:hover {
            color: @primary-color;
        }
    }
    .server-tree-table-content-wrapper {
        .poppy-tree-checkbox-inner {
            border-radius: 50%;
        }
        .poppy-tree-checkbox-checked .poppy-tree-checkbox-inner {
            background: #ffffff;
        }
        .poppy-tree-checkbox-checked .poppy-tree-checkbox-inner::after {
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background-color: @primary-color;
            border: 0;
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            content: ' ';
        }
        .poppy-tree-checkbox-checked:after {
            border-radius: 50%;
        }
        .poppy-tree .poppy-tree-node-content-wrapper.poppy-tree-node-selected{
            background-color: unset;
        }
    }
    .starry-list-data-container {
        .poppy-tree-treenode-selected {
            background-color: @primary-color-lighter;
        }
        .poppy-tree-node-content-wrapper {
            height: 47px;
        }
        .poppy-tree .poppy-tree-node-content-wrapper:hover {
            background-color: transparent;
        }
        .poppy-tree .poppy-tree-treenode:hover {
            background-color: @starry-bg-color-component-hover;
        }
        .poppy-tree .poppy-tree-treenode-selected:hover {
            background-color: @primary-color-light;
        }
        .poppy-tree .poppy-tree-node-content-wrapper {
            transition: all 0s;
        }
    }
    .server-state {
        .state-icon {
            display: inline-table;
            margin-top: -2px;
            vertical-align: middle;
            // display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 8px;
            background: #bfbfbf;
            border-radius: 6px;
            &.active {
                background: #52c41a;
            }
        }
        .state-text {
            position: relative;
            // top: -1px;
            margin-right: 8px;
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
