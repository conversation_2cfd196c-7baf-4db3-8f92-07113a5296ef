@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.server-tree-table-container {
    border: 1px solid @starry-border-level-1-color;
    // border-bottom: none;
    overflow: auto hidden;
    .server-tree-table-header-wrapper {
        min-width: 1000px;
        padding: 0 30px;
        background: var(--poppy-table-header-bg);
        border-bottom: 1px solid @starry-border-level-1-color;
        .border-split{
            float: right;
            display: block;
            border-right: 1px  solid  @starry-border-level-1-color;
            height:60px;
            margin-right: 3px;
        }
        .poppy-row {
            flex-wrap: nowrap;   
            .poppy-col:first-child{
                padding-left: 25px;
                box-sizing: border-box;
            }
        }
        .poppy-col{
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
    .server-tree-table-content-wrapper {
        min-width: 1000px;
        .tree-title-wraper{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            width: 100%;
            line-height: 48px;
        }
        .selected-col{
            background-color: skyblue;
        }
        .operate-col{
            overflow: unset; 
        }
        .border-split{
            float: right;
            display: block;
            border-right: 1px  solid  @starry-border-level-1-color;
            height:48px;
            margin-right: 15px;
        }
        .type-wraper-flex{
            display: flex;
        }
        .poppy-tree .poppy-tree-treenode-disabled .poppy-tree-node-content-wrapper{
            color: @starry-text-color-disabled;
        }
        .poppy-tree-treenode {
            border-bottom: 1px  solid @starry-border-level-1-color;
            height: 48px;
            padding: 0px 30px 0 24px;
            .poppy-tree-checkbox{
                margin-top: 16px;
            }
            &:last-child{
                border-bottom: none;
            }
            &.filter-node {
                // border-top: 1px solid @starry-border-level-1-color;
                &:first-child {
                    border-top: none;
                }
            }
        }
        .poppy-tree-switcher{
            line-height: 48px;
        }
    }
    .poppy-empty {
        margin: 32px 0;
        color: @starry-text-color-placeholder;
    }
}
.server-tree-table-container::abroad{
    border-radius: 8px;
}
.poppy-tree-treenode:has(.tree-title-row-container){
    background-color: @primary-color-light;
}
