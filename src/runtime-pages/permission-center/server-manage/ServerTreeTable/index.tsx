import React, { useState, useRef, useEffect, useCallback, useMemo, memo } from 'react';
import { Row, Col, Tree, Spin, Empty } from '@streamax/poppy';
import { useObserver } from '../server-add/foldTreeTable/hooks';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import classNames from 'classnames';
import { StarryAbroadOverflowEllipsisContainer } from '@/runtime-lib';

export interface ColumnItem {
    title: string;
    colSize: number;
    dataIndex: string;
    render?: (text: any, record: any) => React.ReactNode;
}

interface FoldTreeTableProps {
    columns: ColumnItem[];
    treeData?: any[];
    checkedKeys?: number[];
    selectedKeys?: number[];
    disabled?: boolean;
    loading?: boolean;
    expandedKeys?: number[];
    onCheck?: (checked: any, e: any) => void;
    onExpand?: (checked: any, e: any) => void;
    onSelect?: (select: any, e: any) => void;
    onclick?: (e: any) => void;
    showBorder?: boolean;
    checkable?: boolean;
    selectable?: boolean;
    treeRef?: any;
    searchKey?: string;
}

const PRESET_SERVER = 1;
const FoldTreeTable: React.FC<FoldTreeTableProps> = (props) => {
    const {
        columns,
        disabled,
        treeData,
        checkedKeys,
        selectedKeys,
        loading,
        showBorder = false,
        treeRef,
        searchKey,
        onclick = (e) => {
            e.stopPropagation();
        },
        ...otherProps
    } = props;
    const [treeHeight, setTreeHeight] = useState(620);
    const wrapperRef = useRef();
    const previousWidthRef = useRef(0);
    // 使用useRef存储DOM元素，避免使用state触发重渲染
    const containerRef = useRef<HTMLDivElement>(null);
    
    // 使用requestAnimationFrame限制更新频率
    const rafRef = useRef<number | null>(null);
    
    const resizeCallback = (element: any) => {
        if (rafRef.current) {
            cancelAnimationFrame(rafRef.current);
        }
        
        rafRef.current = requestAnimationFrame(() => {
            const newWidth = element[0].target.offsetWidth - 60;
            if (containerRef.current) {
                previousWidthRef.current = newWidth;
                // 使用CSS变量设置宽度，而不是通过React状态更新
                containerRef.current.style.setProperty('--wrapper-width', `${newWidth}px`);
            }
        });
    };

    useObserver({
        element: wrapperRef,
        callback: resizeCallback,
    });
    
    // 组件卸载时清理
    useEffect(() => {
        return () => {
            if (rafRef.current) {
                cancelAnimationFrame(rafRef.current);
            }
        };
    }, []);

    // 使用memo优化单元格组件，避免不必要的重渲染
    const TreeNodeCell = memo(({
        col,
        index,
        nodeData,
        onclick
    }: {
        col: ColumnItem;
        index: number;
        nodeData: any;
        onclick: (e: any) => void
    }) => {
        // 使用CSS变量和calc函数，避免在JS中计算导致重渲染
        const colStyle = () => ({
            flex: index === 0 ? 1 : 'none',
            width: index === 0 ? 'auto' : `calc((var(--wrapper-width, 0px) * ${col.colSize / 24}))`,
            overflow: 'hidden' as const,
            whiteSpace: 'nowrap' as const,
            textOverflow: 'ellipsis' as const,
            paddingRight: '30px',
        });

        const spanStyle = () => ({
            width: col.dataIndex === 'resourcegroupDescription'
                ? `calc((var(--wrapper-width, 0px) * ${col.colSize / 24}) - 30px)`
                : '',
        });

        const colClassName = index === 1 ? 'type-wraper-flex' : '';
        const spanClassName = classNames('tree-title-wraper', {
            'operate-col': col.dataIndex === 'operate',
        });

        return (
            <Col
                className={colClassName}
                onClick={onclick}
                key={nodeData.resourcegroupCode + nodeData[col.dataIndex] + index}
                data-key={nodeData.key}
                style={colStyle()}
            >
                {index === 1 && <span className="border-split"></span>}
                <span
                    className={spanClassName}
                    style={spanStyle()}
                    title={''}
                >
                    <OverflowEllipsisContainer>
                    {col.render
                        ? col.render(nodeData[col.dataIndex], nodeData)
                        : nodeData[col.dataIndex]}
                    </OverflowEllipsisContainer>
                </span>
            </Col>
        );
    });
    
    // 使用useCallback包装titleRender函数，避免不必要的重新创建
    const titleRender = useCallback((nodeData: any) => {
        const rowClassName = classNames("", {
            'tree-title-row-container': nodeData.key === searchKey, // 搜索高亮
        });

        return (
            <div className={rowClassName}>
                <Row className="tree-title-row" style={{ cursor: 'default', overflow: 'hidden' }}>
                    {columns.map((col, index) => (
                        <TreeNodeCell
                            key={nodeData.resourcegroupCode + col.dataIndex + index}
                            col={col}
                            index={index}
                            nodeData={nodeData}
                            onclick={onclick}
                        />
                    ))}
                </Row>
            </div>
        );
    }, [columns, searchKey, onclick]);
    
    // 使用memo优化表头单元格组件
    const HeaderCell = memo(({
        col,
        index,
        treeDataLength
    }: {
        col: ColumnItem;
        index: number;
        treeDataLength: number
    }) => {
        const style = () => ({
            paddingLeft: index === 1 ? '10px' : '',
        });

        const borderStyle = () => ({
            height: treeDataLength ? '60px' : '46px',
        });

        return (
            <Col
                span={col.colSize}
                key={col.dataIndex}
                style={style()}
            >
                <StarryAbroadOverflowEllipsisContainer>
                    {col.title}
                </StarryAbroadOverflowEllipsisContainer>
                {index === 0 && (
                    <span
                        style={borderStyle()}
                        className="border-split"
                    ></span>
                )}
            </Col>
        );
    });

    // 使用useMemo缓存表头组件
    const generateHeaderCom = useMemo(() => {
        const treeDataLength = treeData?.length || 0;
        
        return (
            <div className="server-tree-table-header-wrapper">
                <Row>
                    {columns.map((col, index) => (
                        <HeaderCell
                            key={col.dataIndex}
                            col={col}
                            index={index}
                            treeDataLength={treeDataLength}
                        />
                    ))}
                </Row>
            </div>
        );
    }, [columns, treeData?.length]);
    const [topItems,setTopItems] = useState<string[]>([])
    useEffect(()=>{
        const topItems = (treeData || []).map((item: any) => item.resourceId);
        setTopItems(topItems)
    },[treeData])
    // 优化filterTreeNode函数，避免在渲染时重新创建
    const filterTreeNode = useCallback((node: any) => {
        return topItems.findIndex((p: any) => p === node.resourceId) !== -1;
    },[topItems]);

    // 使用useMemo优化内容组件
    const generateContentCom = useMemo(() => {
        return (
            <div className="server-tree-table-content-wrapper" ref={wrapperRef as any}>
                <Spin spinning={loading}>
                    {!treeData?.length ? (
                        <Empty imageStyle={{ height: 55 }} />
                    ) : (
                        <Tree
                            ref={treeRef}
                            blockNode
                            checkable
                            virtual={true}
                            height={treeHeight}
                            checkStrictly
                            selectable={false}
                            treeData={treeData}
                            disabled={disabled}
                            checkedKeys={checkedKeys}
                            selectedKeys={selectedKeys}
                            autoExpandParent={false}
                            defaultExpandAll
                            titleRender={titleRender}
                            filterTreeNode={filterTreeNode}
                            {...otherProps}
                        />
                    )}
                </Spin>
            </div>
        );
    }, [
        treeData,
        loading,
        treeHeight,
        treeRef,
        disabled,
        checkedKeys,
        selectedKeys,
        titleRender,
        otherProps
    ]);

    return (
        <div className="server-tree-table-container" ref={containerRef}>
            {generateHeaderCom}
            {generateContentCom}
        </div>
    );
};

export default FoldTreeTable;
