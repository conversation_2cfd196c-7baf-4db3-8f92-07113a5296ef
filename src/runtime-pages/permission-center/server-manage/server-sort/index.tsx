import {
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
    StarryAbroadLRLayout,
} from '@base-app/runtime-lib';
import IconDrag from '@streamax/poppy-icons/lib/icons/IconDrag';
import { i18n, RouterPrompt } from '@base-app/runtime-lib';
import type { ColumnsType } from 'antd/es/table';
import arrayMove from 'array-move';
import React, { useState, useEffect } from 'react';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
// @ts-ignore
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { Table, Button, Space, message } from '@streamax/poppy';
import { getNewServerList, sortServerList } from '@/service/server';
import './index.less';
import { useHistory } from '@base-app/runtime-lib/core';
import { useSubmitFn } from '@streamax/hooks';
import { OverflowEllipsisContainer } from '@streamax/starry-components';

interface DataType {
    resourcegroupName: string;
    id: number;
    resourceId: number;
    resourcegroupDescription: string;
    sortValue: number;
}
const SortableItem = sortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
    <tr {...props} />
));
const SortableBody = sortableContainer((props: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody {...props} />
));
const DragHandle = sortableHandle(() => (
    <StarryAbroadIcon>
        <IconDrag className="server-drag-icon" />
    </StarryAbroadIcon>
));

const columns: ColumnsType<DataType> = [
    {
        title: i18n.t('name', '服务名称'),
        dataIndex: 'resourcegroupName',
        width: '33.3%',
        ellipsis: true,
        render: (text: any, record: any) => { 
             const info = i18n.t(
                 `@i18n:@resourcegroup__${record.resourcegroupCode}`,
                 record.resourcegroupName ?? '-',
             );
           return <StarryAbroadOverflowEllipsisContainer>{info}</StarryAbroadOverflowEllipsisContainer>;
        }
    },
    {
        title: i18n.t('name', '服务描述'),
        dataIndex: 'resourcegroupDescription',
        width: '33.3%',
        ellipsis: true,
        render: (text) => text || '-',
    },
    {
        title: i18n.t('name', '操作'),
        dataIndex: 'sortValue',
        width: '33.3%',
        className: 'drag-visible',
        render: () => <DragHandle />,
    },
];

const ServerSort: React.FC = () => {
    const [dataSource, setDataSource] = useState<DataType[]>([]);
    const [when, setWhen] = useState<boolean>(true);
    const history = useHistory();
    const getDataList = () => {
        const { parentId, appId } = history.location.query;
        getNewServerList({
            appId: appId,
            page: 1,
            parentId: parentId || -1,
            resourcegroupType: 2, // 只有自定义服务才能排序
            pageSize: 10000,
        }).then((res: any) => {
            const listData = (res.list || [])
                .sort((a: any, b: any) => {
                    return a.sortValue - b.sortValue;
                })
                .map((item: any, index: number) => {
                    item.index = index;
                    return item;
                });
            setDataSource(listData);
        });
    };
    useEffect(() => {
        getDataList();
    }, []);

    const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
        if (oldIndex !== newIndex) {
            setWhen(true);
            const newData = arrayMove(dataSource.slice(), oldIndex, newIndex).filter(
                (el: DataType) => !!el,
            );
            setDataSource(newData);
        }
    };

    const DraggableContainer = (props: SortableContainerProps) => (
        <SortableBody
            useDragHandle
            useWindowAsScrollContainer
            helperClass="server-row-dragging"
            onSortEnd={onSortEnd}
            {...props}
        />
    );

    const DraggableBodyRow = ({ className, style, ...restProps }) => {
        const index = dataSource.findIndex((x) => x.index === restProps['data-row-key']);
        return <SortableItem index={index} {...restProps} />;
    };

    // 保存排序
    const [saveSort, submitLoading] = useSubmitFn(async () => {
        const resourceIds = dataSource.map((item) => {
            return item.id as number;
        });
        if (!resourceIds.length) {
            setWhen(false);
            history.goBack();
            return;
        }
        const { parentId, appId } = history.location.query;
        await sortServerList({
            resourceGroupIdList: resourceIds,
            parentId: parentId || -1,
            appId: appId,
        });
        setWhen(false);
        message.success(i18n.t('message', '操作成功'));
        history.goBack();
    });
    const saveCancle = () => {
        setWhen(false);
        history.goBack();
    };

    return (
        <StarryBreadcrumb>
            <StarryCard>
                <RouterPrompt
                    when={when}
                    message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
                />
                <div className="server-page-sort">
                    <div className="poppy-table-around-bordered">
                        <Table
                            bordered="around"
                            pagination={false}
                            dataSource={dataSource}
                            columns={columns}
                            rowKey="index"
                            components={{
                                body: {
                                    wrapper: DraggableContainer,
                                    row: DraggableBodyRow,
                                },
                            }}
                        />
                    </div>
                    <div className="btn-group">
                        <StarryAbroadLRLayout>
                            <Button onClick={saveCancle}>{i18n.t('action', '取消')}</Button>
                            <Button onClick={saveSort} type="primary" loading={submitLoading}>
                                {i18n.t('action', '保存')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </div>
                </div>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default ServerSort;
