@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.server-resource-wraper {
     display: flex;
     >div {
        width: 100%;
     }
    .poppy-tabs-right {
        display: flex;
        flex-direction: row-reverse; /* 将标签和内容从左侧切换到右侧 */
        .poppy-tabs-nav {
            border-left: 1px solid @starry-border-level-2-color;
            margin-bottom: 0;
        }
    }
    .rsp-drawer-title{
        font-size: 20px;
        font-weight: 700;
        line-height: 28px;
        margin-right: 8px;
        color: @starry-text-color-primary;
    }
    // padding-left: 30px;
    // display: flex;
    // .app-server-left {
    //     margin-top: 20px;
    //     min-height: 600px;
    //     padding-top: 10px;
    //     border-left: 1px solid rgb(236, 236, 236);
    //     // flex: 2;
    //     min-width: 80px;
    //     max-width: 150px;
    //     .item {
    //         height: 30px;
    //         padding-left: 20px;
    //         font-size: 15px;
    //         position: relative;
    //         cursor: pointer;
    //         .active {
    //             position: absolute;
    //             left: -5px;
    //             top: 7px;
    //             display: inline-block;
    //             width: 10px;
    //             height: 10px;
    //             border-radius: 5px;
    //             border: 2px solid @primary-color;
    //         }
    //         .active + span {
    //             color: @primary-color;
    //             // font-size: 15px;
    //         }
    //     }
    // }
    .app-server-left{
        height: 100%;
        .poppy-tabs{
            height: 100%;
        }
    }
    .app-server-right {
        // flex: 10;
        flex:1;
        margin-top: 0;
        .top-operate {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }
    }
    .server-resource-wraper-operate-wrap {
        text-align: right;
    }
    .starry-info-block {
        padding-top: 0;
    }
}
.server-resource-wraper-drawer{
    .poppy-tabs-content-holder{
        display: none;
    }
    .app-server-left{
        height: 100%;
        .poppy-tabs{
            height: 100%;
            .poppy-tabs-nav {
                border-left: 1px solid @starry-border-level-2-color;
                margin-bottom: 0;
            }
        }
    }
}
