import React, { useState, useRef, useEffect } from 'react';
import MenuAuth from '../server-add/menuAuth';
import PageAuth from '../server-add/pageAuth';
import { message, Space } from '@streamax/poppy';
import { Auth, i18n, StarryAbroadFormItem, StarryInfoBlock } from '@base-app/runtime-lib';
import { queryServerPageMenu, updateServerPageMenu } from '@/service/server';
import { getLanguageList } from '@/service/language';

import { useLockFn } from '@streamax/hooks';
import './ServerResourece.less';
import { flatten } from 'lodash';
import { useSystemComponentStyle } from '@base-app/runtime-lib';
import { RspBasicLayout, RspCenterContainer } from '@streamax/responsive-layout';

interface ServerResourceProp {
    appId: number;
    serverId: number;
    resourcegroupType: '1' | '2';
    activeTab: string;
    serverName: string;
    serverCode: string;
}
interface LangagueItemType {
    langType: string;
    translation: string;
}

const ServerResource: React.FC<ServerResourceProp> = (props) => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const {
        appId,
        serverId,
        resourcegroupType = '2',
        serverName,
        serverCode,
    } = props;
    const [menuEdit, setMenuEdit] = useState<boolean>(true);
    const [selectPage, setPage] = useState<any>([]);
    const [selectMenu, setMenu] = useState<any>([]);
    const [langagueList, setLangagueList] = useState<LangagueItemType[]>();
    const [selectedMenu, setSelectedMenu] = useState<Record<string, any>>({});
    const pageRef = useRef<any>(null);
    const menuRef = useRef<any>(null);

    const getSeverPageMenu = useLockFn(async (type?: 'edit') => {
        await queryServerPageMenu({
            resourcegroupIds: serverId,
            appId,
            resourceTypes: '1,2,3',
            page: 1,
            pageSize: 1000000,
        }).then((res) => {
            const selectMenu = res.list
                ?.filter((p: any) => p.resourceType == 1)
                .map((item: any) => item.resourceCode);
            const selectPage = res.list
                ?.filter((p: any) => p.resourceType == 2 || p.resourceType == 3)
                .map((item: any) => item.resourceCode);
            setPage(selectPage);
            setMenu(selectMenu);
            if (type === 'edit') {
                setMenuEdit(!menuEdit);
            }
        });
    });

    useEffect(() => {
        if (serverId) getSeverPageMenu();
    }, [serverId]);

    useEffect(() => {
        getLangagueList();
    }, [serverName]);

    const getLangagueList = async () => {
        const data = await getLanguageList({
            langKey: `@i18n:@resourcegroup__${serverCode}`,
        });
        setLangagueList(flatten(data?.languageList));
    };
    const onMenuSelect = (selectInfo) => {
        if (selectInfo.resourceId) {
            setSelectedMenu(selectInfo);
        } else {
            setSelectedMenu({});
        }
    };
    const editSave = async () => {
        await updateServerPageMenu({
            appId,
            resourcegroupId: serverId,
            resourceCodes: [...menuRef.current?.getCheckedKeys(), ...pageRef.current?.getCheckedKeys()],
            logParams: [
                {
                    data: serverName,
                    translationList: langagueList,
                },
            ],
            operationModelCode: 'serve',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'edit',
        }).then(() => {
            message.success(i18n.t('name', '操作成功'));
            setMenuEdit(true);
        });
    };

    const savaAuth = () => {
        if (menuEdit) return;
        editSave();
        setMenuEdit(false);
    };

    const generateTopOperatorText = () => {
        const resourceCode = '@base:@page:server.manage:detail@action:edit.resource';
        if (
            !Auth.check(resourceCode)
        )
            return '';
        if (menuEdit) {
            return (
                <Auth code={resourceCode}>
                    <a
                        onClick={async () => {
                            await getSeverPageMenu('edit');
                        }}
                    >
                        {i18n.t('name', '编辑')}
                    </a>
                </Auth>
            );
        } else {
            return (
                <Space>
                    <Auth code={resourceCode}>
                        <a
                            onClick={() => {
                                setMenuEdit(true);
                                getSeverPageMenu();
                            }}
                        >
                            {i18n.t('name', '取消')}
                        </a>
                    </Auth>
                    <Auth code={resourceCode}>
                        <a onClick={savaAuth}>{i18n.t('name', '保存')}</a>
                    </Auth>
                </Space>
            );
        }
    };

    return (
        <div className="server-resource-wraper">
            <StarryInfoBlock title={i18n.t('name', '服务资源')} operation={resourcegroupType == '2' && generateTopOperatorText()}>
                <RspCenterContainer>
                    <RspBasicLayout gutter={[24, 10]} layoutType="ratio">
                        <RspBasicLayout.Item
                            ratio={{
                                xs: '100%',
                                sm: '100%',
                                md: '100%',
                                lg: 1,
                                xl: 1,
                                xxl: 1,
                            }}
                        >
                            <div className="server-manage-block">
                                <div>
                                    <StarryAbroadFormItem
                                        name="menuAuth"
                                        style={{
                                            marginBottom: isAbroadStyle
                                                ? 0
                                                : 24,
                                        }}
                                        // hidden={type === 'edit'}
                                        initialValue={'init'}
                                    >
                                        <MenuAuth
                                            selectVal={selectMenu}
                                            resourcegroupType={
                                                resourcegroupType as '1' | '2'
                                            }
                                            ref={menuRef}
                                            appId={appId}
                                            disabled={menuEdit}
                                            onMenuSelect={onMenuSelect}
                                        />
                                    </StarryAbroadFormItem>
                                </div>
                            </div>
                        </RspBasicLayout.Item>
                        <RspBasicLayout.Item
                            ratio={{
                                xs: '100%',
                                sm: '100%',
                                md: '100%',
                                lg: 2,
                                xl: 2,
                                xxl: 2,
                            }}
                        >
                            <div className="server-manage-block">
                                <div title={i18n.t('name', '授权页面')}>
                                    <StarryAbroadFormItem
                                        name="pageAuth"
                                        style={{
                                            marginBottom: isAbroadStyle
                                                ? 0
                                                : 24,
                                        }}
                                        initialValue={'init'}
                                    >
                                        <PageAuth
                                            ref={pageRef}
                                            selectVal={selectPage}
                                            resourcegroupType={
                                                resourcegroupType as '1' | '2'
                                            }
                                            appId={appId}
                                            disabled={menuEdit}
                                            selectedMenuInfo={selectedMenu}
                                        />
                                    </StarryAbroadFormItem>
                                </div>
                            </div>
                        </RspBasicLayout.Item>
                    </RspBasicLayout>
                </RspCenterContainer>
            </StarryInfoBlock>
        </div>
    );
};
export default ServerResource;
