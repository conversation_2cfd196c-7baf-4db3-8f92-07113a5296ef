/*
 * @LastEditTime: 2024-09-26 16:11:39
 */
/* eslint-disable react-hooks/rules-of-hooks */
import { useState, useEffect } from 'react';
import { Tabs, Container } from '@streamax/poppy';
import ServerResource from './ServerResource';
import ServerInfo from './ServerInfo';
import { StarryBreadcrumb, PageCardLayout } from '@base-app/runtime-lib';
import { i18n } from '@base-app/runtime-lib';
import { getServerInfo } from '@/service/server';
import RoleReference from './RoleReference';
import {
    useSystemComponentStyle,
  } from '@base-app/runtime-lib';

const { TabPane } = Tabs;

export default function index(props: any) {
    const { isAbroadStyle } = useSystemComponentStyle();
    const { serverId, appId, serverName,resourceGroupCodes } = props.location.query;
    const [serverInfo, setServerInfo] = useState<any>({});

    useEffect(() => {
        getServerDetail(serverId);
    }, []);
    const getServerDetail = (currentServerId: string) => {
        getServerInfo({
            resourcegroupIds: currentServerId,
            appId,
        }).then((res) => {
            setServerInfo(res[0] || {});
        });
    };

    return (
        <StarryBreadcrumb>
            <PageCardLayout
            >
                <ServerInfo resourceGroupCodes={resourceGroupCodes} serverInfo={serverInfo} serverName={serverName} serverId={serverId} getServerDetail={getServerDetail} />
                
                <Tabs
                    style={{ padding: '0 0 32px' }}
                    destroyInactiveTabPane={true}
                    hiddenLine={isAbroadStyle}
                >
                    <TabPane tab={i18n.t('name', '服务资源')} key="resurece">
                        <Container>
                            <ServerResource
                                resourcegroupType={serverInfo.resourcegroupType}
                                appId={appId}
                                serverId={serverId}
                                activeTab="resource"
                                serverName={serverInfo.resourcegroupName}
                                serverCode={serverInfo.resourcegroupCode}
                            />
                        </Container>
                    </TabPane>
                    <TabPane tab={i18n.t('name', '引用角色')} key="role">
                        <RoleReference resourcegroupId={serverId} appId={appId} />
                    </TabPane>
                </Tabs>
            </PageCardLayout>
        </StarryBreadcrumb>
    );
}
