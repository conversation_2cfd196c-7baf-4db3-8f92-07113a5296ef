import React, { useState, useEffect, useRef } from 'react';
import { Table, Input, Select } from '@streamax/poppy';
import {
    TableColumnSetting,
    ListDataContainer,
} from '@streamax/starry-components';
import { i18n, utils, getAppGlobalData } from '@base-app/runtime-lib';
import { fetchRoleList } from '@/service/server';

// import './index.scoped.less';

interface RoleReferenceProps {
    appId: number;
    resourcegroupId: number;
}

const RoleReference: React.FC<RoleReferenceProps> = (props) => {
    const { appId, resourcegroupId } = props;
    const listDataContainerRef = useRef(null);

    const columns = [
        {
            title: i18n.t('name', '角色编码'),
            dataIndex: 'roleCode',
            key: 'roleCode',
        },
        {
            title: i18n.t('name', '角色名称'),
            dataIndex: 'roleName',
            key: 'roleName',
            render:(text:'string',record:any)=>{
                return i18n.t(`@i18n:@role__${record.id}`, record.roleName)
            }
        },
    ];

    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(columns, {
            disabledKeys: ['action'],
        });

    return (
        <ListDataContainer
            ref={listDataContainerRef}
            getDataSource={async (params) => {
                const search=params.search?.replace(/(^\s*)|(\s*$)/g, '')
                const res = await fetchRoleList({
                    appId,
                    resourcegroupId,
                    ...params,
                    search:search?`${encodeURI(search)} in:resourcegroupName,resourcegroupCode`:null
                });
                return new Promise((resolve) => {
                    resolve({
                        ...res,
                    });
                });
            }}
            listRender={(data) => {
                return (
                    <Table
                        size="middle"
                        columns={tableColumns}
                        dataSource={data}
                        pagination={false}
                        bordered="around"
                    />
                );
            }}
            queryForm={{
                layout: 'horizontal',
                items: [
                    {
                        label: i18n.t('name', '角色'),
                        name: 'search',
                        field: Input,
                        fieldProps: {
                            allowClear: true,
                            maxLength: 50,
                            placeholder: i18n.t(
                                'message',
                                '请输入角色名称或角色编码',
                            ),
                        },
                    },
                ],
            }}
        />
    );
};
export default RoleReference;
