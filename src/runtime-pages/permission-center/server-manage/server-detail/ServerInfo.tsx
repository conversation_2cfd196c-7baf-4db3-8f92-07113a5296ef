/*
 * @LastEditTime: 2024-09-26 16:08:25
 */
import React from 'react';
import { Link, useHistory } from '@base-app/runtime-lib/core';
import { i18n, Auth, getAppGlobalData } from '@base-app/runtime-lib';
import { Descriptions, InfoPanel } from '@streamax/starry-components';
import { Action, StarryModal } from '@base-app/runtime-lib';
import { NO_AUTH_TEXT, RoleType } from '@/utils/constant';
import { Button, message, Space, Tag } from '@streamax/poppy';
import { getLanguageList } from '@/service/language';
import { deleteServer, lockResourceGroupState } from '@/service/server';
import { flatten } from 'lodash';
import { ServerState, ServiceType } from '..';
import './ServerInfo.less';
import { IconRequest } from '@streamax/poppy-icons';
import { useLockFn } from '@streamax/hooks';

interface ServerInfoProp {
    serverInfo: any;
    serverId: string;
    serverName: string;
    resourceGroupCodes: string;
    getServerDetail: (serverId: string) => void;
}
// todo2.15.5 功能管理员不能起停用操作 ， 新增起停用操作资源
export default function ServerInfo(props: ServerInfoProp) {
    const { serverInfo, serverId, getServerDetail, serverName, resourceGroupCodes } = props;
    const history = useHistory();
    const roleDisable = getAppGlobalData('APP_USER_INFO')?.roleType === RoleType.FunctionManager; //功能管理员不能修改服务状态
    const tags = [
        undefined,
        <Tag color="blue">{i18n.t('name', '预设服务')}</Tag>,
        <Tag color="green">{i18n.t('name', '自定义服务')}</Tag>,
    ];
    const {
        resourcegroupName,
        resourcegroupDescription,
        resourcegroupCode,
        applicationName,
        resourcegroupType,
        parentName,
        parentId,
        parentResourcegroupCode,
        id,
        appId,
        state,
    } = serverInfo;
    const deleteOperate = () => {
        StarryModal.confirm({
            title: i18n.t('name', '确认删除？'),
            content: i18n.t('message', '确认删除"{server}"服务？', {
                server: i18n.t(`@i18n:@resourcegroup__${resourcegroupCode}`, resourcegroupName),
            }),
            centered: true,
            onOk: async () => {
                const data = await getLanguageList({
                    langKey: `@i18n:@resourcegroup__${resourcegroupCode}`,
                });
                deleteServer({
                    resourcegroupIds: serverId,
                    appId,
                    logParams: [
                        {
                            data: serverInfo.resourcegroupName,
                            translationList: flatten(data?.languageList),
                        },
                    ],
                }).then(() => {
                    message.success(i18n.t('name', '操作成功'));
                    history.goBack();
                });
            },
        });
    };
    const handleToggleServer = () => {
        const serverName = i18n.t(
            `@i18n:@resourcegroup__${serverInfo.resourcegroupCode}`,
            serverInfo.resourcegroupName ?? '-',
        );
        StarryModal.confirm({
            centered: true,
            title:
                state === ServerState.able
                    ? i18n.t('message', '停用确认')
                    : i18n.t('message', '启用确认'),
            content:
                state === ServerState.able
                    ? i18n.t(
                          'message',
                          '确认停用"{serverName}"服务？停用后会导致所有引用角色无法使用该服务',
                          {
                              serverName,
                          },
                      )
                    : i18n.t('message', '确认启用"{serverName}"服务？', {
                          serverName,
                      }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: (close) => {
                return new Promise(async (resolve, reject) => {
                    try {
                        await changeServerState();
                        close?.();
                        return resolve;
                    } catch (error) {
                        close?.();
                        return resolve;
                    }
                });
            },
        });
    };
    const changeServerState = useLockFn(async () => {
        await lockResourceGroupState({
            appId: serverInfo.appId,
            resourcegroupCodes: resourceGroupCodes,
            state: serverInfo.state ? ServerState.unable : ServerState.able,
        }).then(() => {

            message.success(i18n.t('message', '操作成功'));
            getServerDetail(serverId);
        });
    });
    const generateTitle = () => {
        return (
            <Space wrap>
                {serverInfo?.resourcegroupName ? i18n.t(
                    `@i18n:@resourcegroup__${serverInfo.resourcegroupCode}`,
                    serverInfo.resourcegroupName,
                ) : serverName}
                {tags[serverInfo.resourcegroupType]}
            </Space>
        );
    };
    const editInfo =
        resourcegroupType == ServiceType.CUSTOM_SERVER ? (
            <Action
                code="@base:@page:server.manage:detail@action:edit"
                url="/server-manage/edit/:appId/:serverId/:type"
                fellback={''}
                params={{
                    appId: appId,
                    serverId: id,
                    type: 'edit',
                }}
            >
                {i18n.t('name', '编辑')}
            </Action>
        ) : null;
    return (
        <>
            <InfoPanel
                className="server-info-panel"
                extraRight={
                    <Space>
                        <Auth code="@base:@page:server.manage:detail@action:enable.disenable">
                            {!roleDisable && (
                                <Button onClick={handleToggleServer}>
                                    {state == ServerState.able
                                        ? i18n.t('action', '停用')
                                        : i18n.t('action', '启用')}
                                </Button>
                            )}
                        </Auth>
                        <Auth code="@base:@page:server.manage:detail@action:delete">
                            {serverInfo.state == ServerState.unable &&
                                serverInfo.resourcegroupType != ServiceType.PRESET_SERVER && (
                                    <Button danger onClick={deleteOperate}>
                                        {i18n.t('action', '删除')}
                                    </Button>
                                )}
                        </Auth>
                    </Space>
                }
                showCollapseBtn
                defaultCollapsed={true}
                title={generateTitle()}
                style={{
                    borderBottom: 'unset',
                }}
                operationWrap
            >
                <InfoPanel
                    style={{
                        paddingBottom: 0,
                    }}
                    title={<span className="server-base-title">{i18n.t('name', '基本信息')}</span>}
                    extraRight={editInfo}
                >
                    <Descriptions>
                        <Descriptions.Item label={i18n.t('name', '服务名称')} ellipsis>
                            {i18n.t(
                                `@i18n:@resourcegroup__${resourcegroupCode}`,
                                resourcegroupName,
                            ) || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '服务编码')} ellipsis>
                            {resourcegroupCode || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '上级服务')} ellipsis>
                            {parentId == '-2'
                                ? '-'
                                : (parentId &&
                                      i18n.t(
                                          `@i18n:@resourcegroup__${parentResourcegroupCode}`,
                                          parentName,
                                      )) ||
                                  '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '服务类型')} ellipsis>
                            {resourcegroupType == 1
                                ? i18n.t('name', '预设服务')
                                : i18n.t('name', '自定义服务') || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '状态')} ellipsis>
                            {state == ServerState.able
                                ? i18n.t('name', '启用')
                                : i18n.t('name', '停用')}
                        </Descriptions.Item>
                        <Descriptions.Item label={i18n.t('name', '归属应用')} ellipsis>
                            {i18n.t(`@i18n:@app__${appId}`, applicationName) || '-'}
                        </Descriptions.Item>
                        <Descriptions.Item column={2} label={i18n.t('name', '描述')}>
                            {resourcegroupDescription || '-'}
                        </Descriptions.Item>
                    </Descriptions>
                </InfoPanel>
            </InfoPanel>
        </>
    );
}
