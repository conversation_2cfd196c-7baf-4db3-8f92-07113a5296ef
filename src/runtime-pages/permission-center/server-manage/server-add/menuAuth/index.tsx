import React, { useImperativeHandle, useEffect, useState, useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { Space, Tooltip } from '@streamax/poppy';
import { AiIconRiAppsLine, IconFolder01Line, IconMenu } from '@streamax/poppy-icons';
import FoldTreeTable, { ColumnItem, MenuLevelEnum } from '../foldTreeTable';
import CustomCheckbox from '../customCheckbox';
import { fetchNewResourcePageList } from '../../../../../service/resource';
import './index.less';
import { ListDataContainer, OverflowEllipsisContainer } from '@streamax/starry-components';
import { cloneDeep, isNil } from 'lodash';
import { TYPE_RESOURCE_OPERATE, TYPE_RESOURCE_PAGE } from '../pageAuth';

interface MenuPermissionProps {
    disabled?: boolean;
    appId?: number;
    selectVal?: any[];
    resourcegroupType?: '1' | '2'; //1 预设服务  2 自定义服务
    onMenuSelect?: (data: any) => void;
}

interface RefMenuPermissionProps {
    getCheckedKeys: () => number[];
}
const TYPE_RESOURCE_MENU = 1;
const MenuPermission: React.ForwardRefRenderFunction<
    RefMenuPermissionProps,
    MenuPermissionProps
> = (props, ref) => {
    const {
        disabled,
        appId,
        selectVal,
        onMenuSelect,
    } = props;
    const [menuResourceList, setMenuResourceList] = useState<any[]>([]);
    const [pageResourceList, setPageResourceList] = useState<any[]>([]);
    const [treeData, setTreeData] = useState<any>([]);
    const [loading, setLoading] = useState(false);
    const [checkedKeys, setCheckedKeys] = useState<any>([]);
    const [expandedKeys, setExpandedKeys] = useState<number[]>([]);
    const [formatTreeData, setFormatTreeData] = useState<any[]>([]);
    const [searchValue, setSearchValue] = useState<string>('');
    const [selectedKeys, setSelectedKeys] = useState<number[]>([]);
    const [expandedKeysAfterSearch, setExpandedKeysAfterSearch] = useState<
        number[]
    >([]);
    const [firstMatchedNodeKey, setFirstMatchedNodeKey] = useState(
        null,
    );
    const foldTreeTableRef = useRef();

    const getMenuList = () => {
        setLoading(true);
        fetchNewResourcePageList({
            appId: appId,
            page: 1,
            pageSize: 10000,
            // resourceType: 1,
            // tenantId: resourcegroupType == '1' ? 0 : undefined,
        })
            .then((rs) => {
                setPageResourceList(
                    rs.list.filter(
                        (p: any) =>
                            (p.resourceType === TYPE_RESOURCE_PAGE || p.resourceType === TYPE_RESOURCE_OPERATE) &&
                            p.state === 1,
                    ),
                );
                setMenuResourceList(
                    rs.list.filter((it: any) => it.state === 1 && it.resourceType === TYPE_RESOURCE_MENU),
                );
                setLoading(false);
            })
            .catch((err: any) => {
                setLoading(false);
            });
    };

    useEffect(() => {
        if (appId !== undefined && appId !== null) {
            getMenuList();
            resetState();
        }
    }, [appId]);
    const resetState = () => {
        // 切换应用时要清空展开信息，清空选中信息，搜索条件
        setSelectedKeys([]);
        setExpandedKeys([]);
        setSearchValue('');
        foldTreeTableRef.current?.setSearchValue('');
        setFormatTreeData([]);
    };

    // 入参是resourceCode，checkedKeys应该存resourceId
    useEffect(() => {
        const ids = menuResourceList
            .filter((item: any) =>
                (selectVal || []).includes(item.resourceCode),
            )
            .map(({ resourceId }) => resourceId);
        setCheckedKeys(ids);
    }, [selectVal, menuResourceList]);

    useImperativeHandle(ref, () => ({
        // checkedKeys存的resourceId转成resourceCode
        getCheckedKeys: () => {
            return menuResourceList
                .filter((item: any) => checkedKeys.includes(item.resourceId))
                .map(({ resourceCode }) => resourceCode);
        },
    }));

    const findChildren = (data: any[], id: number): any[] => {
        const children = data.filter((item1: any) => {
            if (item1.parentId === id && item1.resourceId !== item1.parentId) {
                // 把该条数据标记为已push，后续不再遍历
                item1.hasPush = true;
                item1.children = findChildren(data, item1.resourceId);
                if (!item1.children || item1.children.length === 0) {
                    item1.isLeaf = true;
                }
                // 过滤掉不需要进行管理的
                const newList = item1.children || [];
                // 如果父节点为不需要管理但是子节点存在需要管理的，则父节点禁用
                if (item1.managementSwitch === 1 && newList.length > 0) {
                    item1.disableCheckbox = true;
                } else {
                    item1.disableCheckbox = item1.managementSwitch;
                    item1.children = newList;
                }
                item1.disableCheckbox = item1.managementSwitch;
                return true;
            }
            return false;
        });
        children.sort((a: any, b: any) => a.sortValue - b.sortValue);
        return children;
    };
    /**
     * @description: 转化平铺数据为treeData
     * @param {any} data
     * @return {*}
     */    
    const translateDataToTree = (data: any[]): any[] => {
        if (!data) return [];
        data.sort((a: any, b: any) => a.parentId - b.parentId);
        let treeDataList: any[] = [];

        data.forEach((item: any) => {
            item.key = item.resourceId;
            // item.title = item.resourceName;
            item.title = null;
            if (!item.hasPush) {
                const newData = {
                    ...item,
                };
                const children = findChildren(data, item.resourceId);
                if (children.length) {
                    newData.children = children;
                } else {
                    newData.isLeaf = true;
                }
                // 过滤掉不需要进行管理的
                const newList = newData.children || [];
                //如果父节点为不需要管理但是子节点存在需要管理的，则父节点禁用
                if (newData.managementSwitch === 1 && newList.length > 0) {
                    newData.disableCheckbox = true;
                } else {
                    newData.children = newList;
                }
                // 最顶层节点并且不需要权限管理，不展示
                if (
                    !(
                        newData.parentId === null &&
                        newData.managementSwitch === 1 &&
                        (newData.children || []).length === 0
                    )
                ) {
                    treeDataList.push(newData);
                }
            }
        });
        // 解决父级被停用时，所有子级都不会展示
        treeDataList = treeDataList.filter((p: any) => !p.parentId);
        treeDataList.sort((a: any, b: any) => a.sortValue - b.sortValue);
        return treeDataList;
    };
    /**
     * @description: 递归寻找pageCode并返回所有已关联的页面
     * @param {any} dataNode
     * @param {string} pageCode
     * @return {*}
     */    
    const findPageCode = (dataNode: any[], pageCode: string[]) => {
        dataNode.forEach((item: any) => {
            if (item.pageCode) {
                pageCode.push(item.pageCode);
            }
            if (item.children && item.children.length > 0) {
                findPageCode(item.children, pageCode);
            }
        });
    };
    /**
     * @description: 根据已关联的页面code遍历页面树，找出是否存在未关联的页面
     * @param {string} pageCode
     * @return {boolean}
     */    
    const hasUnRelatedPage = (pageCode: string[]) => {
        const pageTreeList = translateDataToTree(cloneDeep(pageResourceList));
        const unRelatedPageList = pageTreeList.filter((item) => {
            return !pageCode?.includes(item.resourceCode);
        });
        if (unRelatedPageList.length) return true;
        return false;
    };
    useEffect(() => {
        let list = translateDataToTree(menuResourceList);
        const pageCode: string[] = [];
        findPageCode(list, pageCode);
        const hasUnRelated = hasUnRelatedPage(pageCode);
        if (!isNil(appId) && hasUnRelated) {
            // 构造一个未关联页面的菜单节点
            const newNode = {
                appId: null,
                appName: null,
                children: [],
                clickResponseType: 1,
                freeType: 1,
                id: -1,
                key: -1,
                level: 1,
                managementSwitch: 0,
                openNewTab: 0,
                pageCode: -1,
                relatedPageCodes: pageCode.join(','), //所有菜单已关联的页面pageCode
                pageId: null,
                parentCode: '',
                parentId: null,
                parentName: null,
                pointId: null,
                resourceCode: '-1',
                resourceId: -1,
                resourceName: i18n.t('name', '未关联菜单页面'),
                resourceType: 1,
                resourceUrl: '',
                sortValue: 1,
                state: 1,
                title: null,
                unRelatedPage: true, //菜单是否有关联页面
                disableCheckbox: true, // 禁止选择
            };
            list = list.concat(newNode);
        }
        setTreeData(list);
    }, [menuResourceList, pageResourceList]);
    const renderMenuIcon = (level: MenuLevelEnum) => {
        if (level === MenuLevelEnum.entry) {
            return <AiIconRiAppsLine />;
        } else if (level === MenuLevelEnum.level1) {
            return <IconFolder01Line />;
        } else if (level === MenuLevelEnum.level2) {
            return <IconMenu />;
        }
    };
    const getResourceMenuName = (record: Record<string, string>) => {
        return i18n.t(
            `@i18n:@menu__${record.resourceCode}`,
            record.resourceName,
        );
    };
    const columns: ColumnItem[] = [
        {
            title: i18n.t('name', '菜单名称'),
            colSize: 12,
            dataIndex: 'resourceName',
            render: (text: any, record: any) => {
                return (
                    <Space className={!!record.children?.length ? '' : 'poppy-title-menu-leaf'}>
                        {renderMenuIcon(record.level)}
                        {record.nameMatch ? (
                            <span>
                                {record.nameBeforeMatch}
                                <span style={{ color: 'red' }}>
                                    {record.nameMatch}
                                </span>
                                {record.nameAfterMatch}
                            </span>
                        ) : (
                            getResourceMenuName(record)
                        )}
                        {!!record.children?.length && (
                            <Tooltip title={i18n.t('name', '仅选择此菜单')}>
                                <CustomCheckbox
                                    disabled={disabled}
                                    onClick={(e) =>
                                        chooseSelf(record.resourceId, e)
                                    }
                                ></CustomCheckbox>
                            </Tooltip>
                        )}
                    </Space>
                );
            },
            onClick: (nodeData) => {
                // 取消选中
                const resourceId = nodeData.key;
                if (selectedKeys.includes(resourceId)) {
                    setSelectedKeys([]);
                    onMenuSelect?.({});
                } else {
                    setSelectedKeys([resourceId]);
                    onMenuSelect?.(nodeData);
                }
            },
        },
    ];

    const parentIds: any = [];
    const checkParent = (key: any) => {
        const page = menuResourceList.find((p: any) => p.resourceId === key);

        if (page) {
            // @ts-ignore
            const parentId = page.parentId;
            if (parentId) {
                parentIds.push(parentId);
                checkParent(parentId);
            }
        }
        return parentIds;
    };

    const chooseSelf = (id: number, e: any) => {
        if (e) {
            const parentIds = checkParent(id);
            setCheckedKeys(checkedKeys.concat([id]).concat(parentIds));
        }
    };

    const handleCheck = ({ checked: checkedKey }, { node, checked }) => {
        let newCheckedKeys = [...checkedKey];
        const checkedAllId: number[] = [];

        const checkChildren = (nodeArr: any) => {
            nodeArr.forEach((element: any) => {
                element.resourceId && checkedAllId.push(element.resourceId);
                if (element.children && element.children.length) {
                    checkChildren(element.children);
                }
            });
        };

        const parentIds = checkParent(node.resourceId);
        checkChildren([node]);
        if (checked) {
            newCheckedKeys = newCheckedKeys
                .concat(checkedAllId)
                .concat(parentIds);
        } else {
            newCheckedKeys = checkedKey.filter(
                (p: any) => checkedAllId.indexOf(p) == -1,
            );
        }
        // @ts-ignore
        setExpandedKeys([...new Set(expandedKeys.concat([node.resourceId]))]);
        setCheckedKeys(Array.from(new Set(newCheckedKeys)));
    };

    const onExpand = (expandedKeysValue: React.Key[]) => {
        setExpandedKeys(expandedKeysValue);
    };

    /**
     * @description: 搜索树菜单节点
     * @param {string} value 搜索关键字
     * @return {*}
     */    
    const handleSearch = (value: string) => {
        setSearchValue(value);
        if (!value.trim()) {
            // 如果搜索值为空，恢复原始树数据
            setFormatTreeData(treeData);
            setExpandedKeysAfterSearch([]);
            setFirstMatchedNodeKey(null);
            return;
        }
        // 存储所有匹配节点的路径
        const expandedKeys: number[] = [];
        // 存储第一个匹配的节点key
        let firstMatchKey: null = null;
        // 递归搜索树节点
        const searchTreeData = (
            data: any[],
            parentPath: number[] = [],
        ): any[] => {
            return data
                .map((node) => {
                    const nodeCopy = { ...node };
                    const resourceName = getResourceMenuName(node);
                    const index = resourceName
                        ?.toLocaleLowerCase()
                        .indexOf(value?.toLocaleLowerCase());
                    // 处理子节点匹配
                    let matchedInChildren: any[] = [];
                    if (nodeCopy.children && nodeCopy.children.length > 0) {
                        matchedInChildren = searchTreeData(nodeCopy.children, [
                            ...parentPath,
                            nodeCopy.resourceId,
                        ]);
                    }

                    // 检查子节点是否有匹配
                    const hasMatchedChildren = matchedInChildren.length > 0;

                    // 如果当前节点匹配或者子节点中有匹配
                    if (index > -1 || hasMatchedChildren) {
                        // 如果当前节点匹配
                        if (index > -1) {
                            nodeCopy.matched = true;
                            // 记录第一个匹配的节点
                            if (firstMatchKey === null) {
                                firstMatchKey = nodeCopy;
                            }

                            // 将当前节点的路径添加到展开的键中
                            expandedKeys.push(
                                ...parentPath,
                                nodeCopy.resourceId,
                            );

                            // 为了在渲染时能够高亮显示匹配的文本，我们添加一个特殊的属性
                            nodeCopy.nameBeforeMatch = resourceName.substring(
                                0,
                                index,
                            );
                            nodeCopy.nameMatch = resourceName.substring(
                                index,
                                index + value.length,
                            );
                            nodeCopy.nameAfterMatch = resourceName.substring(
                                index + value.length,
                            );
                        }

                        // 如果有子节点匹配，保留当前节点
                        if (hasMatchedChildren) {
                            nodeCopy.children = matchedInChildren;
                            // 将当前节点的路径添加到展开的键中
                            expandedKeys.push(
                                ...parentPath,
                                nodeCopy.resourceId,
                            );
                            nodeCopy.hasMatchedChildren = true;
                        }

                        return nodeCopy;
                    }

                    // 如果当前节点和子节点都不匹配，返回null
                    return null;
                })
                .filter(Boolean); // 过滤掉null节点
        };

        // 复制原始树数据并进行搜索
        const originalData = cloneDeep(treeData);
        const filteredData = searchTreeData(originalData);
        // 更新状态
        setFormatTreeData(filteredData);
        setExpandedKeysAfterSearch(Array.from(new Set(expandedKeys)));
        if(firstMatchKey?.resourceId) {
            setSelectedKeys([firstMatchKey.resourceId]);
            onMenuSelect?.(firstMatchKey);
        }
    };
    useEffect(() => {
        // 如果有搜索结果，展开所有匹配的节点路径
        if (expandedKeysAfterSearch.length > 0) {
            setExpandedKeys(expandedKeysAfterSearch);
        }
    }, [expandedKeysAfterSearch, firstMatchedNodeKey]);
    useEffect(() => {
        handleSearch(searchValue);
    }, [JSON.stringify(treeData)]);
    return (
        <div className="menu-permission-com-container">
            <div className="menu-permission-com-container-title">
                {i18n.t('name', '授权菜单')}
            </div>
            <div className="menu-permission-com-container-tree-wrap">
                <FoldTreeTable
                    ref={foldTreeTableRef}
                    disabled={disabled}
                    columns={columns}
                    checkedKeys={checkedKeys}
                    treeData={formatTreeData}
                    expandedKeys={expandedKeys}
                    loading={loading}
                    onCheck={handleCheck}
                    onExpand={onExpand}
                    selectable={true}
                    selectedKeys={selectedKeys}
                    search={{
                        placeholder: i18n.t('message', '输入菜单名称查询'),
                        onSearch: handleSearch,
                    }}
                    treeHeight={488}
                />
            </div>
        </div>
    );
};

const MenuPermissionWrapper = React.forwardRef<
    RefMenuPermissionProps,
    MenuPermissionProps
>(MenuPermission) as (
    props: React.PropsWithChildren<MenuPermissionProps> & {
        ref?: React.Ref<RefMenuPermissionProps>;
    },
) => React.ReactElement;

export default MenuPermissionWrapper;
