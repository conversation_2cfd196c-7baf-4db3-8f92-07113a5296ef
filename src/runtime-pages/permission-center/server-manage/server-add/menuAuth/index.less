//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.menu-permission-com-container {
    .menu-permission-com-container-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .menu-permission-com-container-title::abroad {
        font-size: 16px;
    }
    .menu-permission-com-container-tree-wrap {
        height: 100%;
        border: 1px solid @starry-border-level-2-color;
        overflow: hidden;
        .fold-tree-table-container {
            .fold-tree-table-content-wrapper {
                .poppy-tree-treenode {
                    .poppy-row {
                        flex-wrap: nowrap;
                        overflow-wrap: anywhere;
                    }
                }
            }
        }
    }
    .menu-permission-com-container-tree-wrap::abroad {
        border-radius: @border-radius-12;
    }
    .empty-data-wrap {
        height: 436px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    // .poppy-tree-treenode {
    //     .poppy-tree-title {
    //         .poppy-space {
    //             display: flex;
    //             .poppy-space-item:nth-child(2) {
    //                 flex: 1 1 0%;
    //                 width: 0;
    //             }
    //         }
    //     }
    // }
}
