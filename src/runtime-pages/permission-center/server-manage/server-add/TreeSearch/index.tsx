/*
 * @LastEditTime: 2025-05-30 14:46:20
 */
import React, {
    forwardRef,
    useCallback,
    useImperativeHandle,
    useState,
} from 'react';
import { Input } from '@streamax/poppy';
import './index.less';
import { debounce } from 'lodash';
import { AiIconRiSearchLine } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';

type TreeSearchProps = {
    search?: {
        placeholder?: string;
        onSearch?: (value: string) => void;
    };
}
export type TreeSearchRefProps = {
    setSearchValue: (value: string) => void
}

const TreeSearch = forwardRef<
    TreeSearchRefProps,
    TreeSearchProps
>((props, ref) => {
    const { search } = props;
    const [searchValue, setSearchValue] = useState('');
    // 使用防抖处理搜索，停止输入500毫秒后触发搜索
    const handleSearch = useCallback(
        debounce((e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value?.trim() || '';
            search?.onSearch?.(value);
        }, 500),
        [search],
    );
    useImperativeHandle(ref, () => ({
        setSearchValue,
    }));
    return !!search ? (
        <div className="fold-tree-table-sticky">
            <div className="fold-tree-table-search-wrapper">
                <Input
                    placeholder={
                        search?.placeholder || i18n.t('message', '请输入')
                    }
                    prefix={<AiIconRiSearchLine />}
                    onChange={(e) => {
                        setSearchValue(e.target.value);
                        handleSearch(e);
                    }}
                    allowClear
                    maxLength={50}
                    value={searchValue}
                />
            </div>
        </div>
    ) : null;
});

export default TreeSearch;
