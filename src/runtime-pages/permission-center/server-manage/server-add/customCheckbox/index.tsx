/*
 * @LastEditTime: 2025-05-26 17:16:12
 */
import React, { useState } from 'react';
import { IconAuthority02 } from '@streamax/poppy-icons';
import { StarryAbroadIcon } from '@base-app/runtime-lib';
import './index.less';

interface CustomCheckboxprops {
    onClick?: (e: any) => void;
    disabled?: boolean;
}

const CustomCheckbox: React.FC<CustomCheckboxprops> = (props) => {
    const [checked, setChecked] = useState(false);
    const { onClick = () => {}, disabled = false } = props;
    return (
        <IconAuthority02
            {...props}
            className={
                !checked || disabled ? '' : 'coustom-checkbox-icon-checked'
            }
            onClick={(e) => {
                if (!disabled) {
                    e.stopPropagation();
                    setChecked(!checked);
                    onClick(!checked);
                }
            }}
        />
    );
};

export default CustomCheckbox;
