import React, { useImperativeHandle, useEffect, useState } from 'react';
import { i18n, StarryAbroadIcon } from '@base-app/runtime-lib';
import { Checkbox, Row, Col, Space, Tooltip } from '@streamax/poppy';
import FoldTreeTable, { ColumnItem } from '../foldTreeTable';
import CustomCheckbox from '../customCheckbox';
import { fetchNewResourcePageList } from '../../../../../service/resource';
import { cloneDeep, uniqBy } from 'lodash';
import './index.scoped.less';
import { IconPageLine } from '@streamax/poppy-icons';

interface PagePermissionProps {
    disabled?: boolean;
    appId?: number;
    selectVal?: any[];
    resourcegroupType?: '1' | '2';
    selectedMenuInfo?: Record<string, any>;
}

interface RefPagePermissionProps {
    getCheckedKeys: () => number[];
}

export const TYPE_RESOURCE_PAGE = 2;
export const TYPE_RESOURCE_OPERATE = 3;

const PagePermission: React.ForwardRefRenderFunction<
    RefPagePermissionProps,
    PagePermissionProps
> = (props, ref) => {
    const { disabled, appId, selectVal, selectedMenuInfo, resourcegroupType = '2' } = props;
    // @ts-ignore
    const { resourceId: selectedMenu, relatedPageCodes, unRelatedPage } = selectedMenuInfo;
    const [pageResourceList, setPageResourceList] = useState<any[]>([]);
    const [allResourceList, setAllResourceList] = useState<any[]>([]);
    const [treeData, setTreeData] = useState<any>([]);
    const [loading, setLoading] = useState(false);
    const [checkedKeys, setCheckedKeys] = useState<any>([]);
    const [expandedKeys, setExpandedKeys] = useState<number[]>([]);
    const [allPageCheckedKeys, setAllPageCheckedKeys] = useState<number[]>([]);
    
    const getPageList = () => {
        setLoading(true);
        fetchNewResourcePageList({
            appId: appId,
            page: 1,
            pageSize: 10000,
            resourceTypes: 'sorts'
            // resourceType: 2,
            // tenantId: resourcegroupType == '1' ? 0 : undefined,
        })
            .then((rs) => {
                setPageResourceList(
                    rs.list.filter(
                        (p: any) =>
                            (p.resourceType === TYPE_RESOURCE_PAGE || p.resourceType === TYPE_RESOURCE_OPERATE) &&
                            p.state === 1,
                    ),
                );
                setAllResourceList(rs.list);
                // resourceId
                setLoading(false);
            })
            .catch((err: any) => {
                setLoading(false);
            });
    };

    // 入参是resourceCode，checkedKeys应该存resourceId
    useEffect(() => {
        const ids = pageResourceList.filter((item: any) => (selectVal || []).includes(item.resourceCode))
            .map(({resourceId}) => resourceId);
        setCheckedKeys(ids);
    }, [selectVal, pageResourceList]);

    useEffect(() => {
        if (appId !== undefined && appId !== null) {
            getPageList();
            setExpandedKeys([]);
        }
    }, [appId]);

    useImperativeHandle(ref, () => ({
        // checkedKeys存的resourceId转成resourceCode
        getCheckedKeys: () => {
            return pageResourceList.filter((item: any) => checkedKeys.includes(item.resourceId))
            .map(({resourceCode}) => resourceCode);
        },
    }));

    const findChildren = (data: any[], id: number): any[] => {
        const children = data.filter((item1: any) => {
            if (item1.parentId === id) {
                // 把该条数据标记为已push，后续不再遍历
                item1.hasPush = true;
                item1.children = findChildren(data, item1.resourceId);
                if (!item1.children || item1.children.length === 0) {
                    item1.isLeaf = true;
                }
                // 过滤掉不需要进行管理的
                const newList = (item1.children || []).filter(
                    (p: any) => p.managementSwitch === 0,
                );
                // 如果父节点为不需要管理但是子节点存在需要管理的，则父节点禁用
                if (item1.managementSwitch === 1 && newList.length > 0) {
                    item1.disableCheckbox = true;
                } else {
                    item1.children = newList;
                }

                const operates = (item1.children || []).filter(
                    (fp: any) => fp.resourceType === TYPE_RESOURCE_OPERATE,
                );
                const pages = (item1.children || []).filter(
                    (fp: any) => fp.resourceType === TYPE_RESOURCE_PAGE,
                );
                if (operates.length > 0 || pages.length > 0) {
                    item1.children = [...pages];
                    if (operates.length > 0) {
                        item1.children.splice(0, 0, {
                            type: 'operateList',
                            resourceId: operates[0].resourceId,
                            items: operates,
                            title: i18n.t('name', '操作集合'),
                            checkable: false,
                            key: operates
                                .map((o: any) => o.resourceId)
                                .join('-'),

                            className: 'poppy-tree-operate-list'
                        });
                    }
                }
                return true;
            }
            return false;
        });
        return children;
    };

    const translateDataToTree = (data: any[]): any[] => {
        if (!data) return [];
        data.sort((a: any, b: any) => a.parentId - b.parentId);
        let treeDataList: any[] = [];
        // 移除console.log语句
        data.forEach((item: any) => {
            item.key = item.resourceId;
            // item.title = item.resourceName;
            item.title = null;
            item.disableCheckbox = disabled;
            if (!item.hasPush) {
                let newData = {
                    ...item,
                };
                const children = findChildren(data, item.resourceId);
                if (children.length) {
                    newData.children = children;
                } else {
                    newData.isLeaf = true;
                }
                // 过滤掉不需要进行管理的
                const newList = (newData.children || []).filter(
                    (p: any) => p.managementSwitch === 0,
                );
                // 如果父节点为不需要管理但是子节点存在需要管理的，则父节点禁用
                if (newData.managementSwitch === 1 && newList.length > 0) {
                    newData.disableCheckbox = true;
                } else {
                    newData.children = newList;
                }
                const operates = (newData.children || []).filter(
                    (fp: any) => fp.resourceType === TYPE_RESOURCE_OPERATE,
                );
                
                const pages = (newData.children || []).filter(
                    (fp: any) => fp.resourceType === TYPE_RESOURCE_PAGE,
                );
                if (operates.length > 0 || pages.length > 0) {
                    newData.children = [...pages];
                    if (operates.length > 0) {
                        newData.children.splice(0, 0, {
                            type: 'operateList',
                            resourceId: operates[0].resourceId,
                            items: operates,
                            title: i18n.t('name', '操作集合'),
                            checkable: false,
                            key: operates
                                .map((o: any) => o.resourceId)
                                .join('-'),

                            className: 'poppy-tree-operate-list'
                        });
                    }
                }
                if(newData.resourceType === TYPE_RESOURCE_OPERATE){
                    newData= {
                        type: 'operateList',
                        items: [newData],
                        key: newData.resourceId,
                        checkable: false,
                        resourceId: newData.resourceId
                    };
                }
                // 最顶层节点并且不需要权限管理，不展示
                if (
                    !(
                        newData.parentId === null &&
                        newData.managementSwitch === 1 &&
                        (newData.children || []).length === 0
                    )
                ) {
                    treeDataList.push(newData);
                }
            }
        });
        treeDataList = treeDataList.filter((p: any) => !p.parentId);
        return treeDataList;
    };

    // 当selectedMenu变化时，保存当前勾选状态并恢复目标菜单的勾选状态
    useEffect(() => {
        if (selectedMenu === undefined) {
            // 如果取消选中菜单，恢复所有页面资源的勾选状态
            setCheckedKeys(allPageCheckedKeys);
        } else {
            // 保存当前勾选状态到allPageCheckedKeys
            setAllPageCheckedKeys(checkedKeys);
        }
    }, [selectedMenu]);

    /**
     * 递归查找所有子孙菜单
     * @param menuList 所有资源列表
     * @param parentId 父菜单ID
     * @returns 所有子孙菜单ID列表
     */
    const findAllChildPageMenu = (allResourceList: any[], parentId: number): any[] => {
        // 找出子菜单
        const directChildrenMenu = allResourceList.filter(item => item.parentId === parentId);
        if (directChildrenMenu.length === 0) {
            return allResourceList.filter(item => item.resourceId === parentId && item.pageId);
        }
        // 递归查找每个子菜单的子孙菜单
        const descendantIds = directChildrenMenu.flatMap(child => 
            findAllChildPageMenu(allResourceList, child.resourceId)
        );        
        return [...descendantIds];
    };
 
    /**
     * 查找菜单资源下所有的页面资源和操作资源
     * @param selectedMenu 菜单资源ID
     * @returns 该菜单资源下所有的页面资源和操作资源
     */
    const findAllPagesAndActions = (selectedMenu: number) => {
        // 递归查找所有页面和操作资源
        const findAllResourcesRecursively = (resourceList: any[], parentId: number): any[] => {
            // 找出直接子资源
            const directChildren = resourceList.filter(item => item.parentId === parentId);
            if (directChildren.length === 0) {
                return [];
            }
            // 获取所有子资源
            let allResources: any[] = [];
            // 遍历每个直接子资源
            directChildren.forEach(child => {
                // 添加当前资源
                if (child.resourceType === TYPE_RESOURCE_PAGE || child.resourceType === TYPE_RESOURCE_OPERATE) {
                    allResources.push(child);
                }
                // 递归查找子资源的子资源
                const childResources = findAllResourcesRecursively(resourceList, child.resourceId);
                allResources = [...allResources, ...childResources];
            });
            return allResources;
        };
        
        // 获取选中菜单及其所有子菜单ID
        const menuItems = [...findAllChildPageMenu(allResourceList, selectedMenu)];
        
        // 对于每个菜单ID，查找其下的所有页面和操作资源
        let allPagesAndActions: any[] = [];
        menuItems.forEach(menu => {
            // 查找该菜单下的所有页面和操作资源
            const resources = findAllResourcesRecursively(allResourceList, menu.pageId);
            allPagesAndActions = [...allPagesAndActions, ...resources];
        });
        
        const menuIds = menuItems.map((item) => item.pageId);
        const menuPages = allResourceList.filter((item) => {
            return menuIds.includes(item.resourceId);
        });
        // // 合并所有资源并去重
        return uniqBy([...allPagesAndActions, ...menuPages], 'resourceId');
    };

    // 过滤页面资源，只显示当前选中菜单下的资源
    useEffect(() => {
        // 如果没有选中菜单，显示所有页面资源
        if (!selectedMenu) {
            const list = translateDataToTree(cloneDeep(pageResourceList));
            setTreeData(list);
            return;
        }
        let list = [];
        if (unRelatedPage) {
            // 未关联菜单则过滤出未关联的页面渲染
            const treeList = translateDataToTree(cloneDeep(pageResourceList));
            list = treeList.filter((item) => {
                return !relatedPageCodes?.includes(item.resourceCode);
            });
        } else {
            // 找出选中菜单及其所有子孙菜单的ID
            const filterPageResourceList = findAllPagesAndActions(selectedMenu);
            list = translateDataToTree(cloneDeep(filterPageResourceList));
        }
        setTreeData(list);
    }, [pageResourceList, disabled, selectedMenu]);

    const parentIds: any = [];
    const checkParent = (key: any) => {
        const page = pageResourceList.find((p: any) => p.resourceId === key);
        if (page) {
            // @ts-ignore
            const parentId = page.parentId;
            if (parentId) {
                parentIds.push(parentId);
                checkParent(parentId);
            }
        }
        return parentIds;
    };

    const handleCheck = (info: any, nodeInfo: any) => {
        const { checked: checkedKey } = info;
        const { node, checked } = nodeInfo;
        let newCheckedKeys = [...checkedKey];
        const checkedAllId: number[] = [];
        //递归找出子节点id
        const checkChildren = (nodeArr: any) => {
            nodeArr.forEach((element: any) => {
                if (element.type === 'operateList') {
                    element.items.forEach((p: any) => {
                        checkedAllId.push(p.resourceId);
                    });
                } else {
                    element.resourceId && checkedAllId.push(element.resourceId);
                    if (element.children && element.children.length) {
                        checkChildren(element.children);
                    }
                }
            });
        };

        const parentIds = checkParent(node.resourceId);
        checkChildren([node]);
        if (checked) {
            newCheckedKeys = newCheckedKeys
                .concat(checkedAllId)
                .concat(parentIds);
        } else {
            newCheckedKeys = checkedKey.filter(
                (p: any) => checkedAllId.indexOf(p) === -1,
            );
        }
        // @ts-ignore
        setExpandedKeys([...new Set(expandedKeys.concat([node.resourceId]))]);
        setCheckedKeys(Array.from(new Set(newCheckedKeys)));
    };

    const chooseSelf = (id: number, e: any) => {
        if (e) {
            const parentIds = checkParent(id);
            setCheckedKeys(checkedKeys.concat([id]).concat(parentIds));
        }
    };

    const handleCheckOperate = (e: Event, operate: any, Id: any) => {
        // @ts-ignore
        const { checked } = e.target;
        const { resourceId } = operate;
        let checks;
        if (!checked) {
            checks = checkedKeys.filter((p: any) => p !== resourceId);
        } else {
            const parentIds = checkParent(Id);
            checks = [...checkedKeys, resourceId, ...parentIds];
        }

        setCheckedKeys(Array.from(new Set(checks)));
    };

    const columns: ColumnItem[] = [
        {
            title: i18n.t('name', '页面名称'),
            colSize: 21,
            dataIndex: 'resourceName',
            render: (text: any, record: any) => {
                if (record.type === 'operateList') {
                    // const strOp=(record.items || []).map(opt=>opt.resourceId)
                    return (
                        <Row style={{ marginLeft: '-4px' }}>
                            {(record.items || []).map((op: any) => {
                                return (
                                    <Col
                                        className="operate-item"
                                        key={op.resourceId}
                                        style={{ marginRight: '32px' }}
                                    >
                                        <Checkbox
                                            checked={
                                                checkedKeys.findIndex(
                                                    (p: any) =>
                                                        p === op.resourceId,
                                                ) !== -1
                                            }
                                            disabled={disabled}
                                            onChange={(e: Event) => {
                                                handleCheckOperate(
                                                    e,
                                                    op,
                                                    record.resourceId,
                                                );
                                            }}
                                        />
                                        <span
                                            style={{
                                                marginLeft: '10px',
                                            }}
                                            title={i18n.t(
                                                `@i18n:@operation__${op.resourceCode}`,
                                                op.resourceName,
                                            )}
                                        >
                                            {i18n.t(
                                                `@i18n:@operation__${op.resourceCode}`,
                                                op.resourceName,
                                            )}
                                            
                                        </span>
                                    </Col>
                                );
                            })}
                        </Row>
                    );
                }
                return (
                    <div className="poppy-title-page-name">
                        <Space>
                            <IconPageLine />
                            <Space className="poppy-title-page-name-text">
                                <Space>
                                    {i18n.t(
                                        `@i18n:@page__${record.resourceCode}`,
                                        record.resourceName,
                                    )}
                                    {!!record.children?.length && (
                                        <Tooltip
                                            title={i18n.t(
                                                'name',
                                                '仅选择此页面',
                                            )}
                                        >
                                            <CustomCheckbox
                                                disabled={disabled}
                                                onClick={(e) =>
                                                    chooseSelf(
                                                        record.resourceId,
                                                        e,
                                                    )
                                                }
                                            />
                                        </Tooltip>
                                    )}
                                </Space>
                                {record.resourceType === TYPE_RESOURCE_PAGE && (
                                    <span className="resource-page-code">
                                        {record.resourceCode}
                                    </span>
                                )}
                            </Space>
                        </Space>
                    </div>
                );
            },
        },
    ];
    const onExpand = (expandedKeysValue: React.Key[]) => {
        setExpandedKeys(expandedKeysValue as number[]);
    };

    return (
        <div className='page-permission-com-container'>
            <div className='page-permission-com-container-title'>{i18n.t('name', '授权页面')}</div> 
            <div className="page-permission-com-container-tree-wrap">
                <FoldTreeTable
                    columns={columns}
                    loading={loading}
                    checkedKeys={checkedKeys}
                    treeData={treeData}
                    expandedKeys={expandedKeys}
                    onCheck={handleCheck}
                    onExpand={onExpand}
                    treeHeight={560}
                />
            </div>
        </div> 
    );
};

const PagePermissionWrapper = React.forwardRef<
    RefPagePermissionProps,
    PagePermissionProps
>(PagePermission) as (
        props: React.PropsWithChildren<PagePermissionProps> & {
            ref?: React.Ref<RefPagePermissionProps>;
        },
    ) => React.ReactElement;

export default PagePermissionWrapper;
