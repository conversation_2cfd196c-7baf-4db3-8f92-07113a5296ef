//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.page-permission-com-container {
    .page-permission-com-container-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .page-permission-com-container-title::abroad {
        font-size: 16px;
    }
    .page-permission-com-container-tree-wrap {
        min-height: 482px;
        border: 1px solid @starry-border-level-2-color;
        overflow: hidden;
    }
    .page-permission-com-container-tree-wrap::abroad {
        border-radius: @border-radius-12;
    }
    .resource-page-code {
        color: @starry-text-color-secondary;
        word-break: break-word;
    }
    .fold-tree-table-content-wrapper {
        height: 100%;
    }
    
    .fold-tree-table-container .fold-tree-table-content-wrapper .poppy-tree-treenode .poppy-tree-title .poppy-col.operate-item{
        padding-top: 0px;
    }
    
    .empty-data-wrap::global {
        height: 580px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .poppy-title-page-name {
        .poppy-title-page-name-text {
            flex-wrap: wrap;
        }
    }
}
