import React, { useState, useEffect, useRef } from 'react';
import { Button, Input, Select, Form, Divider, message, TreeSelect } from '@streamax/poppy';
import { useHistory } from '@base-app/runtime-lib/core';
import InternationalInput from '../../../../components/InternationalInput';
import { StarryInfoBlock } from '@base-app/runtime-lib';
import { StarryBreadcrumb, PageCardLayout } from '@base-app/runtime-lib';
import {
    i18n,
    utils,
    getAppGlobalData,
    reLoadLanguage,
    RouterPrompt,
    useSystemComponentStyle,
    StarryAbroadFormItem,
    StarryAbroadLRLayout,
} from '@base-app/runtime-lib';
import PageAuth from './pageAuth';
import MenuAuth from './menuAuth';
import {
    createServer,
    updateServer,
    queryServerInfo,
    getNewServerList,
    queryServerPageMenu,
} from '@/service/server';
import { checkFieldSpace, toTreeByRecursion } from '@/utils/commonFun';
import { useAppList } from '@/hooks';
import AuthTip from '@/components/AuthTip';
import { NO_AUTH_TEXT } from '@/utils/constant';
import { NO_AUTH_NUMBER } from '@/utils/commonFun';
import './index.less';
import {
    RspBasicLayout,
    RspCenterContainer,
    RspFormLayout,
} from '@streamax/responsive-layout';

const { Item } = Form;
const { TextArea } = Input;
const APP_ID = getAppGlobalData('APP_ID');
const Index: React.FC = (props: any) => {
    const { isAbroadStyle } = useSystemComponentStyle();
    const { inSaaS, appList } = useAppList({}, [66666]);
    const history: any = useHistory();
    const { appId, serverId, type = 'add' } = props.match.params;
    const [internationalInfo, setInternationalInfo] = useState<any>([]);
    const [selectedAppId, setSelecedtAppId] = useState<any>(
        !inSaaS ? APP_ID : undefined,
    );
    const pageRef = useRef<any>(null);
    const menuRef = useRef<any>(null);
    const [toggleClik, setToggleClik] = useState(true);
    const [basicInfoform] = Form.useForm();
    const [dataSource, setDataSource] = useState<any>({});
    const [selectOptions, setSelectOptions] = useState<any>([]);
    const [pageSelected, setPage] = useState<any>([]);
    const [menuSelected, setMenu] = useState<any>([]);
    const [disableServerAuth, setDisableServerAuth] = useState(false);
    const [singleColumn, setSingleColumn] = useState<boolean>(false);
    const [when, setWhen] = useState(true);
    const [selectedMenu, setSelectedMenu] = useState<Record<string, any>>({});
    // 记录当前回填服务的上级服务id，用于判断是否有权限查看该数据
    const parentIdRef = useRef<number>(999);
    // 记录是停留在当前页面
    const inPage = useRef<boolean>(true);

    const fetchServerList = () => {
        if (typeof selectedAppId !== 'undefined') {
            getNewServerList({
                appId: selectedAppId,
                page: 1,
                pageSize: 10000,
                state: 1, //只查询已启用的服务
            }).then((res) => {
                // 编辑情况下，判断是否有权限
                if ((!!serverId || serverId == 0) && type !== 'add') {
                    const parentId = parentIdRef.current;
                    // parentId == -2 时说明上级服务无权限，区分真正没有上级服务情况
                    const hasAuth =
                        parentId == Number(NO_AUTH_NUMBER) ? false : true;
                    setDisableServerAuth(!hasAuth);
                    if (!hasAuth) {
                        res.list.push({
                            id: parentId,
                            parentId: null,
                            name: NO_AUTH_TEXT,
                            noAuth: true,
                            resourcegroupType: 2,
                        });
                    }
                    basicInfoform.setFieldsValue({
                        parentId: parentIdRef.current,
                    });
                    if (type === 'copy') {
                        basicInfoform.setFieldsValue({
                            parentId: undefined,
                        });
                    }
                }
                // 判断权限完
                const treeData = toTreeByRecursion(
                    res.list.filter((item: any) => {
                        if (!item.noAuth) {
                            item.name = i18n.t(
                                `@i18n:@resourcegroup__${item.resourcegroupCode}`,
                                item.resourcegroupName,
                            );
                        }
                        return item.resourcegroupType == 2;
                    }), //自定义服务
                    'id',
                    'parentId',
                    null,
                    'children',
                    'treeOption',
                ).map((item: any) => {
                    if (item.children.length) {
                        item.children = item.children.map((i: any) => {
                            if (i.children.length) {
                                i.children = i.children.map((j: any) => {
                                    if (j.children.length) {
                                        j.children = [];
                                    }
                                    return j;
                                });
                            }
                            return i;
                        });
                    }
                    return item;
                });
                setSelectOptions(treeData);
            });
        }
    };

    useEffect(() => {
        return ()=>{
            inPage.current = false;
        };
    }, []);

    useEffect(() => {
        fetchServerList();
        setSelectedMenu({});
    }, [selectedAppId]);

    useEffect(() => {
        if ((!!serverId || serverId == 0) && type !== 'add') {
            //查询服务信息 回填表单
            queryServerInfo({
                appId,
                resourcegroupIds: serverId,
            }).then((res) => {
                const {
                    resourcegroupName,
                    resourcegroupDescription,
                    resourcegroupCode,
                    parentId,
                    appId,
                    id,
                } = res[0];
                parentIdRef.current = parentId;
                setDataSource(res[0]);
                basicInfoform.setFieldsValue({
                    appId,
                    parentId: undefined,
                    resourcegroupName:
                        resourcegroupCode && resourcegroupName
                            ? i18n.t(
                                  `@i18n:@resourcegroup__${resourcegroupCode}`,
                                  resourcegroupName,
                              )
                            : '',
                    resourcegroupCode,
                    resourcegroupDescription,
                });
                setSelecedtAppId(appId);
                if (type === 'copy') {
                    // document.title='新增服务'
                    basicInfoform.setFieldsValue({
                        resourcegroupName: '',
                        resourcegroupCode: '',
                        resourcegroupDescription: '',
                        parentId: undefined,
                    });
                    queryServerPageMenu({
                        resourcegroupIds: serverId,
                        appId,
                        resourceTypes: '1,2,3',
                        page: 1,
                        pageSize: 1000000,
                    }).then((res) => {
                        const selectMenu = res.list
                            ?.filter((p: any) => p.resourceType == 1)
                            .map((item: any) => item.resourceCode);
                        const selectPage = res.list
                            ?.filter(
                                (p: any) =>
                                    p.resourceType == 2 || p.resourceType == 3,
                            )
                            .map((item: any) => item.resourceCode);
                        setPage(selectPage);
                        setMenu(selectMenu);
                    });
                }
                basicInfoform.setFieldsValue({
                    appId,
                });
            });
        }
    }, [serverId]);
    const createServcie = (values: any) => {
        const { resourcegroupCode, resourcegroupDescription, parentId, appId } =
            values;
        let tempAppId = appId;
        if (!inSaaS) {
            tempAppId = APP_ID;
        }
        if (!toggleClik) return;
        setToggleClik(false);
        const { translationList, objectName, langId } = internationalInfo;
        let resourcegroupName;
        // 为copy时,objectName应该为空;参数应该直接从value中获取
        if (type === 'copy') {
            resourcegroupName = values.resourcegroupName;
        } else {
            resourcegroupName =
                objectName ||
                (i18n.exists(`@i18n:@resourcegroup__${langId}`)
                    ? dataSource.resourceName
                    : values.resourcegroupName);
        }
        if (type === 'add' || type === 'copy') {
            const resourceCodes = menuRef.current
                .getCheckedKeys()
                .concat(pageRef.current.getCheckedKeys());

            createServer({
                appId: tempAppId,
                parentId,
                resourcegroupName,
                resourcegroupDescription,
                translationList,
                resourcegroupCode: '#' + resourcegroupCode, //需求，增加#来区分和运营平台的code码
                resourceCodes,
                logParams: [{ data: resourcegroupName }],
            }).then(
                async (res) => {
                    if (res) {
                        setWhen(false);
                        message.success(i18n.t('name', '操作成功'));
                        reLoadLanguage && (await reLoadLanguage(true, true));
                        if (inPage.current){
                            history.goBack();
                        }
                    }
                },
                () => {
                    setToggleClik(true);
                },
            );
        } else {
            updateServer({
                parentId,
                appId: selectedAppId,
                resourcegroupId: serverId,
                resourcegroupName,
                resourcegroupDescription,
                entryId: internationalInfo.langId,
                translationList,
                logParams: [{ data: resourcegroupName }],
            }).then(
                async (res) => {
                    if (res) {
                        setWhen(false);
                        message.success(i18n.t('name', '操作成功'));
                        reLoadLanguage && (await reLoadLanguage(true, true));
                        if (inPage.current){
                            history.goBack();
                        }
                        setToggleClik(true);
                    }
                },
                () => setToggleClik(true),
            );
        }
    };
    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '服务名称不能为空'));
    };
    const handleSingleColumn = (single: boolean) => {
        setSingleColumn(single);
    };

    const onMenuSelect = (selectInfo) => {
        if (selectInfo.resourceId) {
            setSelectedMenu(selectInfo);
        } else {
            setSelectedMenu({});
        }
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <PageCardLayout className="server-manage-add">
                <Form
                    form={basicInfoform}
                    layout="vertical"
                    onFinish={createServcie}
                    scrollToFirstError={{
                        behavior: 'smooth',
                        block: 'center',
                        scrollMode: 'if-needed',
                    }}
                >
                    <StarryInfoBlock title={i18n.t('name', '基本信息')}>
                        <div className="add-server-info">
                            <RspFormLayout
                                layoutType="auto"
                                onSingleColumn={handleSingleColumn}
                            >
                                {inSaaS && (
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '归属应用')}
                                        name="appId"
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <Select
                                            disabled={type === 'edit'}
                                            placeholder={i18n.t(
                                                'message',
                                                '请选择归属应用',
                                            )}
                                            className="form-item"
                                            onChange={(e) => {
                                                setSelecedtAppId(e);
                                                basicInfoform.setFieldsValue({
                                                    parentId: null,
                                                });
                                            }}
                                            options={appList}
                                        />
                                    </StarryAbroadFormItem>
                                )}
                                <StarryAbroadFormItem
                                    style={{
                                        paddingRight: singleColumn ? 38 : 0,
                                    }}
                                    label={i18n.t('name', '服务名称')}
                                    name="resourcegroupName"
                                    rules={[
                                        {
                                            required: true,
                                            validator: checkSpace,
                                        },
                                        {
                                            validator:
                                                utils.validator
                                                    .illegalCharacter,
                                        },
                                    ]}
                                >
                                    <InternationalInput
                                        allowClear
                                        maxLength={50}
                                        className="form-item"
                                        placeholder={i18n.t(
                                            'message',
                                            '请输入服务名称',
                                        )}
                                        internationalType="resourcegroup"
                                        modalType={
                                            serverId && type === 'edit'
                                                ? 'edit'
                                                : 'add'
                                        }
                                        entryKey={dataSource?.resourcegroupName}
                                        entryIdOrCode={
                                            dataSource?.resourcegroupCode
                                        }
                                        onSave={(values) =>
                                            setInternationalInfo(values)
                                        }
                                    />
                                </StarryAbroadFormItem>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '服务编码')}
                                    name="resourcegroupCode"
                                    rules={[
                                        {
                                            required: true,
                                        },
                                        {
                                            min: 1,
                                            type: 'string',
                                        },
                                        {
                                            validator:
                                                type === 'add'
                                                    ? utils.validator
                                                          .numberOrLetter
                                                    : () => Promise.resolve(),
                                        },
                                    ]}
                                >
                                    <Input
                                        addonBefore={
                                            type === 'add' || type === 'copy'
                                                ? '#'
                                                : ''
                                        }
                                        maxLength={20}
                                        className="form-item"
                                        allowClear
                                        disabled={type === 'edit'}
                                        placeholder={i18n.t(
                                            'message',
                                            '请输入服务编码',
                                        )}
                                    />
                                </StarryAbroadFormItem>

                                <StarryAbroadFormItem
                                    label={
                                        <span>
                                            {i18n.t('name', '上级服务')}
                                            <AuthTip show={disableServerAuth} />
                                        </span>
                                    }
                                    name="parentId"
                                >
                                    <TreeSelect
                                        placeholder={i18n.t(
                                            'message',
                                            '请选择上级服务',
                                        )}
                                        treeData={selectOptions}
                                        className="form-item"
                                        disabled={type === 'edit'}
                                        allowClear
                                        fieldNames={{
                                            label: 'name',
                                            value: 'id',
                                        }}
                                    />
                                </StarryAbroadFormItem>
                                <RspFormLayout.SingleRow>
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '描述')}
                                        name="resourcegroupDescription"
                                    >
                                        <TextArea
                                            className="form-textarea"
                                            placeholder={i18n.t(
                                                'message',
                                                '不多于500字',
                                            )}
                                            allowClear
                                            showCount
                                            maxLength={500}
                                        />
                                    </StarryAbroadFormItem>
                                </RspFormLayout.SingleRow>
                            </RspFormLayout>
                        </div>
                    </StarryInfoBlock>
                    {!isAbroadStyle && type !== 'edit' && <Divider />}

                    {type !== 'edit' && (
                        <StarryInfoBlock title={i18n.t('name', '服务资源')}>
                            <RspCenterContainer>
                                <RspBasicLayout
                                    gutter={[24, 10]}
                                    layoutType="ratio"
                                >
                                    <RspBasicLayout.Item
                                        ratio={{
                                            xs: '100%',
                                            sm: '100%',
                                            md: '100%',
                                            lg: 1,
                                            xl: 1,
                                            xxl: 1,
                                        }}
                                    >
                                        <div className="server-manage-block">
                                            <div>
                                                <StarryAbroadFormItem
                                                    name="menuAuth"
                                                    style={{
                                                        marginBottom:
                                                            isAbroadStyle
                                                                ? 0
                                                                : 24,
                                                    }}
                                                    hidden={type === 'edit'}
                                                    initialValue={'init'}
                                                >
                                                    <MenuAuth
                                                        selectVal={menuSelected}
                                                        ref={menuRef}
                                                        appId={selectedAppId}
                                                        onMenuSelect={
                                                            onMenuSelect
                                                        }
                                                    />
                                                </StarryAbroadFormItem>
                                            </div>
                                        </div>
                                    </RspBasicLayout.Item>
                                    <RspBasicLayout.Item
                                        ratio={{
                                            xs: '100%',
                                            sm: '100%',
                                            md: '100%',
                                            lg: 2,
                                            xl: 2,
                                            xxl: 2,
                                        }}
                                    >
                                        <div className="server-manage-block">
                                            <div>
                                                <StarryAbroadFormItem
                                                    name="pageAuth"
                                                    style={{
                                                        marginBottom:
                                                            isAbroadStyle
                                                                ? 0
                                                                : 24,
                                                    }}
                                                    hidden={type === 'edit'}
                                                    initialValue={'init'}
                                                >
                                                    <PageAuth
                                                        ref={pageRef}
                                                        selectVal={pageSelected}
                                                        appId={selectedAppId}
                                                        selectedMenuInfo={
                                                            selectedMenu
                                                        }
                                                    />
                                                </StarryAbroadFormItem>
                                            </div>
                                        </div>
                                    </RspBasicLayout.Item>
                                </RspBasicLayout>
                            </RspCenterContainer>
                        </StarryInfoBlock>
                    )}

                    <Item>
                        <StarryAbroadLRLayout>
                            <Button
                                onClick={() => {
                                    setWhen(false);
                                    history.goBack();
                                }}
                            >
                                {i18n.t('name', '取消')}
                            </Button>
                            <Button htmlType="submit" type="primary">
                                {i18n.t('name', '确定')}
                            </Button>
                        </StarryAbroadLRLayout>
                    </Item>
                </Form>
            </PageCardLayout>
        </StarryBreadcrumb>
    );
};
export default Index;
