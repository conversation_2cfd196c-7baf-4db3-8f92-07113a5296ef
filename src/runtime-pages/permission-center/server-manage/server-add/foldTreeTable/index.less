//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
.fold-tree-table-container {
    // border: 1px solid @starry-border-level-2-color;
    // &::abroad {
    //     border-radius: @border-radius-8;
    // }
    height: 580px;
    overflow: hidden;
    overflow-y: auto;
    .fold-tree-table-header-wrapper {
        height: 46px;
        padding: 0 30px;
        line-height: 46px;
        background: @starry-bg-color-component-degraded;
        border-bottom: 1px solid @starry-border-level-1-color;
    }
    .fold-tree-table {
        height: 100%;
    }
    .fold-tree-table-content-wrapper {
        min-height: 200px;
        overflow-y: auto;
        .poppy-tree .poppy-tree-treenode-disabled .poppy-tree-node-content-wrapper {
            color: @starry-text-color-primary;
            padding: 0;
            width: calc(100% - 96px);
        }
        .poppy-tree .poppy-tree-node-content-wrapper {
            padding: 0;
        }
        .poppy-tree .poppy-tree-node-content-wrapper {
            width: calc(100% - 96px);
        }
        .poppy-tree-treenode {
            display: flex;
            align-items: baseline;
            // border-bottom: 1px  solid rgba(0, 0, 0, 0.09);
            padding: 4px 24px 4px 24px;
            &.filter-node {
                border-top: 1px solid @starry-border-level-1-color;
                &:first-child {
                    border-top: none;
                }
            }
            .poppy-tree-switcher {
                .poppy-tree-switcher-icon {
                    margin-top: 15px;
                }
            }
            .poppy-tree-checkbox {
                position: relative;
                top: 3px;
            }
            /****树选中效果展示***/ 
            .poppy-tree-title {
                display: inline-block;
                width: 100%;
                .poppy-col {
                    padding: 7px 4px;
                    /***tree的tittle中的poppy-col有行内样式，这里加important覆盖***/ 
                    padding-right: 4px!important;
                    .poppy-space {
                        display: flex;
                        align-items: baseline;
                        flex-grow: 0;
                    }
                }
            }
        }
    }
    .poppy-empty {
        margin: 32px 0;
    }
    .fold-tree-table-sticky {
        .fold-tree-table-search-wrapper {
            padding: 24px;
            padding-bottom: 16px;
        }
    }
    .selected-background {
        background-color: @tree-node-selected-bg;
        transition: all .3s;
    }
    .selected-background::abroad {
        border-radius: @border-radius-6;
    }
    
    .poppy-tree-operate-list.poppy-tree-treenode .poppy-tree-title>.poppy-row>.poppy-col {
        padding-top: 0;
        padding-bottom: 0;
    }
}

