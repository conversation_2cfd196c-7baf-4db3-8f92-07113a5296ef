import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { Row, Col, Tree, Spin, Empty } from '@streamax/poppy';
import { useObserver } from './hooks';
import './index.less';
import TreeSearch, { TreeSearchRefProps } from '../TreeSearch';

export interface ColumnItem {
    title: string;
    colSize: number;
    dataIndex: string;
    render?: (text: any, record: any) => React.ReactNode;
}
/**
 * @description: 资源等级(菜单：1，服务入口；2，一级；3，二级。2，页面：1，根页面；2，子页面)
 */
export enum MenuLevelEnum {
    /**
     * 服务入口 entry：1
     */
    entry = 1,
    /**
     * 一级菜单 level1：2
     */
    level1 = 2,
    /**
     * 一级菜单 level2：2
     */
    level2 = 3
}

interface FoldTreeTableProps {
    columns: ColumnItem[];
    treeData?: any[];
    checkedKeys?: number[];
    disabled?: boolean;
    selectable?: boolean;
    loading?: boolean;
    expandedKeys?: number[];
    onCheck?: (checked: any, e: any) => void;
    onExpand?: (checked: any, e: any) => void;
    onSelect?: (checked: any, e: any) => void;
    search?: {
        placeholder?: string;
        onSearch?: (value: string) => void;
    };
    selectedKeys?: number[];
    treeHeight?: number;
}


const FoldTreeTable: React.FC<FoldTreeTableProps> = forwardRef((props, ref) => {
    const {
        columns,
        disabled,
        treeData,
        checkedKeys,
        loading,
        selectable,
        search,
        selectedKeys,
        treeHeight,
        ...otherProps
    } = props;
    const [wrapperWidth, setWrapperWidth] = useState(0);
    const wrapperRef = useRef();
    const treeSearchRef = useRef<TreeSearchRefProps>(null);

    useObserver({
        element: wrapperRef,
        callback: (element: any) => {
            const newWidth = element[0].target.offsetWidth - 60;
            if (newWidth !== wrapperWidth && wrapperRef.current) {
                setWrapperWidth(newWidth);
            }
        },
    });
    const titleRender = (nodeData: any) => {
        return (
            <Row style={{ cursor: selectable ? 'pointer' : 'default' }}>
                {columns.map((col, index) => {
                    return (
                        <Col
                            onClick={(e) => {
                                if(selectable) {
                                    col?.onClick && col?.onClick(nodeData)
                                }else{
                                    e.stopPropagation();
                                }
                            }}
                            key={col.dataIndex}
                            style={{
                                flex:
                                    index === 0
                                        ? 1
                                        : `0 0 ${
                                              (col.colSize / 24) * wrapperWidth
                                          }px`,
                                paddingRight: index === 0 ? '16px' : 0,
                            }}
                            className={selectedKeys?.includes(nodeData.key) && disabled ? 'selected-background' : ''}
                        >
                            {col.render
                                ? col.render(nodeData[col.dataIndex], nodeData)
                                : nodeData[col.dataIndex]}
                        </Col>
                    );
                })}
            </Row>
        );
    };
   
    const generateContentCom = () => {
        const topItems = (treeData || []).map((item: any) => item.resourceId);
        return (
            <div className="fold-tree-table">
                <TreeSearch
                    search={search}
                    ref={treeSearchRef}
                />
                <div
                    className={`fold-tree-table-content-wrapper`}
                    ref={wrapperRef as any}
                >
                    <Spin spinning={loading}>
                        {!treeData?.length ? (
                            <div className="empty-data-wrap">
                                <Empty imageStyle={{ height: 55 }} />
                            </div>
                        ) : (
                            <Tree
                                blockNode
                                checkable
                                checkStrictly
                                selectable={selectable ? true : false}
                                treeData={treeData}
                                disabled={disabled}
                                checkedKeys={checkedKeys}
                                autoExpandParent={false}
                                titleRender={titleRender}
                                height={treeHeight}
                                filterTreeNode={(node: any) => {
                                    return (
                                        topItems.findIndex(
                                            (p: any) => p === node.resourceId,
                                        ) !== -1
                                    );
                                }}
                                selectedKeys={selectedKeys}
                                {...otherProps}
                            />
                        )}
                    </Spin>
                </div>
            </div>
        );
    };
    useImperativeHandle(ref, () => ({
        setSearchValue: treeSearchRef.current?.setSearchValue,
    }));
    return (
        <div className="fold-tree-table-container">
            {generateContentCom()}
        </div>
    );
});

export default FoldTreeTable;
