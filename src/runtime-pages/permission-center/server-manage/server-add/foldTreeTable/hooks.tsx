import { useEffect, useRef } from 'react';
import ResizeObserver from 'resize-observer-polyfill';

export const useObserver = ({
    callback,
    element,
}: {
    callback: (element: any) => void;
    element: any;
}) => {
    const current = element && element.current;
    const observer = useRef(null);

    const observe = () => {
        if (element && element.current && observer.current) {
            // @ts-ignore
            observer.current.observe(element.current);
        }
    };

    useEffect(() => {
        if (observer && observer.current && current) {
            // @ts-ignore
            observer.current.unobserve(current);
        }
        const ResizeObserverOrPolyfill = ResizeObserver;
        // @ts-ignore
        observer.current = new ResizeObserverOrPolyfill(callback);
        observe();

        return () => {
            if (observer && observer.current && element && element.current) {
                // @ts-ignore
                observer.current.unobserve(element.current);
            }
        };
    // 添加正确的依赖项，确保当callback或element变化时重新设置观察者
    }, [current, callback, element]);
};

export const useComponentWillMount = (callback: () => void) => {
    const willMount = useRef(true);
    if (willMount.current) {
        callback();
    }
    willMount.current = false;
};
