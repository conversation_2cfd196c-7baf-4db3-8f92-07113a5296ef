import {
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Space, Button, Select, Modal, message, Tag, Popover, Tooltip, Switch } from '@streamax/poppy';
import { useHistory, Link } from '@base-app/runtime-lib/core';
import { utils } from '@base-app/runtime-lib';
import { ListDataContainer, OverflowEllipsisContainer } from '@streamax/starry-components';
import {
    IconEdit,
    IconDelete,
    IconCopy,
    IconAdd01,
    IconSort,
    IconRequest,
    IconInformationFill,
} from '@streamax/poppy-icons';
import {
    StarryBreadcrumb,
    PageCardLayout,
    Action,
    StarryModal,
} from '@base-app/runtime-lib';
import ServerTreeTable from './ServerTreeTable';
import { i18n, getAppGlobalData, useUrlSearchStore, Auth } from '@base-app/runtime-lib';
import { toTreeByRecursion } from '@/utils/commonFun';
import { getNewServerList, deleteServer, lockResourceGroupState } from '@/service/server';
import { useAppList } from '@/hooks';
import './index.less';
import { getLanguageList } from '@/service/language';
import { flatten } from 'lodash';
import classNames from 'classnames';
import { useLockFn } from '@streamax/hooks';
import { RoleType } from '@/utils/constant';

interface AppServerProps {
    appId: string;
}
type DescribeProps = {
    content: string;
};
type EvenData = {
    event: string;
    node: {
        resourcegroupType: ServiceType;
    };
    selected: boolean;
    selectedNodes: Object[];
};
export enum ServiceType {
    PRESET_SERVER = 1, //预设服务
    CUSTOM_SERVER = 2, //自定义服务
}
export enum ServerState {
    unable = 0, //停用
    able = 1 //启用
}
enum TreeNodeType {
    root = 1, //根节点只有一个
    child = 2, //不是根节点多个
}
type DataRow = {
    resourcegroupType: ServiceType;
    parentId: string;
    sortValue: number;
    children: [];
};
const Describe = (props: DescribeProps) => (
    <Popover content={props.content} placement="right" className="sort-info-icon">
        <IconInformationFill />
    </Popover>
);
const AppServer: React.FC<AppServerProps> = () => {
    const history: any = useHistory();
    const searchStore = useUrlSearchStore();
    const listDataContainerRef = useRef<any>(null);
    const [appId, setAppId] = useState<number | undefined>(undefined);
    const roleDisable = getAppGlobalData('APP_USER_INFO')?.roleType === RoleType.FunctionManager;//功能管理员不能修改服务状态
    const tags = [
        undefined,
        <Tag color="blue">{i18n.t('name', '预设服务')}</Tag>,
        <Tag color="green">{i18n.t('name', '自定义服务')}</Tag>,
    ];
    const { inSaaS, appList } = useAppList({}, [66666]);
    const [selectKeys, setSelectKeys] = useState<number[]>([]);
    const [selectRow, setSelectRow] = useState<DataRow>({
        resourcegroupType: ServiceType.CUSTOM_SERVER,
        parentId: '',
        sortValue: 0,
        children: [],
    });

    useEffect(() => {
        appId !== undefined && listDataContainerRef.current?.loadDataSource();
    }, [appId]);

    const selectApp = (e: number) => {
        setAppId(e);
        searchStore.set({
            appId: e,
        });
    };

    useEffect(() => {
        const queryAppId = searchStore.get().appId;
        if (queryAppId !== undefined && queryAppId !== null) {
            setAppId(Number(queryAppId));
        } else {
            if (inSaaS) {
                setAppId(appList[0]?.value);
            } else {
                setAppId(Number(getAppGlobalData('APP_ID')));
            }
        }
    }, [appList]);

    const collectIds = (treeData: Record<string, any>[], getId = (node: Record<string, any>) => node.resourcegroupCode) => {
        const allIds: string[] = [];
        function traverse(node: Record<string, any>) {
            const id = getId(node);
            allIds.push(id);
            if (Array.isArray(node.children) && node.children.length > 0) {
                node.children.forEach(child => traverse(child));
            }
        }
        treeData.forEach(node => traverse(node));
        return allIds;
    };

    const handleToggleServer = (checked: boolean, row: any) => {
        const resourcegroupCodes = collectIds([row]).join();
        const serverName = i18n.t(
            `@i18n:@resourcegroup__${row.resourcegroupCode}`,
            row.resourcegroupName ?? '-',
        );
        StarryModal.confirm({
            centered: true,
            title: checked
                ? i18n.t('message', '启用确认')
                : i18n.t('message', '停用确认'),
            content: checked
                ? i18n.t('message', '确认启用"{serverName}"服务？', {
                    serverName,
                })
                : i18n.t('message', '确认停用"{serverName}"服务？停用后会导致所有引用角色无法使用该服务', {
                    serverName,
                }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: (close) => {
                return new Promise(async (resolve, reject) => {
                    try {
                        await changeServerState(resourcegroupCodes, row, checked);
                        close?.();
                        return resolve;
                    } catch (error) {
                        close?.();
                        return reject;
                    }
                  });
            },
        });
    };
    const changeServerState = useLockFn(async (resourcegroupCodes, row, checked) => {
        await lockResourceGroupState({
            appId: row.appId,
            resourcegroupCodes: resourcegroupCodes,
            state: checked ? ServerState.able : ServerState.unable,
        }).then(() => {
            message.success(i18n.t('message', '操作成功'));
            listDataContainerRef.current?.loadDataSource();
        });
    });

    const columns = [
        {
            title: i18n.t('name', '服务名称'),
            dataIndex: 'resourcegroupName',
            colSize: 6,
            render: (text: any, record: any) => {
                const info = i18n.t(
                                `@i18n:@resourcegroup__${record.resourcegroupCode}`,
                                record.resourcegroupName ?? '-',
                            );
                const fellback = <StarryAbroadOverflowEllipsisContainer>{info}</StarryAbroadOverflowEllipsisContainer>;
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <Action
                            code="@base:@page:server.manage@action:details"
                            url="/server-manage/detail"
                            fellback={fellback}
                            params={{
                                serverId: record.id,
                                appId: record.appId,
                                serverName: info,
                                resourceGroupCodes: collectIds([record]).join(),
                            }}
                        >
                            {info}
                        </Action>
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '服务类型'),
            dataIndex: 'resourcegroupType',
            colSize: 5,
            render: (text: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {tags[text]}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'state',
            colSize: 4,
            render: (text: any, row: any) => {
                return (
                    <div className="server-state">
                        <span
                            className={classNames('state-icon', {
                                active: text === ServerState.able,
                            })}
                        />
                        <span className="state-text">
                            {text === ServerState.able
                                ? i18n.t('state', '启用')
                                : i18n.t('state', '停用')}
                        </span>
                        {
                            <Auth code="@base:@page:server.manage@action:enable.disenable">
                                <span
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                >
                                    <Switch
                                        size="small"
                                        disabled={roleDisable}
                                        checked={text === ServerState.able}
                                        onChange={(checked: boolean) =>
                                            handleToggleServer(checked, row)
                                        }
                                    />
                                </span>
                            </Auth>
                        }
                    </div>
                );
            },
        },
        {
            title: i18n.t('name', '描述'),
            dataIndex: 'resourcegroupDescription',
            colSize: 5,
            render: (text: any) => (
                <StarryAbroadOverflowEllipsisContainer>
                    {text || '-'}
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            colSize: 4,
            ellipsis: true,
            render: (id: any, record: any) => {
                return (
                    record.resourcegroupType !== ServiceType.PRESET_SERVER && (
                        <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                            <Action
                                code="@base:@page:server.manage@action:edit"
                                url="/server-manage/edit/:appId/:serverId/:type"
                                fellback={''}
                                params={{
                                    appId: record.appId,
                                    serverId: record.id,
                                    type: 'edit',
                                }}
                            >
                                <Tooltip title={i18n.t('action', '编辑')}>
                                    <span>
                                        <StarryAbroadIcon>
                                            <IconEdit />
                                        </StarryAbroadIcon>
                                    </span>
                                </Tooltip>
                            </Action>
                            <Action
                                code="@base:@page:server.manage@action:copy"
                                url="/server-manage/copy/:appId/:serverId/:type"
                                fellback={''}
                                params={{
                                    appId: record.appId,
                                    serverId: record.id,
                                    type: 'copy',
                                }}
                            >
                                <Tooltip title={i18n.t('action', '复制')}>
                                    <span>
                                        <StarryAbroadIcon>
                                            <IconCopy />
                                        </StarryAbroadIcon>
                                    </span>
                                </Tooltip>
                            </Action>
                            {record.state !== ServerState.able && (
                                <Auth code={'@base:@page:server.manage@action:delete'}>
                                    <a onClick={(e) => deleteServerItem(record, e)}>
                                        <Tooltip title={i18n.t('action', '删除')}>
                                        <span>
                                            <StarryAbroadIcon>
                                                <IconDelete />
                                            </StarryAbroadIcon>
                                        </span>
                                        </Tooltip>
                                    </a>
                                </Auth>
                            )}
                        </Space>
                    )
                );
            },
        },
    ];

    const deleteServerItem = (record: any, e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        e.stopPropagation();
        StarryModal.confirm({
            icon: <IconRequest />,
            title: i18n.t('message', '删除确认'),
            content: i18n.t('message', '确认删除"{server}"服务？', {
                server: i18n.t(`@i18n:@resourcegroup__${record.resourcegroupCode}`, record.resourcegroupName),
            }),
            async onOk() {
                const data = await getLanguageList({
                    langKey: `@i18n:@resourcegroup__${record.resourcegroupCode}`,
                });
                deleteServer({
                    resourcegroupIds: record.id,
                    appId,
                    logParams: [
                        {
                            data: record.resourcegroupName,
                            translationList: flatten(data?.languageList),
                        },
                    ],
                }).then(() => {
                    message.success(i18n.t('name', '操作成功'));
                    listDataContainerRef.current?.loadDataSource();
                });
            },
        });
    };

    const addSever = () => {
        history.push({
            pathname: '/server-manage/add/0/0/add',
        });
    };
    const sortSever = () => {
        history.push({
            pathname: '/server-manage/sort',
            query: {
                parentId: selectRow.parentId,
                id: selectKeys[0],
                appId: appId,
            },
        });
    };
    const onSelect = useCallback((selectKey: number[], e: EvenData) => {
        setSelectKeys(selectKey);
        const { node } = e;
        //@ts-ignore
        setSelectRow(node);
    },[]);
    const onclick = useCallback(() => {
        return true;
    },[])
    /**
     * @description:
     * @param {DataRow} data
     * @param {1} flag 是否为根节点1 根节点， 2不是根节点
     * @return {*}
     */
    const sortTreeData = (data: DataRow[], flag: TreeNodeType) => {
        let child = [];
        //是否为根节点
        if (flag === TreeNodeType.root) {
            child = data[0].children;
        } else {
            child = data;
        }
        child.sort(function (a, b) {
            return a.sortValue - b.sortValue;
        });
        child.forEach((item) => {
            if (item.children) {
                //@ts-ignore
                item.children = sortTreeData(item.children, 2);
            }
        });
        if (flag === TreeNodeType.root) {
            //@ts-ignore
            data[0].children = child;
            return data;
        } else {
            return child;
        }
    };
    return (
        <StarryBreadcrumb>
            <PageCardLayout className="server-manage-wraper">
                <ListDataContainer
                    ref={listDataContainerRef}
                    loadDataSourceOnMount={false}
                    pagination={{
                        affix: false,
                    }}
                    getDataSource={async () => {
                        if (appId === null) {
                            return Promise.resolve({ list: [] });
                        }
                        try {
                            const rs = await getNewServerList({
                                appId: appId,
                                page: 1,
                                pageSize: 10000,
                            });
                            //rs.list 转换tree
                            const treeOption = {
                                enable: true, // 是否开启转tree插件数据
                                keyField: 'key', // 标识字段名称
                                valueField: 'value', // 值字段名称
                                titleField: 'title', // 标题字段名称

                                keyFieldBind: 'id', // 标识字段绑定字段名称
                                valueFieldBind: 'id', // 值字段名称绑定字段名称
                                titleFieldBind: 'resourcegroupName', // 标题字段名称绑定字段名称
                            };
                            const list = toTreeByRecursion(
                                rs.list,
                                'id',
                                'parentId',
                                //@ts-ignore
                                null,
                                'children',
                                treeOption,
                            );
                            let resourcegroupType1: DataRow[] = []; //预设服务
                            let resourcegroupType2: DataRow[] = []; //自定义服务
                            list.forEach((item: DataRow) => {
                                if (item.resourcegroupType === ServiceType.PRESET_SERVER) {
                                    resourcegroupType1.push(item);
                                } else {
                                    resourcegroupType2.push(item);
                                }
                            });
                            resourcegroupType1 = sortTreeData(
                                resourcegroupType1,
                                TreeNodeType.child,
                            );
                            resourcegroupType2 = sortTreeData(
                                resourcegroupType2,
                                TreeNodeType.child,
                            );
                            return Promise.resolve({
                                ...rs,
                                list: [...resourcegroupType1, ...resourcegroupType2],
                            });
                        } catch (err) {
                            return Promise.resolve({ list: [] });
                        }
                    }}
                    toolbar={{
                        extraLeft: (
                            <>
                                {
                                    <Auth code={'@base:@page:server.manage@action:add'}>
                                        <Button type="primary" onClick={addSever}>
                                            <IconAdd01 />
                                            {i18n.t('name', '添加')}
                                        </Button>
                                    </Auth>
                                }
                                {Auth.check('@base:@page:server.manage@action:sort') && (
                                    <>
                                        <Auth code={'@base:@page:server.manage@action:sort'}>
                                            <Button
                                                onClick={sortSever}
                                                disabled={
                                                    selectKeys.length === 0 ||
                                                    selectRow?.resourcegroupType ===
                                                    ServiceType.PRESET_SERVER
                                                }
                                            >
                                                <IconSort />
                                                {i18n.t('name', '排序')}
                                            </Button>
                                        </Auth>
                                        <Describe
                                            content={i18n.t('message', '仅支持自定义服务排序')}
                                        />
                                    </>
                                )}
                            </>
                        ),
                        extraRight: inSaaS && (
                            <Select
                                value={appId}
                                onChange={selectApp as any}
                                placeholder={i18n.t('message', '请选择归属应用')}
                                style={{ width: '250px' }}
                                options={appList}
                            />
                        ),
                    }}
                    style={{height: '100%'}}
                    listRender={(data) => {
                        return (
                            <ServerTreeTable
                                columns={columns}
                                treeData={data}
                                loading={false}
                                checkable={false}
                                selectedKeys={selectKeys}
                                selectable={true}
                                onSelect={onSelect}
                                onclick={onclick}
                            />
                        );
                    }}
                />
            </PageCardLayout>
        </StarryBreadcrumb>
    );
};
export default AppServer;
