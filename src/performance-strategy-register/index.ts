import { usePerformanceStrategy } from '../modules/performance';
import { i18n } from '../runtime-lib';
import RealtimeVideoPerformanceRegister from './performanceRealTimeVideo';
import IntercomPerformanceRegister from '../runtime-lib/modules/IntercomAndListen/performance';
import RealtimeVideoPerformanceNewMonitoringRegister from './performanceRealTimeVideoNewMonitoring';
import { MONITORING_VERSION, getMonitoringVersion } from '@/runtime-lib/utils/getMonitoringVersion';

export async function registerStrategy() {
    const monitoringVersion = await getMonitoringVersion();

    usePerformanceStrategy.getState().setStrategy({
        describe: {
            strategyThrottleWaitTime: {
                labelText: () => i18n.t('name', '策略联动执行频率'),
                resolveValueText: (val) => `${val}${i18n.t('message', '秒')}`,
                // 如果需要固定这个策略，可以打开下面的部分
                // fixed: true
            },
            realtimeAlarmShowEvidenceInfo: {
                labelText: () => i18n.t('name', '实时报警是否渲染证据信息'),
                resolveValueText(val) {
                    return val ? i18n.t('name', '是') : i18n.t('name', '否');
                },
            },
            realtimeAlarmPushFrequency: {
                labelText: () => i18n.t('name', '实时报警推送频率'),
                resolveValueText(val) {
                    return i18n.t('name', '{frequency}条/秒', {
                        frequency: val,
                    });
                },
            },
            realtimeAlarmUIUpdateInterval: {
                labelText: () => i18n.t('name', '实时报警界面更新间隔'),
                resolveValueText(val) {
                    return i18n.t('name', '{interval}秒', {
                        interval: val,
                    });
                },
            },
        },
        low: {
            strategyThrottleWaitTime: 30,
            realtimeAlarmShowEvidenceInfo: false,
            realtimeAlarmPushFrequency: 1,
            realtimeAlarmUIUpdateInterval: 5,
        },
        medium: {
            strategyThrottleWaitTime: 20,
            realtimeAlarmShowEvidenceInfo: true,
            realtimeAlarmPushFrequency: 5,
            realtimeAlarmUIUpdateInterval: 3,
        },
        high: {
            strategyThrottleWaitTime: 15,
            realtimeAlarmShowEvidenceInfo: true,
            realtimeAlarmPushFrequency: 5,
            realtimeAlarmUIUpdateInterval: 1,
        },
    });

    // 若租户参数控制不使用新版本监控页，注册对讲模块性能探测
    // 注册对讲模块性能探测
    if (monitoringVersion === MONITORING_VERSION.OLD_MONITORING_VERSION) {
        IntercomPerformanceRegister();
    }

    // 注册实时视频弹窗性能探测
    // 若租户参数控制不使用新版本监控页，注册旧版本实时视频性能探测
    if (monitoringVersion === MONITORING_VERSION.OLD_MONITORING_VERSION) {
        RealtimeVideoPerformanceRegister();
    } else {
        // 若租户参数控制使用新版本监控页，注册新版本实时视频性能探测
        RealtimeVideoPerformanceNewMonitoringRegister();
    }
}
