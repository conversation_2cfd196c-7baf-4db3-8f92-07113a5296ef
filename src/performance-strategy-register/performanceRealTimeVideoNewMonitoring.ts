/*
 * @LastEditTime: 2025-01-14 14:55:41
 * 新版本监控页的实时视频相关性能探测，删除新开页面实时视频
 */
import { usePerformanceStrategy } from '../modules/performance';
import i18n from '@/runtime-lib/i18n';

enum PosterStrategyEnum {
    AUTO = 'AUTO',
    QUALITY = 'QUALITY',
    PERFORMANCE = 'PERFORMANCE',
}

const RealtimeVideoPerformanceRegister = () => {
    usePerformanceStrategy.getState().setStrategy({
        describe: {
            realtimeVideoAndIntercomAtSameTime: {
                labelText: () => i18n.t('name', '可同时进行播放实时视频和对讲'),
                resolveValueText(val) {
                    return val ? i18n.t('name', '是') : i18n.t('name', '否');
                },
                // 如果需要固定这个策略，可以打开下面的部分
                fixed: true,
            },
            stopWithPoster: {
                labelText: () => i18n.t('name', '实时视频结束时展示尾帧'),
                resolveValueText(val) {
                    return val === undefined
                        ? i18n.t('name', '是')
                        : i18n.t('name', '否');
                },
                fixed: false,
            },
            captureStrategy: {
                labelText: () => i18n.t('name', '播放器截图策略'),
                resolveValueText(val) {
                    const map: Record<string, string> = {
                        [PosterStrategyEnum.PERFORMANCE]: i18n.t(
                            'name',
                            '性能优先',
                        ),
                        [PosterStrategyEnum.AUTO]: i18n.t('name', '自动'),
                        [PosterStrategyEnum.QUALITY]: i18n.t(
                            'name',
                            '质量优先',
                        ),
                    };
                    return map[val];
                },
                fixed: false,
            },
        },
        low: {
            realtimeVideoAndIntercomAtSameTime: false,
            stopWithPoster: false,
            captureStrategy: PosterStrategyEnum.PERFORMANCE,
        },
        medium: {
            realtimeVideoAndIntercomAtSameTime: false,
            stopWithPoster: undefined,
            captureStrategy: PosterStrategyEnum.PERFORMANCE,
        },
        high: {
            realtimeVideoAndIntercomAtSameTime: true,
            stopWithPoster: undefined, // starry player需要传undefined才能保留！
            captureStrategy: PosterStrategyEnum.AUTO,
        },
    });
};

export default RealtimeVideoPerformanceRegister;
