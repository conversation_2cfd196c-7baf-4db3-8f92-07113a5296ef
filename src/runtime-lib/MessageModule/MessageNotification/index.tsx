/*
 * @LastEditTime: 2025-02-13 17:31:50
 */
/*
 * @LastEditTime: 2024-03-23 20:28:11
 */
import React, {
    useEffect,
    useState,
    forwardRef,
    useRef,
} from 'react';
import { Badge, NotificationWithRender } from '@streamax/poppy';
import { IconMessageFill } from '@streamax/poppy-icons';
import type { OpenPersonalCenter } from '../../layout/Header/PersonalCenter';
import type { MessageNotificationConfig } from './type';
import { batchUpdateReadStatus, fetchMessageNoticePage } from '../../service';
import useWebsocketMessage from '../../hooks/useWebsocketMessage';
import './index.less';
import { i18n, lifecycle } from '@base-app/runtime-lib';
import { useAsyncEffect, useDebounceFn, useLatest } from '@streamax/hooks';
import { B_MESSAGE_NOTICE } from '../../hooks/useWebsocketMessage/constant';
import { g_emmiter } from '../../emmiter';
import { SYT_NOTICE_EVENT_EMIT } from './constant';
import { getMessageNotificationRender } from '../MessageTemplate';
import { IconClose02 } from '@streamax/poppy-icons';
import { useHistory } from '@base-app/runtime-lib/core';
import { useDataStore } from './hooks/useDataStore';
import { MESSAGE_DRAWER_TOGGLE_EMMITER } from '@/runtime-lib/layout/Header/consants';
import { shallow } from "zustand/shallow";
import { logger } from '@/logger';

// 轮播推送消息类型
const NOTIFICATION_TYPE = 9;

interface MessageNotificationProps {
    openPersonalCenter?: OpenPersonalCenter;
}
type SystemNoticeEventEmitType = MessageNotificationProps & {
    count: number; 
}
export interface MessageNotificationRefType {
    updateUnreadMsgCount: () => void;
}

// 关闭方式
export const CLOSE_TYPE = {
    HAND: 1, // 手动
    AUTO: 2, // 自动
};
// SYT_NOTICE_EVENT_EMIT的emit触发原因：行业层需要on这个数据去展示红点，组件外需要知道是否有未读消息
const MessageNotification = (
    props: MessageNotificationProps,
    ref: React.Ref<MessageNotificationRefType>,
) => {
    const { readyState, subscribe, unsubscribe } = useWebsocketMessage();
    const [notificationUnreadCount, setNotificationUnreadCount] = useState(0);

    const { openPersonalCenter } = props;
    const history = useHistory();

    // const { noticeConfig } = useMessageInfo(notificationMessage, {
    //     handleClickNotice,
    //     openPersonalCenter,
    // });
    const {
        addAlarmMessageQueue,
        alarmMessageQueue,
        removeAlarmMessageQueue,
        hasMessageInStack,
        resetStore,
        isNotificationHidden,
        setHasNewNotificationWhenHidden,
        autoCloseMessageQueue,
        addAutoCloseMessageQueue,
        removeAutoCloseMessageQueue,
    } = useDataStore((state: any) => ({
        addAlarmMessageQueue: state.addAlarmMessageQueue,
        alarmMessageQueue: state.alarmMessageQueue,
        removeAlarmMessageQueue: state.removeAlarmMessageQueue,
        hasMessageInStack: state.hasMessageInStack,
        resetStore: state.resetStore,
        isNotificationHidden: state.isNotificationHidden,
        setHasNewNotificationWhenHidden: state.setHasNewNotificationWhenHidden,
        autoCloseMessageQueue: state.autoCloseMessageQueue,
        removeAutoCloseMessageQueue: state.removeAutoCloseMessageQueue,
        addAutoCloseMessageQueue: state.addAutoCloseMessageQueue,
    }), shallow);
    const hasNewAlarmMessageWhenHidden = useRef(false); //是否有新的报警手动关闭类型的，控制呼吸效果，true一直显示，false则判断自动关闭队列显示隐藏
    const alarmMessageQueueRef = useLatest(alarmMessageQueue);
    const hasMessageInStackRef = useLatest(hasMessageInStack);
    const isNotificationHiddenRef = useLatest(isNotificationHidden);
    const autoCloseMessageQueueRef = useLatest(autoCloseMessageQueue);
    // 添加一个状态来跟踪个人消息中心抽屉是否打开
    const isPersonalCenterMessageOpen = useRef(false);
    /**
     * 判断手动关闭报警信息类型数量，大于5条折叠
     */
    const handleAlarmNotification = (message) => {
        if (
            alarmMessageQueueRef.current.length > 4 ||
            hasMessageInStackRef.current
        ) {
            message.getContainer = () =>
                document.querySelector('.stack-notification-box');
            message.placement = 'topRight';
            message.top = 0;
        }
        message.className = 'alarm-message-notification-modal';
        if (
            alarmMessageQueueRef.current.length > 4 &&
            !hasMessageInStackRef.current
        ) {
            //告警消息提醒框有和其他类型一起创建在非折叠容器中且大于5条时在折叠容器中创建提醒框
            //先关闭非折叠容器再创建折叠容器的提醒框
            alarmMessageQueueRef.current.forEach((item) => {
                NotificationWithRender.close(item.id);
            });
            alarmMessageQueueRef.current.forEach((item) => {
                item.getContainer = () =>
                    document.querySelector('.stack-notification-box');
                item.placement = 'topRight';
                item.top = 0;
                handleNotification(item);
            });
        }

        //超过50条移除最老的一条
        if (alarmMessageQueueRef.current.length > 50) {
            //休眠不执行
            if (!documentVisibilityRef.current) {
                return;
            }
            const { id } = alarmMessageQueueRef.current[0];
            NotificationWithRender.close(id);
            removeAlarmMessageQueue(id);
        }
        handleNotification(message);

        //提醒框创建后再改变数组
        addAlarmMessageQueue(message);
    };

    const documentVisibilityRef = useRef(true);
    useEffect(() => {
        const handleVisibilityChange = () => {
            documentVisibilityRef.current =
                document.visibilityState === 'visible';
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener(
                'visibilitychange',
                handleVisibilityChange,
            );
        };
    }, []);

    useAsyncEffect(async () => {
        if (readyState === WebSocket.OPEN) {
            subscribe(
                B_MESSAGE_NOTICE,
                (data: any) => {
                    // 过滤轮播消息
                    if (data && data.notificationType !== NOTIFICATION_TYPE) {
                        if (data?.modelData?.alarmTypeNameId) {
                            // 处理国际化 todo 临时处理，后续等后端修改
                            data.modelData.alarmTypeName = i18n.t(
                                data?.modelData?.alarmTypeNameId,
                                data?.modelData?.alarmTypeNameId,
                            );
                        }
                        setNotificationUnreadCount(1);
                        g_emmiter.emit(SYT_NOTICE_EVENT_EMIT, {
                            count: 1,
                        });
                        // 是否是需要手动关闭的通知
                        const isManualClose = data.popType === CLOSE_TYPE.HAND;
                        logger.notification.info(
                            `[notification] receive message: ${data}`,
                        );
                        if (isManualClose) {
                            //隐藏时是否有新的报警消息
                            hasNewAlarmMessageWhenHidden.current =
                                isNotificationHiddenRef.current;
                            //告警消息类型
                            handleAlarmNotification(data);
                        } else if (documentVisibilityRef.current) {
                            // 获取类型的通知消息渲染模板
                            handleNotification(data);
                            addAutoCloseMessageQueue(data);
                        }
                    }
                },
                {
                    customSend: {
                        type: 'b.msg_type',
                        values: [B_MESSAGE_NOTICE],
                        // @ts-ignore
                        limitValue: 2, // 限制推送频率，每秒2条通知
                    },
                },
            ).then(async () => {
                // socket连接通知订阅成功
                await lifecycle.sysSocket.executeStep('noticeSubscribeSuccess', async () => {});
            }).catch(async (err) => {
                // socket连接通知订阅失败
                await lifecycle.sysSocket.executeStep('noticeSubscribeFail', async () => {});
            });
        }
    }, [readyState]);

    useEffect(()=>{
        if(!isNotificationHidden) {
            //当显示消息弹窗时hasNewAlarmMessageWhenHidden重置为初始值
            hasNewAlarmMessageWhenHidden.current = false;
        }
    },[isNotificationHidden]);

    const handleNotification = (messageInfo: any) => {
        const {
            notificationType,
            id,
            popType,
            className,
            getContainer,
            placement,
            top,
        } = messageInfo;
        // 是否是需要手动关闭的通知
        const isManualClose = popType === CLOSE_TYPE.HAND;
        // 如果通知弹窗处于隐藏状态并且个人中心消息类型抽屉没有打开，产生新消息设置展开按钮呼吸灯效果
        if (isNotificationHiddenRef.current && !isPersonalCenterMessageOpen.current) {
            setHasNewNotificationWhenHidden(true);
        }

        const props = {
            type: notificationType,
            messageInfo,
            handleClickNotice,
            openPersonalCenter,
        };

        // 从消息模板中获取对应消息类型的渲染组件
        // @ts-ignore
        const NotificationTemplate = getMessageNotificationRender(props);
        NotificationWithRender.open({
            className: className || `message-notification-modal`,
            // @ts-ignore
            maxCount: 99, // 此属性并不会生效，但去掉后会出现ts提示问题，先暂时保留，并设置一个较大的值
            closeIcon: (
                <IconClose02 onClick = {(e) =>{ e.stopPropagation(); onClickDebounce?.(messageInfo)}} />
            ),
            style: {
                zIndex: 1061, // 定义弹出窗的层级，高于header的层级，header的层级1020,高于poppy-popconfirm 1060
            },
            key: id,
            duration: !isManualClose ? 4.5 : 0,
            //挂载到折叠组件
            getContainer:
                getContainer ||
                (() => document.querySelector('.notification-box')),
            placement: placement || 'bottomRight',
            top,
            customRender: (prefixCls: any) => {
                // clone自定义的渲染组件，传入参数
                const result = (
                    <NotificationTemplate
                        messageInfo={messageInfo}
                        handleClickNotice={handleClickNotice}
                        prefixCls={prefixCls}
                        openPersonalCenter={openPersonalCenter}
                        history={history}
                    />
                );
                return result;
            },
            onClose: () => {
                if (!isManualClose) {
                    //自动关闭移除数组
                    logger.notification.info(
                        `[notification] removeAutoCloseMessageQueueId: ${id}, autoCloseMessageQueue: ${autoCloseMessageQueue}`,
                    );
                    removeAutoCloseMessageQueue(id);
                }
                //消息隐藏状态下新产生的自动关闭类型消息被自动关闭时，停止展开按钮呼吸灯效果
                if (
                    isNotificationHiddenRef.current &&
                    !hasNewAlarmMessageWhenHidden.current &&
                    autoCloseMessageQueueRef.current.length === 0
                ) {
                    setHasNewNotificationWhenHidden(false);
                }
            },
        });
    };

    // 监听抽屉打开和关闭事件
    useEffect(() => {
        const handleDrawerToggle = (data: { isOpen: boolean }) => {
            isPersonalCenterMessageOpen.current = data.isOpen;
            if(data.isOpen) setHasNewNotificationWhenHidden(false);
        };
        // 订阅抽屉打开和关闭事件
        g_emmiter.on(MESSAGE_DRAWER_TOGGLE_EMMITER, handleDrawerToggle);
        // 组件卸载时取消订阅
        return () => {
            // 使用事件名称字符串取消订阅，避免类型错误
            g_emmiter.off(MESSAGE_DRAWER_TOGGLE_EMMITER);
        };
    }, []);

    useEffect(() => {
        g_emmiter.on(SYT_NOTICE_EVENT_EMIT, (data: SystemNoticeEventEmitType) => {
            const { count } = data || {};
            setNotificationUnreadCount(count);
        });
        // 第一次进入查询是否有未读消息
        fetchMessageNoticePage({
            page: 1,
            pageSize: 10,
            read: false,
        }).then(({ total }) => {
            setNotificationUnreadCount(total);
            g_emmiter.emit(SYT_NOTICE_EVENT_EMIT, {
                count: total,
                openPersonalCenter,
            });
        });
    }, []);
    // 点击消息通知框
    async function handleClickNotice(messageInfo: MessageNotificationConfig) {
        try {
            //弱网环境关闭延迟，接口请求前执行
            if (messageInfo.popType === CLOSE_TYPE.HAND) {
                removeAlarmMessageQueue(messageInfo.id);
            } else {
                removeAutoCloseMessageQueue(messageInfo.id);
            }
            NotificationWithRender.close(messageInfo.id);
            // 标记为已读
            const data = await batchUpdateReadStatus({
                ids: [messageInfo.id],
            });
            setNotificationUnreadCount(data);
            g_emmiter.emit(SYT_NOTICE_EVENT_EMIT, { count: data });
        } catch (error) {
            //console.log(error);
        }
    }
    const { run: onClickDebounce } = useDebounceFn(handleClickNotice, { wait: 500 });

    useEffect(() => {
        if (alarmMessageQueue.length === 1 && hasMessageInStack) {
            const message = alarmMessageQueue[0];
            message.getContainer = () =>
                document.querySelector('.notification-box');
            message.placement = 'bottomRight';
            //先关闭折叠容器再创建非折叠容器的提醒框
            NotificationWithRender.close(message.id);
            handleNotification(message);
        }
    }, [alarmMessageQueue, hasMessageInStack]);

    return (
        <>
            <Badge size="small" dot={notificationUnreadCount > 0}>
                <span className="notification-notice-icon right-wrapper-opt-icon">
                    <IconMessageFill style={{ fontSize: '20px' }} />
                </span>
            </Badge>
        </>
    );
};

export default forwardRef(MessageNotification);
