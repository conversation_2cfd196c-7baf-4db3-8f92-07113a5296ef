import React, { ReactNode } from 'react';
import {
    AlarmMessage,
    PromptCarousel,
    VideoDownload,
    VehicleStore,
    TenantStore,
    TenantApply,
    TenantValidateTimeNotify,
    LicenseExpire,
    ManualEvidence,
    FileExport,
    TenantActiveResult
} from './MessageItems';
import { IJumpConfig, MessageInfo } from './type';
import { OpenPersonalCenter } from '../../layout/Header/PersonalCenter';

// 消息类型-应用id
type TypeAndAppId = `${number}-${number}`;

export type CustomRegister = {
    [typeAndAppId: TypeAndAppId]: RegisterTemplateCallback;
};
export type TemplateItem = {
    NotificationTemplate: ReactNode;
    MessageItem: ReactNode;
};
export type Template = {
    [typeAndAppId: TypeAndAppId]: TemplateItem;
};
export type DefaultTemplate = {
    [type: number]: TemplateItem;
};

// 通知类型
export const NotificationTypeMap = {
    alarm: 1, // 报警
    manualEvidence: 2, // 手动下载证据
    videoDownload: 3, // 视频剪辑
    fileExport: 4, // 文件导出
    tenantApply: 5, // 申请访问租户
    tenantStore: 6, // 租户空间预警
    vehicleStore: 7, // 车辆空间预警
    tenantValidateTimeNotify: 8, // 租户有效期提醒
    promptCarousel: 9, // 轮播消息
    licenseExpire: 10, // License到期通知
    tenantActiveResult: 12, // License到期通知
};

export type HandleMessageTypeProps = {
    type: number;
    messageInfo: MessageInfo;
    openPersonalCenter?: OpenPersonalCenter;
    handleClickNotice?: (info: MessageInfo) => void;
    pageConfig?: IJumpConfig;
    prefixCls?: string;
};

/**
 * 缓存数据
 */
// 自定义注册消息模板的回调存储
// 注册时缓存，挂载时调用
const customRegister: CustomRegister = {};
// 中台默认消息模板
const defaultTemplate: DefaultTemplate = {
    [NotificationTypeMap.alarm]: AlarmMessage,
    [NotificationTypeMap.manualEvidence]: ManualEvidence,
    [NotificationTypeMap.videoDownload]: VideoDownload,
    [NotificationTypeMap.fileExport]: FileExport,
    [NotificationTypeMap.tenantApply]: TenantApply,
    [NotificationTypeMap.tenantStore]: TenantStore,
    [NotificationTypeMap.vehicleStore]: VehicleStore,
    [NotificationTypeMap.tenantValidateTimeNotify]: TenantValidateTimeNotify,
    [NotificationTypeMap.promptCarousel]: PromptCarousel,
    [NotificationTypeMap.licenseExpire]: LicenseExpire,
    [NotificationTypeMap.tenantActiveResult]: TenantActiveResult,
};

// 行业层自定义消息模板
const customTemplate: Template = {};

export type RegisterTemplateCallback = () => TemplateItem;
/**
 * 注册自定义消息模板
 * @param type 消息类型,参考NotificationTypeMap
 * @param appId 应用id
 * @param cb 注册消息类型模板的回调
 */
export const registerTemplate = (
    type: number,
    appId: number | string,
    cb: RegisterTemplateCallback,
) => {
    // 行业层调用注册消息模板方法，存入缓存，到header渲染时，调用挂载方法，记录到对应状态中
    customRegister[`${type}-${appId}`] = cb;
};

/**
 * 挂载自定义消息模板
 */
export const mountCustomTemplate = () => {
    // header渲染时，调用挂载方法，将行业层调用registerTemplate方法注册的消息类型回调函数执行，将执行结果记录到对应状态中
    const keys = Object.keys(customRegister);
    keys.forEach((key) => {
        const cb = customRegister[key];
        if (!cb) return;
        const template = cb();
        customTemplate[key] = template;
        // 挂载后，清空缓存
        customRegister[key] = null;
    });
    // 增加自定义模板的日志打印，方便排查
    // console.log('==========【customTemplate】========', keys);
};

/**
 * 根据消息类型获取消息列表项渲染组件
 * @param type 消息类型
 * @returns ReactNode 对应消息类型的渲染组件
 */
export const getMessageItem: ReactNode = (props: HandleMessageTypeProps) => {
    // 调用消息类型调度函数，返回对应消息类型的渲染组件
    const { messageItem } = handelMessageType(props);
    return messageItem;
};

/**
 * 根据消息类型获取notification渲染组件
 * @param props 处理消息类型需要的参数
 * @returns ReactNode 对应消息类型的notification渲染组件
 */
export const getMessageNotificationRender: ReactNode = (
    props: HandleMessageTypeProps,
) => {
    // 调用消息类型调度函数，返回对应消息类型的notification渲染组件
    const { notificationTemplate } = handelMessageType(props);
    return notificationTemplate;
};

/**
 * 根据消息类型获取缓存状态中的数据
 * @param type 消息类型
 */
const handelMessageType = (props: HandleMessageTypeProps) => {
    // 根据消息类型，先获取行业层自定义的消息类型的数据
    // 若行业层没有对该消息类型做自定义，则获取中台默认的消息类型的数据
    const { type, messageInfo } = props;
    const { modelData } = messageInfo;
    const typeAndAppId = `${type}-${modelData?.appId}`;
    const target = customTemplate[typeAndAppId];
    const notificationTemplate =
        target?.NotificationTemplate ||
        defaultTemplate[type].NotificationTemplate;
    const messageItem =
        target?.MessageItem || defaultTemplate[type].MessageItem;
    return {
        notificationTemplate,
        messageItem,
    };
};

// 导出utils工具函数
export * as MessageUtils from './utils';
// 导出默认消息列表项组件
export { default as MessageDefaultMessageItem } from './DefaultMessageItem';
// 导出默认notification渲染组件
export { default as MessageDefaultNotificationRender } from './DefaultNotificationRender';
// 导出公共组件
export { default as MessageBtnDetail } from './components/BtnDetail';
