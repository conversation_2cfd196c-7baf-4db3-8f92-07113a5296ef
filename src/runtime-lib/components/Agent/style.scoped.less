@import '../../styles/themeStyle.less';
@import '~@streamax/poppy-themes/starry/index.less';
@import  '~@streamax/poppy-themes/starry/abroad.less';

.floatingWrapper {
  position: fixed;
  right: 4px;
  bottom: 64px;
  z-index: 1999;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  transition: all 0.3s @ease-in-out;
  background-image: url(../../../assets/icons/ai_agent_logo.svg);
  background-repeat: no-repeat;
  background-size: 56px 56px;
  box-shadow: 0 2px 5px rgba(87, 111, 247, 0.4);

  &:hover {
    cursor: pointer;
    transform: scale(1.1);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: scale(1.2) rotate(0deg);
  }
  20% {
    transform: scale(1.2) rotate(-5deg);
  }
  40% {
    transform: scale(1.2) rotate(5deg);
  }
  60% {
    transform: scale(1.2) rotate(-3deg);
  }
  80% {
    transform: scale(1.2) rotate(3deg);
  }
}

.chatModal {
  display: flex;
  flex-direction: column;
  position: fixed;
  right: 16px;
  bottom: 16px;
  border-radius: 16px;
  z-index: 2000;
  width: 41.67%; /* 800px / 1920px ≈ 41.67% */
  max-width: 800px;
  height: 680px;
  max-height: 95%;
  background: @starry-bg-color-component-elevated;
  box-shadow: 0 0 40px -8px @starry-drawer-box-shadow-color;
  animation: popIn 0.3s @ease-out forwards;
  transform-origin: bottom right;
  
  @media screen and (max-width: 1600px) {
    width: 45%;
  }
  
  @media screen and (max-width: 1366px) {
    width: 50%;
  }
  
  @media screen and (max-width: 1199px) {
    width: 60%;
  }
  
  @media screen and (max-width: 992px) {
    width: 70%;
  }
  
  @media screen and (max-width: 768px) {
    width: 85%;
    right: 8px;
    bottom: 8px;
  }
}

.chatHeader {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 12px 16px;
  border-radius: 16px 16px 0 0;
  background: @grey-900;
  border-bottom: 1px solid @starry-border-level-2-color;

  img {
    width: 32px;
    height: 32px;
  }

  .ai-title {
    margin-left: 8px;
    color: @starry-text-color-inverse;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }

  .header-icon {
    margin-left: auto;
    color: @starry-text-color-inverse;
    font-size: 16px;
    transform: scale(1) rotate(0deg);
    transition: transform 0.5s ease-in-out;
    
    &:hover {
      transform: scale(1.2) rotate(180deg);
    }
  }
}

.chatContainer {
  position: relative;
  height: 572px;
  background: @starry-bg-color-component-elevated;
  overflow-y: hidden;

  .init-title {
    margin: 16px 0 24px;
    color: @starry-text-color-primary;
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
  }

  .init-content {
    color: @starry-text-color-primary;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }

  .message-list {
    display: flex;
    flex-direction: column;
    height: 486px;
    padding: 0 20px;
    overflow-y: auto;
  }

  .inputArea {
    position: absolute;
    display: flex;
    align-items: center;
    bottom: 0;
    width: 100%;
    height: 86px;
    background: @starry-bg-color-component-elevated;
    padding: 16px;
    textarea {
      margin-left: 12px;
      padding: 16px 40px 16px 16px;
      border-radius: 8px;
    }
    .after-icon {
      position: absolute;
      font-size: 20px;
      right: 32px;
      bottom: 31px;
      color: @starry-text-color-primary;
      cursor: pointer;
      &:hover {
        animation: shake 0.5s ease-in-out forwards;
      }
    }
    .disabled {
      color: @starry-text-color-disabled;
      cursor: not-allowed;
      &:hover {
        animation: none;
      }
    }
  }
}

.chatFooter {
  margin-top: auto;
  padding: 16px;
  height: 52px;
  border-top: 1px solid @starry-border-level-1-color;
  border-radius: 0 0 16px 16px;
  color: @starry-text-color-secondary;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}