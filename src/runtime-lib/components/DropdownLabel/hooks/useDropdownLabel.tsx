import { useSystemComponentStyle } from "@base-app/runtime-lib";

import { useState } from "react";

import { AbroadDropDown } from "../AbroadDropDown";
import { DefaultDropDown } from "../DefaultDropDown";
import { AddonBeforeAbroadDropDown } from "../AddonBeforeAbroadDropDown";

import { OverflowEllipsisContainer } from "@streamax/starry-components";
import { utils } from "@base-app/runtime-lib";
import type { DropDownProps } from "@streamax/poppy/lib/dropdown";



/**

 *<AUTHOR>

 *@date 2025/04/27 19:17:28

 *@description: add a description

 **/
// 下拉组件的目录接口
export interface IMenuOption {
    // 下拉项显示的文本
    label: string;
    // 下拉项对应的key
    value: string;
    // 国内风格时生效
    placeholder?: string;
}

interface IUseDropdownLabelProps {
    menuOptions: IMenuOption[];
    // 切换下拉选项时触发的回调
    onSelect?: (key: string) => void;
    // 默认选择的下拉选项
    defaultKey?: string;
    dropdownConfig?: Partial<DropDownProps>; // 所有属性变为可选;

}


interface IUseDropdownLabelResult {
    defaultDropDownMenu: React.ReactNode | null;
    selectKey: string;
    updateSelectKey: (key: string) => void;
    fieldProps: Record<string, any>
}

export const useDropdownLabel: (props: IUseDropdownLabelProps) =>
        IUseDropdownLabelResult = (props: IUseDropdownLabelProps) => {
    const { menuOptions, defaultKey, onSelect, dropdownConfig } = props;
    const { isAbroadStyle } = useSystemComponentStyle();
    const [selectKey, setSelectKey] = useState( defaultKey || menuOptions[0]?.value );
    // 获取新label交互开关
    const showNewLabelStyle = utils.tenantParams.newLabelStyleSwitchParam.isEnable();

    const innerOnSelect = ( key: string )=>{
        setSelectKey(key);
        onSelect?.(key);
    };

    const getDropdownMenuDom = ()=>{
        if (!menuOptions || !menuOptions.length) return null;
        // 只有一个的时候没必要展示下拉框
        if (menuOptions.length <= 1) {
            return (
                <OverflowEllipsisContainer>
                    {menuOptions.find(i=>i.value === selectKey)?.label || '-'}
                </OverflowEllipsisContainer>
            );
        }
        if (isAbroadStyle){
            return (
                <AbroadDropDown
                    dropdownConfig={dropdownConfig}
                    selectKey={selectKey}
                    onMenuSelect={innerOnSelect}
                    menuOptions={menuOptions}
                />
            );
        } else {
            return (
                <DefaultDropDown
                    dropdownConfig={dropdownConfig}
                    selectKey={selectKey}
                    onMenuSelect={innerOnSelect}
                    menuOptions={menuOptions}
                />);
        }
    };

    const getAddonBeforeMenuDom = ()=>{
        if (!menuOptions || !menuOptions.length) return null;
        if (isAbroadStyle && !showNewLabelStyle && menuOptions.length > 1){
            return (
                <AddonBeforeAbroadDropDown
                    dropdownConfig={dropdownConfig}
                    selectKey={selectKey}
                    onMenuSelect={innerOnSelect}
                    menuOptions={menuOptions}
                />
            );
        }
        return null;
    };

    const getPlaceholder = ()=>{
        if (!menuOptions || !menuOptions.length) return null;
        return menuOptions.find((item: any)=>item.value === selectKey)?.placeholder;
    };

    const getFormLabelHolder = ()=>{
        if (!menuOptions || !menuOptions.length) return '';
        return menuOptions.find((item: any)=>item.value === selectKey)?.label || '';
    };

    return {
        defaultDropDownMenu: isAbroadStyle ? null : getDropdownMenuDom(),
        selectKey,
        updateSelectKey: setSelectKey,
        fieldProps: {
            showLabelInteraction: true,
            placeholder: getPlaceholder(),
            formLabel: getFormLabelHolder(),
            customLabel: getDropdownMenuDom(),
            addonBefore: getAddonBeforeMenuDom()
        },
    };
};
