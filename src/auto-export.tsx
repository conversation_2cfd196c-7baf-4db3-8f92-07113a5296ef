/**
 * ***************************************************
 * *                     警告                         *
 * *            自动生成文件，请勿手动修改！              *
 * ***************************************************
 */

import { dynamic } from '@base-app/runtime-lib';

import type DevicePlaybackShareProps from "../.auto-share-props/runtime-pages/video-service/video-surveillance/playback-device/auto-share-props";
export const DevicePlayback: React.FC<DevicePlaybackShareProps> = dynamic(() => import("./video-service/video-surveillance/playback-device/index"));

import type CarEscortShareProps from "../.auto-share-props/runtime-pages/video-service/video-surveillance/car-escort/auto-share-props";
export const CarEscort: React.FC<CarEscortShareProps> = dynamic(() => import("./video-service/video-surveillance/car-escort/index"));

import type VideoLibraryShareProps from "../.auto-share-props/runtime-pages/video-library/auto-share-props";
export const VideoLibrary: React.FC<VideoLibraryShareProps> = dynamic(() => import("./video-library/index"));

import type VehicleRealtimeVideoModuleShareProps from "../.auto-share-props/runtime-pages/vehicle-realtime-video/auto-share-props";
export const VehicleRealtimeVideoModule: React.FC<VehicleRealtimeVideoModuleShareProps> = dynamic(() => import("./vehicle-realtime-video/index"));

import type UserDetailShareProps from "../.auto-share-props/runtime-pages/user-manage/user-detail/auto-share-props";
export const UserDetail: React.FC<UserDetailShareProps> = dynamic(() => import("./user-manage/user-detail/index"));

import type AddEditUserShareProps from "../.auto-share-props/runtime-pages/user-manage/add-edit-user/auto-share-props";
export const AddEditUser: React.FC<AddEditUserShareProps> = dynamic(() => import("./user-manage/add-edit-user/index"));

import type TrackPlaybackShareProps from "../.auto-share-props/runtime-pages/track-playback/auto-share-props";
export const TrackPlayback: React.FC<TrackPlaybackShareProps> = dynamic(() => import("./track-playback/index"));

import type TenantTemplateDetailShareProps from "../.auto-share-props/runtime-pages/tenant-center/tenant-template/detail/auto-share-props";
export const TenantTemplateDetail: React.FC<TenantTemplateDetailShareProps> = dynamic(() => import("./tenant-center/tenant-template/detail/index"));

import type TenantDetailShareProps from "../.auto-share-props/runtime-pages/tenant-center/tenant-detail/auto-share-props";
export const TenantDetail: React.FC<TenantDetailShareProps> = dynamic(() => import("./tenant-center/tenant-detail/index"));

import type TenantBillShareProps from "../.auto-share-props/runtime-pages/tenant-center/tenant-bill/auto-share-props";
export const TenantBill: React.FC<TenantBillShareProps> = dynamic(() => import("./tenant-center/tenant-bill/index"));

import type SubTenantManageShareProps from "../.auto-share-props/runtime-pages/tenant-center/sub-tenant-manage/auto-share-props";
export const SubTenantManage: React.FC<SubTenantManageShareProps> = dynamic(() => import("./tenant-center/sub-tenant-manage/index"));

import type SubTenantDetailShareProps from "../.auto-share-props/runtime-pages/tenant-center/sub-tenant-detail/auto-share-props";
export const SubTenantDetail: React.FC<SubTenantDetailShareProps> = dynamic(() => import("./tenant-center/sub-tenant-detail/index"));

import type SubAddEditShareProps from "../.auto-share-props/runtime-pages/tenant-center/sub-add-edit/auto-share-props";
export const SubAddEdit: React.FC<SubAddEditShareProps> = dynamic(() => import("./tenant-center/sub-add-edit/index"));

import type TenantCenterSubAddShareProps from "../.auto-share-props/runtime-pages/tenant-center/sub-add/auto-share-props";
export const TenantCenterSubAdd: React.FC<TenantCenterSubAddShareProps> = dynamic(() => import("./tenant-center/sub-add/index"));

import type IssueTextNotStartDetailShareProps from "../.auto-share-props/runtime-pages/task-center/issue-text/taskInform/auto-share-props";
export const IssueTextNotStartDetail: React.FC<IssueTextNotStartDetailShareProps> = dynamic(() => import("./task-center/issue-text/taskInform/notstart"));

import type IssueTextDoingDetailShareProps from "../.auto-share-props/runtime-pages/task-center/issue-text/taskInform/auto-share-props";
export const IssueTextDoingDetail: React.FC<IssueTextDoingDetailShareProps> = dynamic(() => import("./task-center/issue-text/taskInform/doing"));

import type IssueTextDoneDetailShareProps from "../.auto-share-props/runtime-pages/task-center/issue-text/taskInform/auto-share-props";
export const IssueTextDoneDetail: React.FC<IssueTextDoneDetailShareProps> = dynamic(() => import("./task-center/issue-text/taskInform/detail"));

import type VideoWallShareProps from "../.auto-share-props/runtime-pages/system/video-wall/auto-share-props";
import type { VideoWallShareProps as VideoWallVideoWallShareProps } from "./system/video-wall/index";
export const VideoWall: React.FC<VideoWallShareProps & VideoWallVideoWallShareProps> = dynamic(() => import("./system/video-wall/index"));

import type VideoRegularDownloadDetailShareProps from "../.auto-share-props/runtime-pages/system/video-regular-download/detail/auto-share-props";
import type { VideoRegularDownloadDetailShareProps as VideoRegularDownloadDetailVideoRegularDownloadDetailShareProps } from "./system/video-regular-download/detail/index";
export const VideoRegularDownloadDetail: React.FC<VideoRegularDownloadDetailShareProps & VideoRegularDownloadDetailVideoRegularDownloadDetailShareProps> = dynamic(() => import("./system/video-regular-download/detail/index"));

import type VideoRegularDownloadAddShareProps from "../.auto-share-props/runtime-pages/system/video-regular-download/add/auto-share-props";
import type { VideoRegularDownloadAddShareProps as VideoRegularDownloadAddVideoRegularDownloadAddShareProps } from "./system/video-regular-download/add/index";
export const VideoRegularDownloadAdd: React.FC<VideoRegularDownloadAddShareProps & VideoRegularDownloadAddVideoRegularDownloadAddShareProps> = dynamic(() => import("./system/video-regular-download/add/index"));

import type UnknownDriverAlarmShareProps from "../.auto-share-props/runtime-pages/system/unknown-driver-alarm/auto-share-props";
export const UnknownDriverAlarm: React.FC<UnknownDriverAlarmShareProps> = dynamic(() => import("./system/unknown-driver-alarm/index"));

import type FaceContrastRecordShareProps from "../.auto-share-props/runtime-pages/system/face-contrast-record/auto-share-props";
export const FaceContrastRecord: React.FC<FaceContrastRecordShareProps> = dynamic(() => import("./system/face-contrast-record/index"));

import type DataClearShareProps from "../.auto-share-props/runtime-pages/system/data-clear/auto-share-props";
import type { DataClearShareProps as DataClearDataClearShareProps } from "./system/data-clear/index";
export const DataClear: React.FC<DataClearShareProps & DataClearDataClearShareProps> = dynamic(() => import("./system/data-clear/index"));

import type UserPolicyShareProps from "../.auto-share-props/runtime-pages/strategy-center/user-policy/auto-share-props";
export const UserPolicy: React.FC<UserPolicyShareProps> = dynamic(() => import("./strategy-center/user-policy/index"));

import type DefaultUserSettingShareProps from "../.auto-share-props/runtime-pages/strategy-center/user-policy/user-setting/auto-share-props";
export const DefaultUserSetting: React.FC<DefaultUserSettingShareProps> = dynamic(() => import("./strategy-center/user-policy/user-setting/index"));

import type UserPolicyEditShareProps from "../.auto-share-props/runtime-pages/strategy-center/user-policy/edit/auto-share-props";
export const UserPolicyEdit: React.FC<UserPolicyEditShareProps> = dynamic(() => import("./strategy-center/user-policy/edit/index"));

import type UserPolicyDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/user-policy/detail/auto-share-props";
export const UserPolicyDetail: React.FC<UserPolicyDetailShareProps> = dynamic(() => import("./strategy-center/user-policy/detail/index"));

import type UserPolicyAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/user-policy/add/auto-share-props";
export const UserPolicyAdd: React.FC<UserPolicyAddShareProps> = dynamic(() => import("./strategy-center/user-policy/add/index"));

import type PreciseControlDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/precise-control/detail/auto-share-props";
export const PreciseControlDetail: React.FC<PreciseControlDetailShareProps> = dynamic(() => import("./strategy-center/precise-control/detail/index"));

import type PreciseControlAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/precise-control/add/auto-share-props";
export const PreciseControlAdd: React.FC<PreciseControlAddShareProps> = dynamic(() => import("./strategy-center/precise-control/add/index"));

import type PictureCaptureShareProps from "../.auto-share-props/runtime-pages/strategy-center/picture-capture/auto-share-props";
export const PictureCapture: React.FC<PictureCaptureShareProps> = dynamic(() => import("./strategy-center/picture-capture/index"));

import type PictureCaptureDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/picture-capture/detail/auto-share-props";
export const PictureCaptureDetail: React.FC<PictureCaptureDetailShareProps> = dynamic(() => import("./strategy-center/picture-capture/detail/index"));

import type PictureCaptureAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/picture-capture/add/auto-share-props";
export const PictureCaptureAdd: React.FC<PictureCaptureAddShareProps> = dynamic(() => import("./strategy-center/picture-capture/add/index"));

import type FaceContrastShareProps from "../.auto-share-props/runtime-pages/strategy-center/face-contrast/auto-share-props";
export const FaceContrast: React.FC<FaceContrastShareProps> = dynamic(() => import("./strategy-center/face-contrast/index"));

import type FaceContrastEditShareProps from "../.auto-share-props/runtime-pages/strategy-center/face-contrast/edit/auto-share-props";
export const FaceContrastEdit: React.FC<FaceContrastEditShareProps> = dynamic(() => import("./strategy-center/face-contrast/edit/index"));

import type FaceContrastDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/face-contrast/detail/auto-share-props";
export const FaceContrastDetail: React.FC<FaceContrastDetailShareProps> = dynamic(() => import("./strategy-center/face-contrast/detail/index"));

import type FaceContrastAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/face-contrast/add/auto-share-props";
export const FaceContrastAdd: React.FC<FaceContrastAddShareProps> = dynamic(() => import("./strategy-center/face-contrast/add/index"));

import type EvidenceUploadSettingsShareProps from "../.auto-share-props/runtime-pages/strategy-center/evidence/auto-share-props";
export const EvidenceUploadSettings: React.FC<EvidenceUploadSettingsShareProps> = dynamic(() => import("./strategy-center/evidence/index"));

import type EvidenceUploadDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/evidence/detail/auto-share-props";
export const EvidenceUploadDetail: React.FC<EvidenceUploadDetailShareProps> = dynamic(() => import("./strategy-center/evidence/detail/index"));

import type EvidenceUploadAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/evidence/add/auto-share-props";
export const EvidenceUploadAdd: React.FC<EvidenceUploadAddShareProps> = dynamic(() => import("./strategy-center/evidence/add/index"));

import type EmailSendingShareProps from "../.auto-share-props/runtime-pages/strategy-center/email-sending/auto-share-props";
export const EmailSending: React.FC<EmailSendingShareProps> = dynamic(() => import("./strategy-center/email-sending/index"));

import type EmailSendingDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/email-sending/detail/auto-share-props";
export const EmailSendingDetail: React.FC<EmailSendingDetailShareProps> = dynamic(() => import("./strategy-center/email-sending/detail/index"));

import type EmailSendingAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/email-sending/add/auto-share-props";
export const EmailSendingAdd: React.FC<EmailSendingAddShareProps> = dynamic(() => import("./strategy-center/email-sending/add/index"));

import type DefaultUserSettingsShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/user-setting/auto-share-props";
export const DefaultUserSettings: React.FC<DefaultUserSettingsShareProps> = dynamic(() => import("./strategy-center/default/detail-page/user-setting/index"));

import type DefaultSoundShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/sound/auto-share-props";
export const DefaultSound: React.FC<DefaultSoundShareProps> = dynamic(() => import("./strategy-center/default/detail-page/sound/index"));

import type DefaultPictureCaptureShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/picture-capture/auto-share-props";
export const DefaultPictureCapture: React.FC<DefaultPictureCaptureShareProps> = dynamic(() => import("./strategy-center/default/detail-page/picture-capture/index"));

import type DefaultMailSendShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/mail-send/auto-share-props";
export const DefaultMailSend: React.FC<DefaultMailSendShareProps> = dynamic(() => import("./strategy-center/default/detail-page/mail-send/index"));

import type DefaultLinkAlarmShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/linkage/auto-share-props";
export const DefaultLinkAlarm: React.FC<DefaultLinkAlarmShareProps> = dynamic(() => import("./strategy-center/default/detail-page/linkage/index"));

import type DefaultFaceContrastShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/face-contrast/auto-share-props";
export const DefaultFaceContrast: React.FC<DefaultFaceContrastShareProps> = dynamic(() => import("./strategy-center/default/detail-page/face-contrast/index"));

import type DefaultEvidenceUploadShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/evidence/auto-share-props";
export const DefaultEvidenceUpload: React.FC<DefaultEvidenceUploadShareProps> = dynamic(() => import("./strategy-center/default/detail-page/evidence/index"));

import type DefaultChannelSettingShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/channel-setting/auto-share-props";
export const DefaultChannelSetting: React.FC<DefaultChannelSettingShareProps> = dynamic(() => import("./strategy-center/default/detail-page/channel-setting/index"));

import type DefaultAutoHandleShareProps from "../.auto-share-props/runtime-pages/strategy-center/default/detail-page/auto-handle/auto-share-props";
export const DefaultAutoHandle: React.FC<DefaultAutoHandleShareProps> = dynamic(() => import("./strategy-center/default/detail-page/auto-handle/index"));

import type AutoHandleShareProps from "../.auto-share-props/runtime-pages/strategy-center/auto-handle/auto-share-props";
export const AutoHandle: React.FC<AutoHandleShareProps> = dynamic(() => import("./strategy-center/auto-handle/index"));

import type AutoHandleDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/auto-handle/detail/auto-share-props";
export const AutoHandleDetail: React.FC<AutoHandleDetailShareProps> = dynamic(() => import("./strategy-center/auto-handle/detail/index"));

import type AutoHandleAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/auto-handle/add/auto-share-props";
export const AutoHandleAdd: React.FC<AutoHandleAddShareProps> = dynamic(() => import("./strategy-center/auto-handle/add/index"));

import type AlarmLinkageShareProps from "../.auto-share-props/runtime-pages/strategy-center/alarm-linkage/auto-share-props";
export const AlarmLinkage: React.FC<AlarmLinkageShareProps> = dynamic(() => import("./strategy-center/alarm-linkage/index"));

import type AlarmLinkageDetailShareProps from "../.auto-share-props/runtime-pages/strategy-center/alarm-linkage/detail/auto-share-props";
export const AlarmLinkageDetail: React.FC<AlarmLinkageDetailShareProps> = dynamic(() => import("./strategy-center/alarm-linkage/detail/index"));

import type AlarmLinkageAddShareProps from "../.auto-share-props/runtime-pages/strategy-center/alarm-linkage/add/auto-share-props";
export const AlarmLinkageAdd: React.FC<AlarmLinkageAddShareProps> = dynamic(() => import("./strategy-center/alarm-linkage/add/index"));

import type RoleManageShareProps from "../.auto-share-props/runtime-pages/permission-center/role-manage/auto-share-props";
export const RoleManage: React.FC<RoleManageShareProps> = dynamic(() => import("./permission-center/role-manage/index"));

import type RoleDetailShareProps from "../.auto-share-props/runtime-pages/permission-center/role-manage/role-detail/auto-share-props";
export const RoleDetail: React.FC<RoleDetailShareProps> = dynamic(() => import("./permission-center/role-manage/role-detail/index"));

import type RoleAddEditShareProps from "../.auto-share-props/runtime-pages/permission-center/role-manage/role-add-edit/auto-share-props";
export const RoleAddEdit: React.FC<RoleAddEditShareProps> = dynamic(() => import("./permission-center/role-manage/role-add-edit/index"));

import type RoleAuthorizeShareProps from "../.auto-share-props/runtime-pages/permission-center/role-authorize/auto-share-props";
export const RoleAuthorize: React.FC<RoleAuthorizeShareProps> = dynamic(() => import("./permission-center/role-authorize/index"));

import type RiskTaskShareProps from "../.auto-share-props/runtime-pages/monitoring-center/risk-task/auto-share-props";
export const RiskTask: React.FC<RiskTaskShareProps> = dynamic(() => import("./monitoring-center/risk-task/index"));

import type RealtimeVideoShareProps from "../.auto-share-props/runtime-pages/monitoring-center/realtime-video/auto-share-props";
export const RealtimeVideo: React.FC<RealtimeVideoShareProps> = dynamic(() => import("./monitoring-center/realtime-video/index"));

import type RootPageShareProps from "../.auto-share-props/runtime-pages/monitoring-center/realtime-monitoring-new/auto-share-props";
export const RootPage: React.FC<RootPageShareProps> = dynamic(() => import("./monitoring-center/realtime-monitoring-new/RMPageContainer"));

import type RealtimeMonitoringCacheShareProps from "../.auto-share-props/runtime-pages/monitoring-center/realtime-monitoring/auto-share-props";
export const RealtimeMonitoringCache: React.FC<RealtimeMonitoringCacheShareProps> = dynamic(() => import("./monitoring-center/realtime-monitoring/index"));

import type LicenseBindQueryShareProps from "../.auto-share-props/runtime-pages/license-bind-query/auto-share-props";
export const LicenseBindQuery: React.FC<LicenseBindQueryShareProps> = dynamic(() => import("./license-bind-query/index"));

import type IntercomModuleShareProps from "../.auto-share-props/runtime-pages/intercom/auto-share-props";
export const IntercomModule: React.FC<IntercomModuleShareProps> = dynamic(() => import("./intercom/index"));

import type GroupManageShareProps from "../.auto-share-props/runtime-pages/group-manage/auto-share-props";
export const GroupManage: React.FC<GroupManageShareProps> = dynamic(() => import("./group-manage/index"));

import type AddSecretKeyShareProps from "../.auto-share-props/runtime-pages/general-settings/white-list-key/add/auto-share-props";
export const AddSecretKey: React.FC<AddSecretKeyShareProps> = dynamic(() => import("./general-settings/white-list-key/add/index"));

import type SendMailSettingQueryShareProps from "../.auto-share-props/runtime-pages/general-settings/sendmail-setting-query/auto-share-props";
export const SendMailSettingQuery: React.FC<SendMailSettingQueryShareProps> = dynamic(() => import("./general-settings/sendmail-setting-query/index"));

import type EditMessageTempCompShareProps from "../.auto-share-props/runtime-pages/general-settings/platform-message/components/MessageTemplate/edit-template-info/auto-share-props";
import type { EditMessageTempCompShareProps as EditMessageTempCompEditMessageTempCompShareProps } from "./general-settings/platform-message/components/MessageTemplate/edit-template-info/index";
export const EditMessageTempComp: React.FC<EditMessageTempCompShareProps & EditMessageTempCompEditMessageTempCompShareProps> = dynamic(() => import("./general-settings/platform-message/components/MessageTemplate/edit-template-info/index"));

import type MessageTemplateShareProps from "../.auto-share-props/runtime-pages/general-settings/platform-message/components/MessageTemplate/detail/auto-share-props";
import type { MessageTemplateShareProps as MessageTemplateMessageTemplateShareProps } from "./general-settings/platform-message/components/MessageTemplate/detail/index";
export const MessageTemplate: React.FC<MessageTemplateShareProps & MessageTemplateMessageTemplateShareProps> = dynamic(() => import("./general-settings/platform-message/components/MessageTemplate/detail/index"));

import type MultipleLanguagesShareProps from "../.auto-share-props/runtime-pages/general-settings/multiple-languages/auto-share-props";
export const MultipleLanguages: React.FC<MultipleLanguagesShareProps> = dynamic(() => import("./general-settings/multiple-languages/index"));

import type MailTemplateShareProps from "../.auto-share-props/runtime-pages/general-settings/mail-template/auto-share-props";
export const MailTemplate: React.FC<MailTemplateShareProps> = dynamic(() => import("./general-settings/mail-template/index"));

import type TemplateDetailShareProps from "../.auto-share-props/runtime-pages/general-settings/mail-template/detail/auto-share-props";
import type { TemplateDetailShareProps as TemplateDetailTemplateDetailShareProps } from "./general-settings/mail-template/detail/index";
export const TemplateDetail: React.FC<TemplateDetailShareProps & TemplateDetailTemplateDetailShareProps> = dynamic(() => import("./general-settings/mail-template/detail/index"));

import type AddMailTemplateShareProps from "../.auto-share-props/runtime-pages/general-settings/mail-template/add-mail-template/auto-share-props";
export const AddMailTemplate: React.FC<AddMailTemplateShareProps> = dynamic(() => import("./general-settings/mail-template/add-mail-template/index"));

import type AlarmSettingShareProps from "../.auto-share-props/runtime-pages/general-settings/alarm-settings/auto-share-props";
export const AlarmSetting: React.FC<AlarmSettingShareProps> = dynamic(() => import("./general-settings/alarm-settings/index"));

import type ChannelSettingQueryShareProps from "../.auto-share-props/runtime-pages/general-settings/CH-set-query/auto-share-props";
export const ChannelSettingQuery: React.FC<ChannelSettingQueryShareProps> = dynamic(() => import("./general-settings/CH-set-query/index"));

import type TimeFenceListShareProps from "../.auto-share-props/runtime-pages/fence/time/auto-share-props";
export const TimeFenceList: React.FC<TimeFenceListShareProps> = dynamic(() => import("./fence/time/index"));

import type TimeFenceDetailShareProps from "../.auto-share-props/runtime-pages/fence/time/auto-share-props";
export const TimeFenceDetail: React.FC<TimeFenceDetailShareProps> = dynamic(() => import("./fence/time/detail"));

import type TimeActionShareProps from "../.auto-share-props/runtime-pages/fence/time/auto-share-props";
export const TimeAction: React.FC<TimeActionShareProps> = dynamic(() => import("./fence/time/action"));

import type SpeedFenceListShareProps from "../.auto-share-props/runtime-pages/fence/speed/auto-share-props";
export const SpeedFenceList: React.FC<SpeedFenceListShareProps> = dynamic(() => import("./fence/speed/index"));

import type SpeedFenceDetailShareProps from "../.auto-share-props/runtime-pages/fence/speed/auto-share-props";
export const SpeedFenceDetail: React.FC<SpeedFenceDetailShareProps> = dynamic(() => import("./fence/speed/detail"));

import type SpeedActionShareProps from "../.auto-share-props/runtime-pages/fence/speed/auto-share-props";
export const SpeedAction: React.FC<SpeedActionShareProps> = dynamic(() => import("./fence/speed/action"));

import type BaseFenceListShareProps from "../.auto-share-props/runtime-pages/fence/base/auto-share-props";
export const BaseFenceList: React.FC<BaseFenceListShareProps> = dynamic(() => import("./fence/base/index"));

import type BaseFenceDetailShareProps from "../.auto-share-props/runtime-pages/fence/base/auto-share-props";
export const BaseFenceDetail: React.FC<BaseFenceDetailShareProps> = dynamic(() => import("./fence/base/detail"));

import type BaseActionShareProps from "../.auto-share-props/runtime-pages/fence/base/auto-share-props";
export const BaseAction: React.FC<BaseActionShareProps> = dynamic(() => import("./fence/base/action"));

import type AreaListShareProps from "../.auto-share-props/runtime-pages/fence/area/auto-share-props";
export const AreaList: React.FC<AreaListShareProps> = dynamic(() => import("./fence/area/index"));

import type EvidenceListPageShareProps from "../.auto-share-props/runtime-pages/evidence-list/auto-share-props";
export const EvidenceListPage: React.FC<EvidenceListPageShareProps> = dynamic(() => import("./evidence-list/index"));

import type EvidenceDetailShareProps from "../.auto-share-props/runtime-pages/evidence-list/detail/auto-share-props";
export const EvidenceDetail: React.FC<EvidenceDetailShareProps> = dynamic(() => import("./evidence-list/detail/index"));

import type ApplicationManageShareProps from "../.auto-share-props/runtime-pages/application-manage/auto-share-props";
import type { ApplicationManageShareProps as ApplicationManageApplicationManageShareProps } from "./application-manage/index";
export const ApplicationManage: React.FC<ApplicationManageShareProps & ApplicationManageApplicationManageShareProps> = dynamic(() => import("./application-manage/index"));

import type AddApplicationShareProps from "../.auto-share-props/runtime-pages/application-manage/application-detail/auto-share-props";
import type { ApplicationDetailShareProps as AddApplicationApplicationDetailShareProps } from "./application-manage/application-detail/index";
export const AddApplication: React.FC<AddApplicationShareProps & AddApplicationApplicationDetailShareProps> = dynamic(() => import("./application-manage/application-detail/index"));

import type LinkSettingShareProps from "../.auto-share-props/runtime-pages/alarm-setting-menu/link-setting/auto-share-props";
export const LinkSetting: React.FC<LinkSettingShareProps> = dynamic(() => import("./alarm-setting-menu/link-setting/index"));

import type AlarmSettingQueryShareProps from "../.auto-share-props/runtime-pages/alarm-setting-menu/alarm-setting-query/auto-share-props";
export const AlarmSettingQuery: React.FC<AlarmSettingQueryShareProps> = dynamic(() => import("./alarm-setting-menu/alarm-setting-query/index"));

import type AlarmListPageShareProps from "../.auto-share-props/runtime-pages/alarm-center/alarm-list/auto-share-props";
export const AlarmListPage: React.FC<AlarmListPageShareProps> = dynamic(() => import("./alarm-center/alarm-list/index"));

import type AlarmDetailShareProps from "../.auto-share-props/runtime-pages/alarm-center/alarm-list/alarm-detail/auto-share-props";
export const AlarmDetail: React.FC<AlarmDetailShareProps> = dynamic(() => import("./alarm-center/alarm-list/alarm-detail/index"));

import type VehicleTypeManageShareProps from "../.auto-share-props/runtime-pages/general-settings/vehicle-type-manage/auto-share-props";
export const VehicleTypeManage: React.FC<VehicleTypeManageShareProps> = dynamic(() => import("./general-settings/vehicle-type-manage/index"));