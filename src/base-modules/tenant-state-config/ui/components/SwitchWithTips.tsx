import React, {ReactNode} from "react";
import {Space, Switch, Tooltip} from "@streamax/poppy";
import {SwitchProps} from "antd/lib/switch";
import {IconInformationFill} from "@streamax/poppy-icons";
import {StarryAbroadIcon} from "@/runtime-lib";
import './index.scope.less';

interface ISwitchWithTipsProps extends SwitchProps{
    tooltip?: string | ReactNode;
}
export default  (props: ISwitchWithTipsProps) => {
    const {tooltip, ...rest} = props;
    return (
        <div className='switch-with-tips'>
            <Switch {...rest}/>
            {tooltip &&
                <Tooltip
                    title={tooltip}
                    placement="right"
                >
                    <span>
                        <StarryAbroadIcon>
                            <IconInformationFill
                                className="tips-icon-information"
                            />
                        </StarryAbroadIcon>
                    </span>
                </Tooltip>
            }
        </div>
    );
};
