## 复用框架页面迁移指南

> 此文档主要用于说明如何进行页面迁移，以及一些开发规范说明



#### 项目目录说明

```shell
.
├── README.md
├── example                          # 示例目录(不需要关注)
│   ├── app.tsx
│   ├── config.properties
│   ├── package.json
│   ├── pages
│   │   ├── index.scoped.less
│   │   └── index.tsx
│   ├── runtime-loader.json
│   ├── tsconfig.json
│   └── typing.d.ts
├── package.json
├── src                              # 项目代码目录
│   ├── hooks                        # 公共hooks(不需要关注)
│   │   ├── index.tsx
│   │   └── useInit.tsx
│   ├── index.ts                     # 入口文件(路由映射文件，需要关注)
│   ├── pages                        # 页面代码目录(需要关注)
│   │   └── example
│   │       ├── index.tsx
│   │       └── model.ts
│   └── service                      # service请求代码(需要关注)
│       └── example.ts
├── tsconfig.json
└── yarn.lock
```

如上所述，在开发页面时，需要关注的文件主要有`src/index.ts`、`src/pages`、`src/service`。

`src/index.ts`:  这个文件里面主要是申明路由与页面的映射关系，添加页面后，需要在文件里的`routes`数组里面加上新增的页面路由。

`src/pages`：这个文件夹中主要存放的是页面的代码，每个页面自行组织一个文件夹，页面文件夹的命名规则为**全小写，较长时采用横线划分**，例如`user-manage`。

`src/service`： 这个文件夹主要存放的是接口请求的代码，以后的代码中，**接口请求都统一到service文件中**，不再把接口请求分散到各个业务代码中，方便集中管理。同时规约**采用模块划分组织文件**，例如`user`模块、`vehicle`模块等。



#### 开发规范约定

此节约定一些开发时需要注意的事项，建议在开发时遵从下列中的约定。

- 页面文件夹命名采用全小写、横线划分
- service文件夹下文件采用模块进行组织
- icon图标采用全路径方式引入
- 全局变量采用`getAppGlobalData`方法获取



#### 开发环境启动

1. 首先从仓库`runtime-starry-pages`中将项目代码拉取到本地

```shell
git clone http://**************/streamax-base/web/runtime-starry-pages.git
```

2. 执行`yarn install`或者`npm install`安装依赖

```shell
yarn install
# or
npm install
```

3. 配置本地hosts文件，增加配置`************** static.dev.streamax.com`
4. 依赖安装完成并hosts文件修改完成后执行`yarn start`或者`npm run start`启动项目

```shell
yarn start
# or
npm run start
```

5. 项目启动后，浏览器打开对应的端口，即可看见目前存在的页面路由，点击对应的路由即可跳转到对应的页面中，然后可以修改页面代码查看效果



#### 发布到测试环境

开发完成后，需要部署到测试环境供其他同事使用或调试时，可执行下列命令：

```shell
yarn pub:dev
# or
npm run pub:dev
```



#### 发布到正式环境

开发完成后，需要部署到正式环境时，可执行下列命令：

```shell
yarn pub
# or
npm run pub
```



#### 开发说明

此节将简要的说明在开发时，一些公共库的引入方式以及使用方式。

##### i18n工具

需要使用`i18n`工具时，只需要从`@base-app/runtime-lib`中引入对应的包即可，使用方式与之前相同。

```react
import { i18n } from '@base-app/runtime-lib';

export default Example = () => {
  return (
  	<div>{i18n.t('starry.common.btn.ok', '确定')}</div>
  );
};
```

##### 词条提取与合并
提取：项目运行 npm run build:dist 后。会在根目录生成 i18n 文件夹。里面包含当前项目词条（记得删除缓存文件 runtime-starry-pages/node_modules/.cache）。其他项目类型

合并： 将所有项目的词条json文件合并到一个json文件后。放入文件夹 ```runtime-starry-pages\resources\i18n\need-translate-doc``` 下。将上一个版本的已翻译文件放入文件夹```runtime-starry-pages\resources\i18n\translated-doc```下。然后 运行 ``` npm run i18n-merge ```后。会将新增的词条输出在目录。请将这个文件给到版本负责人。


##### request工具

`request`工具即之前的`axios`，该包需要从`@base-app/runtime-lib`中引入；使用方式存在变化，不再提供`get`、`post`等方法，统一采用配置的`method`来指定请求方式，以及不再统一指定`baseURL`，每个请求中需要自己指定`baseURL`，如下示例：

```typescript
import { request } from '@base-app/runtime-lib';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];

export const getUserList = async (params) => {
	const { message, code, success, data } = await request({
    method: 'get',
    url: '/base-server-service/api/v1/user/page'
    baseURL,
    params
  });
  if (!success || code !== 1000) {
    // todo
    throw message;
  }
  return data;
};
```

##### model使用

当页面中需要使用`model`时，由于`@streamax/runtime-starry-pages`项目并不会自动注册`model`，所以需要手动注册。

1. 先申明一个`model`文件，即在对应的页面目录下创建一个`model.ts`文件，文件中的写法与之前的写法相同，需要导出一个对象，对象有`namespace`、`state`、`effects`和`reducers`等几个属性；
2. 在页面渲染之前注册该`model`，如果是函数组件，则可以使用`hooks`目录下的`useInit`，在这个hooks中进行注册，如果是类组件，则可以在`constructor`的时候注册。具体可参考如下代码：

```react
import { getApp } from '@streamax/runtime-dva';  // 引入getApp方法
import { useInit } from '../../hooks';  // 引入useInit钩子，该钩子会在渲染之前执行，且只执行一次
import ExampleModel from './model';  // 引入model

const Example = () => {
  // 注册model
  useInit(() => {
    const app = getApp();
    if (app) {
      app.model(ExampleModel);
    }
  });
};
```

注册完成后，使用的时候与之前的方式保持一致，可以使用`useDispatch`和`useSelector`，这两个包都需要从`@streamax/runtime-dva`中引入，例如：

```react
import { useEffect } from 'react';
import { Dispatch, useDispatch, useSelector } from '@streamax/runtime-dva';
// ...

const Example = () => {
  const dispatch: Dispatch = useDispatch();
  const { userList } = useSelector(
  	(state: any) => ({
      userList: state.exampleModel.userList
    })
  );
  
  useEffect(() => {
    dispatch({
      type: 'exampleModel/fetchUserList',
      payload: {
        page: 1,
        pageSize: 20
      }
    });
  }, []);
  
  // ...
};
```

##### 公共组件使用

之前项目的公共组件已经全部移植到`@base-app/runtime-lib`中，需要使用对应的组件时，需要从这个包里面引入。目前有的组件有`BreadcrumbLayout`、`Cardlayout`、`Descriptions`、`DescriptionsCard`、`MuliteTreeSelect`和`ReportTable`。

使用方式同之前的项目一致：

```react
import { ReportTable } from '@base-app/runtime-lib';
```

##### icon图标使用

之前的图标都是直接导入的`svg`图片，现在部分图标已经统一规划到`@streamax/poppy-icons`包中，所以使用对应的图标时，可以直接从`@streamax/poppy-icons`中引入，目前收录的图标可以在[这儿](http://**************:8848/components-common/icon)查看。使用方式如下示例：

```react
// 推荐引入方式
import IconIp from '@streamax/poppy-icons/lib/icons/IconIp';
// 不推荐引入方式(目前暂无插件处理，所以会导致打包的时候引入全部的图标)
// import { IconIp } from '@streamax/poppy-icons';

const Example = () => {
  return <IconIp />;
};
```

如果需要使用`antd`的图标或者是`Icon`组件，也是直接从`@streamax/poppy-icons`库中引入

```react
// 引入Icon组件
import Icon from '@ant-design/icons';
// 引入antd的图标
import { AccountBookOutlined } from '@ant-design/icons';
```

##### 全局下参数获取

之前的一些项目配置信息、用户配置信息以及资源信息等都是直接挂载在`window`对象下的，现在使用方式存在变更。`window`对象下主要挂载了`APP_CONFIG`和`APP_GLOBAL_DATA`这两个对象

`APP_CONFIG`:  项目配置信息，如网关地址、登陆地址等

`APP_GLOBAL_DATA`:  一些其他的全局信息，比如`APP_ID`、资源、用户信息、用户配置等，但是不推荐直接使用`window.APP_GLOBAL_DATA['APP_ID']`的方式获取相关的数据，在`@base-app/runtime-lib`中提供了`getAppGlobalData`方法用于获取相关的数据，建议使用此方法获取，使用示例如下：

```react
import { getAppGlobalData } from '@base-app/runtime-lib';

const Example = () => {
    const resource = getAppGlobalData('APP_RESOURCE') || {};
    const menus = resource['menus'] || [];
    const pages = resource['pages'] || [];
  	const APPID = getAppGlobalData('APP_ID');
  
  	// ...
};
```

##### utils工具函数使用

utils工具包现在是放在`@base-app/runtime-lib`中的，主要有以下几类工具函数：

`constant`：主要包含时区、地图类型、日期格式、速度单位、里程单位、温度单位和语言等常量枚举值；

`general`：主要包含数组转树结构等方法；

`formator`：主要包含时区转换、文件大小单位转换等方法；

`validator`：主要包含密码、非法字符、数字字母等校验规则；

`pattern`：主要包含一些正则表达式。

```javascript
import { utils } from '@base-app/runtime-lib';

// example
utils.constant.TIME_ZONE_OPTIONS
utils.general.arrayToTree()
utils.formator.zeroTimeStampToFormatTime()
utils.validator.password
utils.pattern.email
```

##### emmiter使用

项目内置了`emmiter`，如果需要使用可以直接从`@base-app/runtime-lib`中导入`g_emmiter`即可。

```javascript
import { g_emmiter } from '@base-app/runtime-lib';

const handler = () => {
  // todo
};

// 订阅
const token = g_emmiter.on('example.emmiter.key', handler);

// 取消订阅
g_emmiter.off('example.emmiter.key', token)

// 触发
g_emmiter.emit('example.emmiter.key');
```

在点击页面顶部的修改密码、退出登陆等出现模态框时，内置会触发`global:modal-opened`和`global:modal-closed`消息，如果有场景(视频控件页面，弹出模态框时需要隐藏控件，取消模态框后需要显示控件)需要使用

时，可以直接订阅这两个消息并做相应的处理。

## AI的运用

当前项目使用cline和roo code 工具增强对项目的理解和开发

### cline
cline的规则已经建立，cline的规则入口.clinerules文件夹查看，可以自定义添加规则，当前已经支持memory-bank并且已经初始化完成，其他人员无需初始化。

* 需要更新memory-bank的功能需要执行更新命令
```
update memory bank
```
参考地址：https://docs.cline.bot/improving-your-prompting-skills/cline-memory-bank#getting-started-with-memory-bank

### roo-code
roo-code已经添加支持memory-bank，无需初始化，加载插件会自动初始化

## mcp服务
当前项目已经接入yapi生成代码，配置如下：对于使用roo code 支持项目mcp配置，无需自己配置，之需要修改/Users/<USER>/project/company/streamax/saas-web/.roo/mcp.json里面的用户名称和密码。对于cline用户拷贝下面配置到mcp配置文件即可
```
 "yapi-server": {
      "disabled": false,
      "timeout": 60,
      "autoStart": true,
      "alwaysAllow": [],
      "command": "npx",
      "args": [
       "-y",
        "@streamax/yapi-mcp-server"
      ],
      "env": {
        "YAPI_BASE_URL": "http://192.168.150.124:3000",
        "YAPI_USERNAME":"username",
        "YAPI_PASSWORD": "password"
      },
      "transportType": "stdio"
    }
```

使用方法：命令行输入我要添加business项目，tag为V2.16.6的接口
参考地址：http://192.168.150.83:4873/-/web/detail/@streamax/yapi-mcp-server
