const ExtendSyntaxLessPlugin = require('@streamax/less-plugin-syntax-extend');
const LessFunctionOverrideLessPlugin = require('@streamax/less-plugin-dynamic-variable');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MaterialPlugin = require('@streamax/material/webpack-plugin');

const injectAlias = (config) => {
    config.resolve.alias.set('@', `/src`);
};

const externalResponsiveLayout = (config) => {
    const externals = config.toConfig().externals || [];
    externals.push({
        '@streamax/responsive-layout': {
            commonjs: '@streamax/responsive-layout',
            commonjs2: '@streamax/responsive-layout',
            amd: ['@base-app/runtime-combine', '__RESPONSIVE_LAYOUT__'],
            root: ['@base-app/runtime-combine', '__RESPONSIVE_LAYOUT__'],
        },
    });
    config.externals(externals);
};

module.exports = {
    extractCSS: true,
    extractI18n: {},
    extraBabelPlugins: [
        ['@streamax/babel-plugin-add-sharing-props'],
        [require('@streamax/material/babel-plugin')],
    ],
    lessExtend: {
        lessFunctionSupportCssVariable: true,
        lessPseudoElementSyntaxExtend: true,
    },
    chainWebpack: (config) => {
        injectAlias(config);
        config.plugin('copy').use(CopyWebpackPlugin, [
            {
                patterns: [
                    {
                        from: `${process.cwd()}/node_modules/@streamax/streamap-js/dist`,
                        to: `${process.cwd()}/dist`,
                        globOptions: {
                            ignore: ['**/demo', '**/doc'],
                        },
                    },
                    {
                        from: `${process.cwd()}/extend.conf.tpl`,
                        to: `${process.cwd()}/dist`,
                    },
                ],
            },
        ]);
        config.module
            .rule('less')
            .use('less-loader')
            .loader('less-loader')
            .options({
                lessOptions: {
                    modifyVars: { 'root-entry-name': 'variable' },
                    javascriptEnabled: true,
                    plugins: [
                        new LessFunctionOverrideLessPlugin(),
                        new ExtendSyntaxLessPlugin(),
                    ],
                },
            });
        config.module
            .rule('worker')
            .test(/\.worker.js$/)
            .use('worker')
            .loader('worker-loader')
            .options({
                inline: 'no-fallback',
            })
            .end();
        config.module
            .rule('pcm')
            .test(/\.(pcm)$/)
            .use('file')
            .loader('file-loader')
            .end();
        config.resolve.fallback = {
            querystring: require.resolve('querystring-es3'),
        };
        // 将@streamax/responsive-layout配置成externals，避免runtime-lib和runtime-pages各自打包
        externalResponsiveLayout(config);
        config.plugin('material-plugin').use(MaterialPlugin);
        return config;
    },
};
