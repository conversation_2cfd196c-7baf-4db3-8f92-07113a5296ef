module.exports = {
    // 当前项目的配置，按规范取runtime-starry-xx-pages中的xx
    namespace: 'base',
    translate: {
        translateEnUS: true,
        resourceNameUseEn: true,
    },
    apps: [
        {
            app: 'BasePlatform',
            appId: '0',
            menuDir: 'resources/menus/base',
            pageDirs: ['resources/pages/base'],
            outputDir: 'resources/output',
            // 上一个版本带翻译的excel，用于对比当前版本资源和提供已有的翻译
            previousVersionPath: 'resources/previous-version/BasePlatform.xlsx',
        },
        {
            app: 'BaseBusiness',
            appId: '1',
            menuDir: 'resources/menus/ft',
            pageDirs: ['resources/pages/ft'],
            outputDir: 'resources/output',
            // 上一个版本带翻译的excel，用于对比当前版本资源和提供已有的翻译
            previousVersionPath: 'resources/previous-version/BaseBusiness.xlsx',
        },
    ],
    replaceDir: 'resources/replace/replace.xlsx',
};
