# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/npm-debug.log*
/yarn-error.log
/package-lock.json
/resource-script/generate-sql.js
/resources/output
/resources/i18n
/resources/previous-version
/resources/replace
/i18n/result

# ide
.vscode
.history
.idea

# mock
mock/shentao.ts

# production
/dist
/.dist-runtime-combine
/.dist-runtime-pages
/install

# misc
.DS_Store

# umi
/src/.umi
/src/.umi-production
/src/.umi-test
/.env.local
.umirc.local.ts
.yalc
yalc.lock
.eslintcache
# sonar
.scannerwork
